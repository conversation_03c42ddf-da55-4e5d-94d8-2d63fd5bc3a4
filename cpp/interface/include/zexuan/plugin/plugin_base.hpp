#pragma once

#include "zexuan/logger.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/base/observer.hpp"
#include "zexuan/base/subject.hpp"
#include "zexuan/base/mediator.hpp"
#include <string>
#include <memory>
#include <functional>
#include <vector>

// 前向声明
namespace zexuan { namespace plugin { class MessageMediator; } }

namespace zexuan {
namespace plugin {

/**
 * 非模板的插件基础接口 - 纯粹基于新的Observer/Subject/Mediator模式
 */
class IPlugin {
public:
    virtual ~IPlugin() = default;

    // 基础插件信息
    virtual const char* getName() const = 0;
    virtual const char* getVersion() const = 0;
    virtual const char* getDescription() const = 0;

    // 生命周期管理
    virtual bool initialize() = 0;
    virtual void shutdown() = 0;

    // 获取插件的Observer/Subject接口
    virtual zexuan::base::Observer* asObserver() = 0;
    virtual zexuan::base::Subject* asSubject() = 0;
};

/**
 * 插件观察者实现 - 纯粹基于BaseObserver
 */
class PluginObserver : public zexuan::base::BaseObserver {
public:
    PluginObserver(int id, const std::string& description, IPlugin* plugin)
        : zexuan::base::BaseObserver(id, description), plugin_(plugin) {}

    void setMessageHandler(std::function<void(const zexuan::base::Message&)> handler) {
        messageHandler_ = std::move(handler);
    }

    void setEventHandler(std::function<void(const zexuan::base::EventMessage&)> handler) {
        eventHandler_ = std::move(handler);
    }

protected:
    void onNotify(zexuan::base::Subject* subject, const zexuan::base::Message& message) override {
        if (messageHandler_) {
            messageHandler_(message);
        }
    }

    void onEvent(const zexuan::base::EventMessage& event) override {
        if (eventHandler_) {
            eventHandler_(event);
        }
    }

private:
    IPlugin* plugin_;
    std::function<void(const zexuan::base::Message&)> messageHandler_;
    std::function<void(const zexuan::base::EventMessage&)> eventHandler_;
};

/**
 * 插件主题实现 - 纯粹基于BaseSubject
 */
class PluginSubject : public zexuan::base::BaseSubject {
public:
    PluginSubject(int id, const std::string& description, IPlugin* plugin)
        : zexuan::base::BaseSubject(id, description), plugin_(plugin) {}

private:
    IPlugin* plugin_;
};

/**
 * 统一的插件基类 - 纯粹基于新的Observer/Subject/Mediator模式
 */
template<typename DerivedPlugin>
class PluginBase : public IPlugin {
public:
    PluginBase(const std::string& loggerName)
        : loggerName_(loggerName), pluginId_(generatePluginId()) {
        // 创建Observer和Subject实例
        observer_ = std::make_unique<PluginObserver>(pluginId_, loggerName + "_Observer", this);
        subject_ = std::make_unique<PluginSubject>(pluginId_ + 1000, loggerName + "_Subject", this);

        // 设置消息和事件处理器
        observer_->setMessageHandler([this](const zexuan::base::Message& msg) {
            handleMessage(msg);
        });

        observer_->setEventHandler([this](const zexuan::base::EventMessage& event) {
            handleEvent(event);
        });
    }

    virtual ~PluginBase() = default;

    // === IPlugin接口实现 ===
    virtual const char* getName() const = 0;
    virtual const char* getVersion() const = 0;
    virtual const char* getDescription() const = 0;

    zexuan::base::Observer* asObserver() override { return observer_.get(); }
    zexuan::base::Subject* asSubject() override { return subject_.get(); }

    // === 生命周期管理 ===

    bool initialize() override {
        if (initialized_) {
            return true;
        }

        getLogger()->info("{} initializing...", getName());

        // 确保中介者已初始化并注册Observer和Subject
        ensureMediator();

        // 调用子类的初始化逻辑
        if (!onInitialize()) {
            getLogger()->error("{} initialization failed", getName());
            return false;
        }

        // 设置Observer和Subject的Mediator
        subject_->setMediator(mediator_);

        initialized_ = true;
        getLogger()->info("{} initialized successfully!", getName());

        // 调用初始化完成回调
        onInitializationComplete();

        return true;
    }

    void shutdown() override {
        if (!initialized_) {
            return;
        }

        getLogger()->info("{} shutting down...", getName());

        // 调用子类的关闭逻辑
        onShutdown();

        // 从Mediator注销Observer和Subject
        if (mediator_) {
            std::string errorMsg;
            mediator_->unregisterObserver(pluginId_, errorMsg);
            mediator_->unregisterSubject(pluginId_ + 1000, errorMsg);
        }

        initialized_ = false;
        getLogger()->info("{} shutdown complete!", getName());
    }

    // === 状态管理 ===

    bool isInitialized() const { return initialized_; }
    bool isPaused() const { return paused_; }

    void setPaused(bool paused) {
        paused_ = paused;
        getLogger()->info("{} {}", getName(), paused ? "paused" : "resumed");
    }

    // === Observer/Subject通信接口 ===

    /**
     * 发送消息到指定目标
     */
    void sendMessage(const std::string& content, int targetId) {
        zexuan::base::Message msg(1, content, pluginId_);
        subject_->sendCommand(msg, targetId);
    }

    /**
     * 发送结果消息
     */
    void sendResult(const std::string& content, const std::string& invokeId) {
        zexuan::base::Message msg(100, content, pluginId_);
        msg.setInvokeId(invokeId);
        subject_->sendResult(msg);
    }

    /**
     * 发送事件通知
     */
    void sendEvent(int eventType, const std::string& deviceId, const std::string& data) {
        zexuan::base::EventMessage event(eventType, deviceId, data, pluginId_);
        subject_->sendEventNotify(event);
    }

    /**
     * 广播消息给所有观察者
     */
    void broadcastMessage(const std::string& content) {
        zexuan::base::Message msg(2, content, pluginId_);
        subject_->notify(msg);
    }

protected:
    // === 子类需要实现的虚函数 ===

    /**
     * 子类初始化逻辑
     */
    virtual bool onInitialize() = 0;

    /**
     * 子类关闭逻辑
     */
    virtual void onShutdown() {}

    /**
     * 初始化完成后的回调
     */
    virtual void onInitializationComplete() {}

    // === 消息和事件处理方法 ===

    /**
     * 处理接收到的消息（子类可重写）
     */
    virtual void handleMessage(const zexuan::base::Message& message) {
        getLogger()->info("Plugin {} received message: type={}, content={}, from={}",
                         getName(), message.getType(), message.getContent(), message.getSourceId());
    }

    /**
     * 处理接收到的事件（子类可重写）
     */
    virtual void handleEvent(const zexuan::base::EventMessage& event) {
        getLogger()->info("Plugin {} received event: type={}, device={}, data={}, from={}",
                         getName(), event.getEventType(), event.getDeviceId(),
                         event.getEventData(), event.getSourceId());
    }

    // === 便捷的Observer/Subject操作方法 ===

    /**
     * 设置关心的事件类型
     */
    void setCareEventTypes(const std::vector<int>& eventTypes) {
        observer_->careEventTypes_ = eventTypes;
    }

    /**
     * 设置关心的设备ID
     */
    void setCareDeviceIds(const std::vector<std::string>& deviceIds) {
        observer_->careDeviceIds_ = deviceIds;
    }

    /**
     * 设置管理的设备ID（用于Subject）
     */
    void setManagedDevices(const std::vector<std::string>& deviceIds) {
        subject_->setManagedDevices(deviceIds);
    }

    /**
     * 直接附加一个Observer到这个插件的Subject
     */
    void attachObserver(std::shared_ptr<zexuan::base::Observer> observer) {
        subject_->attach(observer);
    }

    /**
     * 从这个插件的Subject分离一个Observer
     */
    void detachObserver(std::shared_ptr<zexuan::base::Observer> observer) {
        subject_->detach(observer);
    }



    // === 错误处理辅助方法 ===

    template<typename Func>
    void safeExecute(const std::string& operation, Func&& func) {
        try {
            func();
        } catch (const std::exception& e) {
            getLogger()->error("Error in {}: {}", operation, e.what());
        }
    }

    // Logger访问（延迟初始化）
    std::shared_ptr<spdlog::logger> getLogger() {
        if (!logger_) {
            try {
                // 通过单例注册表获取 Logger，然后创建具名 logger
                auto loggerSingleton = getSingleton<zexuan::Logger>();
                logger_ = loggerSingleton->getLogger(loggerName_);
            } catch (const std::exception&) {
                // 如果单例注册表不可用，创建一个简单的控制台 logger
                logger_ = spdlog::stdout_color_mt(loggerName_ + "_fallback");
            }
        }
        return logger_;
    }

private:
    /**
     * 确保中介者已初始化
     */
    void ensureMediator();  // 实现需要在包含MessageMediator头文件后

    /**
     * 生成唯一的插件ID
     */
    static int generatePluginId() {
        static int nextId = 1;
        return nextId++;
    }

    // 状态变量
    bool initialized_ = false;
    bool paused_ = false;

    // 插件标识
    std::shared_ptr<spdlog::logger> logger_;
    std::string loggerName_;
    int pluginId_;

    // Observer和Subject实例
    std::unique_ptr<PluginObserver> observer_;
    std::unique_ptr<PluginSubject> subject_;

    // 中介者
    std::shared_ptr<zexuan::base::Mediator> mediator_;
};

// 为了向后兼容，提供别名
template<typename T>
using SimplePluginInterface = PluginBase<T>;

// 非模板别名，用于插件管理器
using SimplePluginInterface_Base = IPlugin;

} // namespace plugin
} // namespace zexuan

// 包含MessageMediator头文件并提供ensureMediator实现
#include "zexuan/plugin/message_mediator.hpp"

namespace zexuan {
namespace plugin {

template<typename DerivedPlugin>
void PluginBase<DerivedPlugin>::ensureMediator() {
    if (!mediator_) {
        mediator_ = getSingleton<MessageMediator>();

        // 注册Observer和Subject到Mediator
        std::string errorMsg;
        if (!mediator_->registerObserver(pluginId_, observer_.get(), errorMsg)) {
            getLogger()->error("Failed to register observer: {}", errorMsg);
        }
        if (!mediator_->registerSubject(pluginId_ + 1000, subject_.get(), errorMsg)) {
            getLogger()->error("Failed to register subject: {}", errorMsg);
        }
    }
}

} // namespace plugin
} // namespace zexuan
