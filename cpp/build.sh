#!/bin/bash
set -e  # 遇到错误立即退出

# 默认构建类型
BUILD_TYPE=${1:-debug}
shift || true  # 移除第一个参数，保留剩余的CMake选项

# 转换构建类型
case ${BUILD_TYPE,,} in  # 转换为小写再比较
    "debug")
        CONAN_BUILD_TYPE="Debug"
        CMAKE_BUILD_TYPE="debug"
        ;;
    "release")
        CONAN_BUILD_TYPE="Release"
        CMAKE_BUILD_TYPE="release"
        ;;
    "relwithdebinfo")
        CONAN_BUILD_TYPE="RelWithDebInfo"
        CMAKE_BUILD_TYPE="relwithdebinfo"
        ;;
    *)
        echo "错误: 不支持的构建类型 '${BUILD_TYPE}'"
        echo "支持的构建类型: debug, release, relwithdebinfo"
        exit 1
        ;;
esac

# 显示构建信息
echo "开始构建..."
echo "构建类型: ${BUILD_TYPE}"
echo "Conan 构建类型: ${CONAN_BUILD_TYPE}"
echo "CMake 构建类型: ${CMAKE_BUILD_TYPE}"
echo "额外的 CMake 选项: $@"

# 确保构建目录存在
BUILD_DIR="build"
mkdir -p "${BUILD_DIR}"
cd "${BUILD_DIR}"

# 安装依赖
echo "安装 Conan 依赖..."
conan install .. --build=missing -s build_type=${CONAN_BUILD_TYPE}

# 配置项目
echo "配置 CMake..."
cd ..  # 返回项目根目录
cmake --preset "conan-${CMAKE_BUILD_TYPE}" "$@"

# 构建项目
echo "构建项目..."
cmake --build --preset "conan-${CMAKE_BUILD_TYPE}" -j$(nproc)

echo "构建完成！"
echo "可执行文件位置: bin/"
echo "库文件位置: libs/"