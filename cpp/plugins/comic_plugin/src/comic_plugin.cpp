#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/thread_pool.hpp"
#include <filesystem>
#include <chrono>
#include <iomanip>
#include <sstream>
#include <random>
#include <vector>
#include <algorithm>
#include <atomic>
#include <thread>
#include <future>
namespace fs = std::filesystem;

/**
 * Comic Plugin - 批量重命名文件插件
 * 功能：将文件夹下的所有文件重命名为：毫秒级时间戳_UUID.原扩展名
 * 格式：20240823133804_c87398b4-0e7e-4ef5-9d8f-7e2dbde914cc.jpg
 */
class ComicPlugin : public zexuan::plugin::PluginBase<ComicPlugin> {
public:
    ComicPlugin() : PluginBase("zexuan.plugin.comic") {}

    // === 基础插件信息 ===
    const char* getName() const override { return "ComicPlugin"; }
    const char* getVersion() const override { return "1.0.0"; }
    const char* getDescription() const override { return "Comic file batch renaming plugin with thread pool"; }

protected:
    // === 基类虚函数实现 ===
    
    bool onInitialize() override {
        getLogger()->info("ComicPlugin initializing...");
        std::cout << "ComicPlugin::onInitialize() called" << std::endl;
        return true;
    }

    void onInitializationComplete() override {
        getLogger()->info("ComicPlugin ready for batch file renaming");

        // 使用新的Observer/Subject系统发送就绪消息
        broadcastMessage("ComicPlugin is ready! Send folder path to start renaming.");
    }

    void onShutdown() override {
        getLogger()->info("ComicPlugin shutting down...");

        // 由于消息处理是同步的，到这里时所有任务都已经完成
        // 不需要额外的等待逻辑

        getLogger()->info("ComicPlugin shutdown complete");
    }

    // === 重写消息和事件处理方法 ===

    void handleMessage(const zexuan::base::Message& message) override {
        std::cout << "ComicPlugin received message!" << std::endl;
        std::cout << "ComicPlugin::handleMessageEvent called" << std::endl;
        std::cout << "  Source: " << message.getSourceId() << std::endl;
        std::cout << "  Message: " << message.getContent() << std::endl;
        std::cout << "  IsPaused: " << isPaused() << std::endl;

        // 调试：显示插件的 MessageMediator 地址
        auto pluginMediator = zexuan::getSingleton<zexuan::plugin::MessageMediator>();
        std::cout << "  🔍 插件 MessageMediator 地址: " << pluginMediator.get() << std::endl;

        if (isPaused()) {
            std::cout << "  Skipping message (paused)" << std::endl;
            return;
        }

        getLogger()->info("Received message from {}: {}", message.getSourceId(), message.getContent());

        // 处理文件夹路径消息
        handleFolderPath(message.getContent(), std::to_string(message.getSourceId()));
    }

    void handleEvent(const zexuan::base::EventMessage& event) override {
        std::cout << "ComicPlugin received event: type=" << event.getEventType()
                  << ", device=" << event.getDeviceId() << std::endl;

        // 处理控制事件
        if (event.getEventType() == 999) { // 停止事件
            if (isProcessing_) {
                getLogger()->info("Stopping file processing...");
                isProcessing_ = false;
            }
        }
    }

private:
    // === 成员变量 ===
    BS::light_thread_pool threadPool_;  // 成员变量必须显式指定模板参数
    std::atomic<bool> isProcessing_{false};
    std::atomic<int> processedCount_{0};
    std::atomic<int> totalCount_{0};
    std::string currentFolderPath_;  // 当前设置的文件夹路径

    // === 核心功能方法 ===

    // === 核心功能方法 ===
    
    void handleFolderPath(const std::string& message, const std::string& requester) {
        std::string folderPath;

        // 解析消息格式
        if (message.find("Folder path: ") == 0) {
            folderPath = message.substr(13); // 去掉 "Folder path: " 前缀
        } else if (message == "start_processing") {
            // 如果已经有文件夹路径，开始处理
            std::cout << "ComicPlugin: Received start_processing command" << std::endl;
            if (!currentFolderPath_.empty()) {
                std::cout << "ComicPlugin: Starting processing for: " << currentFolderPath_ << std::endl;
                startProcessing(currentFolderPath_, requester);
                std::cout << "ComicPlugin: startProcessing() returned" << std::endl;
            } else {
                std::cout << "ComicPlugin: Error - No folder path set. Please send folder path first." << std::endl;
            }
            return;
        } else {
            // 直接当作文件夹路径处理
            folderPath = message;
        }

        if (folderPath.empty()) {
            std::cout << "ComicPlugin: Error - Empty folder path" << std::endl;
            return;
        }

        if (!fs::exists(folderPath) || !fs::is_directory(folderPath)) {
            std::cout << "ComicPlugin: Error - Invalid folder path: " << folderPath << std::endl;
            return;
        }

        // 保存文件夹路径
        currentFolderPath_ = folderPath;
        getLogger()->info("Folder path set to: {}", folderPath);
        std::cout << "ComicPlugin: Folder path set to: " << folderPath << ". Ready for start_processing command." << std::endl;
    }

    void startProcessing(const std::string& folderPath, const std::string& requester) {
        std::cout << "ComicPlugin::startProcessing() called" << std::endl;
        std::cout << "  📁 Folder path: " << folderPath << std::endl;
        std::cout << "  👤 Requester: " << requester << std::endl;

        if (isProcessing_) {
            std::cout << "  ⚠️  Already processing, skipping" << std::endl;
            std::cout << "ComicPlugin: Already processing files. Please wait." << std::endl;
            return;
        }

        getLogger()->info("Starting batch rename for folder: {}", folderPath);
        std::cout << "ComicPlugin: Starting batch rename for: " << folderPath << std::endl;

        std::cout << "ComicPlugin: About to submit task to thread pool and wait for completion" << std::endl;

        // 同步处理文件重命名 - 直接等待任务完成
        auto future = threadPool_.submit_task([this, folderPath, requester]() {
            std::cout << "ComicPlugin: Thread pool task started" << std::endl;
            processFolderAsync(folderPath, requester);
            std::cout << "ComicPlugin: Thread pool task completed" << std::endl;
        });

        // 直接等待任务完成，阻塞消息处理
        std::cout << "ComicPlugin: Waiting for task completion..." << std::endl;
        future.wait();
        std::cout << "ComicPlugin: Task completed, message processing will now return" << std::endl;

        // 发送完成通知
        std::cout << "ComicPlugin: File renaming completed for: " << folderPath << std::endl;
    }

    void processFolderAsync(const std::string& folderPath, const std::string& requester) {
        std::cout << "ComicPlugin: processFolderAsync started" << std::endl;

        isProcessing_ = true;
        processedCount_ = 0;
        totalCount_ = 0;

        try {
            std::cout << "ComicPlugin: About to collect files from: " << folderPath << std::endl;

            // 收集所有文件
            std::vector<fs::path> files;
            for (const auto& entry : fs::directory_iterator(folderPath)) {
                if (entry.is_regular_file()) {
                    files.push_back(entry.path());
                }
            }

            std::cout << "ComicPlugin: Files collected, count: " << files.size() << std::endl;

            totalCount_ = files.size();
            if (totalCount_ == 0) {
                std::cout << "ComicPlugin: No files found, returning" << std::endl;
                isProcessing_ = false;
                return;
            }

            getLogger()->info("Found {} files to process", totalCount_.load());
            // 注意：不在线程池任务中调用 publishMessage，避免死锁

            std::cout << "ComicPlugin: About to submit blocks to thread pool" << std::endl;

            // 使用线程池的 blocks 功能批量处理文件（使用 submit_blocks 而不是 detach_blocks）
            const int numBlocks = std::min(8, static_cast<int>(files.size())); // 最多8个块

            std::cout << "ComicPlugin: numBlocks = " << numBlocks << std::endl;

            auto blockFutures = threadPool_.submit_blocks(0, files.size(),
                [this, &files, requester](int start, int end) {
                    processFileBlock(files, start, end, requester);
                },
                numBlocks
            );

            // 等待所有块处理完成
            std::cout << "ComicPlugin: About to wait for " << blockFutures.size() << " futures" << std::endl;
            for (size_t i = 0; i < blockFutures.size(); ++i) {
                blockFutures[i].wait();
            }

            std::cout << "ComicPlugin: All futures completed" << std::endl;

            // 记录完成信息（不在线程池任务中调用 publishMessage）
            std::string completionMsg = "Batch rename completed! Processed " +
                                      std::to_string(processedCount_.load()) + "/" +
                                      std::to_string(totalCount_.load()) + " files.";

            getLogger()->info(completionMsg);
            // 注意：不在线程池任务中调用 publishMessage，避免死锁

        } catch (const std::exception& e) {
            std::string errorMsg = "Error during batch rename: " + std::string(e.what());
            getLogger()->error(errorMsg);
            // 注意：不在线程池任务中调用 publishMessage，避免死锁
        }

        isProcessing_ = false;
    }

    void processFileBlock(const std::vector<fs::path>& files, int start, int end, const std::string& requester) {
        std::cout << "ComicPlugin: processFileBlock called, range [" << start << ", " << end << ")" << std::endl;

        for (int i = start; i < end && isProcessing_; ++i) {
            try {
                const auto& filePath = files[i];
                std::cout << "ComicPlugin: Processing file " << (i+1) << "/" << files.size() << ": " << filePath.filename().string() << std::endl;

                std::string newName = generateNewFileName(filePath.extension().string());
                fs::path newPath = filePath.parent_path() / newName;

                std::cout << "ComicPlugin: Generated new name: " << newName << std::endl;

                // 确保新文件名不存在
                int counter = 1;
                while (fs::exists(newPath)) {
                    std::string baseName = newName.substr(0, newName.find_last_of('.'));
                    std::string extension = newName.substr(newName.find_last_of('.'));
                    newPath = filePath.parent_path() / (baseName + "_" + std::to_string(counter) + extension);
                    counter++;
                }

                // 重命名文件
                std::cout << "ComicPlugin: About to rename: " << filePath.string() << " -> " << newPath.string() << std::endl;
                fs::rename(filePath, newPath);
                processedCount_++;
                std::cout << "ComicPlugin: ✅ Successfully renamed file " << (i+1) << std::endl;

                getLogger()->debug("Renamed: {} -> {}", filePath.filename().string(), newPath.filename().string());

                // 每处理10个文件记录一次进度（不发送消息，避免死锁）
                if (processedCount_.load() % 10 == 0) {
                    getLogger()->debug("Progress: {}/{}", processedCount_.load(), totalCount_.load());
                }

            } catch (const std::exception& e) {
                getLogger()->error("Failed to rename file {}: {}", files[i].string(), e.what());
            }
        }
    }

    // === 辅助方法 ===
    
    std::string generateNewFileName(const std::string& extension) {
        // 生成毫秒级时间戳
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);
        auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;
        
        std::stringstream ss;
        ss << std::put_time(std::localtime(&time_t), "%Y%m%d%H%M%S");
        ss << std::setfill('0') << std::setw(3) << ms.count();
        
        // 生成UUID
        std::string uuid = generateUUID();
        
        // 组合文件名
        return ss.str() + "_" + uuid + extension;
    }

    std::string generateUUID() {
        // 简单的UUID生成器（UUID v4格式）
        std::random_device rd;
        std::mt19937 gen(rd());
        std::uniform_int_distribution<> dis(0, 15);
        std::uniform_int_distribution<> dis2(8, 11);

        std::stringstream ss;
        int i;
        ss << std::hex;
        for (i = 0; i < 8; i++) {
            ss << dis(gen);
        }
        ss << "-";
        for (i = 0; i < 4; i++) {
            ss << dis(gen);
        }
        ss << "-4";
        for (i = 0; i < 3; i++) {
            ss << dis(gen);
        }
        ss << "-";
        ss << dis2(gen);
        for (i = 0; i < 3; i++) {
            ss << dis(gen);
        }
        ss << "-";
        for (i = 0; i < 12; i++) {
            ss << dis(gen);
        }
        return ss.str();
    }
};

// === 插件导出函数 ===
extern "C" {
    /**
     * 设置单例访问器（由主程序调用）
     */
    void set_singleton_accessor(zexuan::SingletonRegistryAccessor accessor) {
        zexuan::setSingletonRegistryAccessor(accessor);
        std::cout << "ComicPlugin: 单例访问器已设置" << std::endl;
    }

    ComicPlugin* create_plugin() {
        return new ComicPlugin();
    }

    void destroy_plugin(ComicPlugin* plugin) {
        delete plugin;
    }

    const char* get_plugin_info() {
        return "Comic Plugin v1.0.0 - Cross-library singleton support";
    }
}
