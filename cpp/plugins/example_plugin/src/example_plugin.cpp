#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include <vector>
#include <map>
#include <numeric>
#include <algorithm>
#include <sstream>
#include <iostream>
/**
 * 简化的数据处理插件 - 使用新的Observer/Subject/Mediator模式
 */
class DataProcessorPlugin : public zexuan::plugin::PluginBase<DataProcessorPlugin> {
public:
    DataProcessorPlugin() : PluginBase("zexuan.plugin.example") {}

    // === 基础插件信息 ===
    const char* getName() const override { return "ExamplePlugin"; }
    const char* getVersion() const override { return "1.0.0"; }
    const char* getDescription() const override { return "Example Plugin demo using new Observer/Subject/Mediator pattern"; }

protected:
    // === 基类虚函数实现 ===

    bool onInitialize() override {
        // 重置统计信息
        dataProcessingStats_.clear();
        dataGenerationActive_ = false;
        return true;
    }

    void onInitializationComplete() override {
        // 发布初始数据
        publishInitialData();
    }

    // === 重写消息和事件处理方法 ===

    void handleMessage(const zexuan::base::Message& message) override {
        std::cout << "ExamplePlugin received message from " << message.getSourceId()
                  << ": " << message.getContent() << std::endl;

        if (isPaused()) {
            return;
        }

        // 处理不同类型的消息
        processDataMessage(message.getContent(), std::to_string(message.getSourceId()));
    }

    void handleEvent(const zexuan::base::EventMessage& event) override {
        std::cout << "ExamplePlugin received event: type=" << event.getEventType()
                  << ", device=" << event.getDeviceId() << std::endl;

        // 处理数据事件
        if (event.getEventType() == 1) {
            processDataEvent(event.getEventData());
        }
    }

private:
    // === 成员变量 ===
    std::map<std::string, double> dataProcessingStats_;
    bool dataGenerationActive_ = false;

    // === 核心功能方法 ===

    void processDataMessage(const std::string& message, const std::string& requester) {
        getLogger()->info("Processing data request: {}", message);

        // 处理数据处理请求
        if (message == "process_data" || message.find("data") != std::string::npos) {
            sendMessage("Data processing completed", std::stoi(requester));
            // 发送数据事件
            sendEvent(1, "data_processor", "Processed: " + message);
        } else {
            sendMessage("ExamplePlugin processed: " + message, std::stoi(requester));
        }
    }

    void processDataEvent(const std::string& eventData) {
        getLogger()->info("Processing data event: {}", eventData);
        // 处理数据事件逻辑
        dataProcessingStats_["events_processed"]++;
    }

    void publishInitialData() {
        getLogger()->info("Published initial data");
        broadcastMessage("ExamplePlugin is ready for data processing!");
    }
};

// === 插件导出函数 ===
extern "C" {
    /**
     * 设置单例访问器（由主程序调用）
     * 这样插件就能访问主程序的单例注册表
     */
    void set_singleton_accessor(zexuan::SingletonRegistryAccessor accessor) {
        zexuan::setSingletonRegistryAccessor(accessor);
        std::cout << "ExamplePlugin: 单例访问器已设置" << std::endl;
    }

    DataProcessorPlugin* create_plugin() {
        return new DataProcessorPlugin();
    }

    void destroy_plugin(DataProcessorPlugin* plugin) {
        delete plugin;
    }

    const char* get_plugin_info() {
        return "Example Plugin v1.0.0 - Using new Observer/Subject/Mediator pattern";
    }
}
