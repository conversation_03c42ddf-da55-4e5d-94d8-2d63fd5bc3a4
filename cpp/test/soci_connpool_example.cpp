#include "soci/soci.h"
#include "soci/mysql/soci-mysql.h"
#include "zexuan/thread_pool.hpp"
#include "zexuan/database_connection_pool.hpp"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <vector>
using namespace soci;
using namespace std;

// 格式化时间戳的辅助函数
std::string format_timestamp(const std::tm& t) {
    std::ostringstream oss;
    oss << std::put_time(&t, "%Y-%m-%d %H:%M:%S");
    return oss.str();
}

struct Comic {
    int id;
    std::string name;
    std::string path;
    std::tm created_at;
    std::tm updated_at;
};

namespace soci {
    template<>
    struct type_conversion<Comic> {
        typedef values base_type;

        static void from_base(values const & v, indicator /* ind */, Comic & c) {
            c.id = v.get<int>("id");
            c.name = v.get<std::string>("name");
            c.path = v.get<std::string>("path");
            
            indicator ind_created = v.get_indicator("created_at");
            if (ind_created != i_null) {
                c.created_at = v.get<std::tm>("created_at");
            }
            
            indicator ind_updated = v.get_indicator("updated_at");
            if (ind_updated != i_null) {
                c.updated_at = v.get<std::tm>("updated_at");
            }
        }

        static void to_base(const Comic & c, values & v, indicator & ind) {
            v.set("id", c.id);
            v.set("name", c.name);
            v.set("path", c.path);
            v.set("created_at", c.created_at);
            v.set("updated_at", c.updated_at);
            ind = i_ok;
        }
    };
}

// 测试多线程数据库操作
void test_multi_thread_db() {
    // 获取数据库连接池实例（会自动从配置文件初始化）
    auto& pool = zexuan::DatabaseConnectionPool::getInstance();

    // 创建线程池
    zexuan::thread_pool thread_pool;
    
    // 准备测试数据
    vector<Comic> comics;
    for(int i = 0; i < 200; i++) {
        Comic c;
        c.id = i + 100;
        c.name = "test_" + to_string(i);
        c.path = "path_" + to_string(i);
        time_t now = time(nullptr);
        c.created_at = *localtime(&now);
        c.updated_at = c.created_at;
        comics.push_back(c);
    }

    // 1. 使用基本类型向量进行批量插入
    cout << "\n=== 使用批量插入 ===\n";
    const size_t BATCH_SIZE = 20;
    
    auto insert_futures = thread_pool.submit_blocks(0, comics.size(),
        [&comics](size_t start, size_t end) {
            try {
                zexuan::ConnectionGuard conn;
                size_t block_size = end - start;

                // 准备基本类型的向量
                vector<int> ids(block_size);
                vector<string> names(block_size);
                vector<string> paths(block_size);
                vector<std::tm> created_times(block_size);
                vector<std::tm> updated_times(block_size);

                // 填充数据
                for(size_t i = 0; i < block_size; i++) {
                    const Comic& c = comics[start + i];
                    ids[i] = c.id;
                    names[i] = c.name;
                    paths[i] = c.path;
                    created_times[i] = c.created_at;
                    updated_times[i] = c.updated_at;
                }

                // 使用预处理语句进行批量插入
                statement st = ((*conn).prepare << 
                    "INSERT INTO comics(id, name, path, created_at, updated_at) "
                    "VALUES(:id, :name, :path, :created_at, :updated_at)",
                    use(ids), use(names), use(paths),
                    use(created_times), use(updated_times));

                // 执行批量插入
                st.execute(true);

                cout << "成功批量插入 " << block_size << " 条记录\n";
                return block_size;
            } catch(const std::exception& e) {
                cerr << "批量插入失败: " << e.what() << endl;
                return size_t(0);
            }
        },
        comics.size() / BATCH_SIZE
    );

    // 等待所有插入完成并统计
    auto insert_results = insert_futures.get();
    size_t total_inserted = 0;
    for(auto count : insert_results) {
        total_inserted += count;
    }
    cout << "总共成功插入 " << total_inserted << " 条记录\n";

    // 2. 使用 blocks 和 rowset 进行批量查询
    cout << "\n=== 使用 rowset 进行批量查询 ===\n";
    auto query_futures = thread_pool.submit_blocks(0, comics.size(),
        [](size_t start, size_t end) {
            try {
                zexuan::ConnectionGuard conn;
                
                // 计算查询范围
                int start_id = static_cast<int>(start + 100);
                int end_id = static_cast<int>(end + 100);
                
                // 使用 rowset 进行查询
                rowset<row> rs = ((*conn).prepare << 
                    "SELECT * FROM comics WHERE id >= :start AND id < :end "
                    "ORDER BY id",
                    use(start_id), use(end_id));

                size_t count = 0;
                for (const row& r : rs) {
                    cout << "ID: " << r.get<int>(0)
                         << ", Name: " << r.get<string>(1)
                         << ", Path: " << r.get<string>(2) << endl;
                    count++;
                }
                cout << "成功查询块 [" << start << ", " << end 
                     << "), 共 " << count << " 条记录\n";
                return count;
            } catch(const std::exception& e) {
                cerr << "批量查询失败: " << e.what() << endl;
                return size_t(0);
            }
        },
        comics.size() / BATCH_SIZE
    );

    // 等待所有查询完成并统计
    auto query_results = query_futures.get();
    size_t total_queried = 0;
    for(auto count : query_results) {
        total_queried += count;
    }
    cout << "总共成功查询 " << total_queried << " 条记录\n";

    // 3. 使用向量绑定进行批量删除
    cout << "\n=== 使用向量绑定进行批量删除 ===\n";
    auto delete_futures = thread_pool.submit_blocks(0, comics.size(),
        [&comics](size_t start, size_t end) {
            try {
                zexuan::ConnectionGuard conn;
                size_t block_size = end - start;
                
                // 准备删除的ID列表
                vector<int> ids(block_size);
                for(size_t i = 0; i < block_size; i++) {
                    ids[i] = comics[start + i].id;
                }

                // 批量删除
                statement st = ((*conn).prepare << 
                    "DELETE FROM comics WHERE id = :id",
                    use(ids));
                
                st.execute(true);
                cout << "成功批量删除 " << block_size << " 条记录\n";
                return block_size;
            } catch(const std::exception& e) {
                cerr << "批量删除失败: " << e.what() << endl;
                return size_t(0);
            }
        },
        comics.size() / BATCH_SIZE
    );

    // 等待所有删除完成并统计
    auto delete_results = delete_futures.get();
    size_t total_deleted = 0;
    for(auto count : delete_results) {
        total_deleted += count;
    }
    cout << "总共成功删除 " << total_deleted << " 条记录\n";
}

int main() {
    try {
        test_multi_thread_db();
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    return 0;
}
