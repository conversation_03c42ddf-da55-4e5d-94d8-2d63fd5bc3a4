#include "zexuan/base/singleton_registry.hpp"
#include <iostream>
#include <thread>
#include <vector>
#include <cassert>
#include <chrono>

using namespace zexuan;

// 测试用的简单类
class TestClass {
public:
    TestClass() : value_(42) {
        std::cout << "TestClass constructed with value: " << value_ << std::endl;
    }
    
    ~TestClass() {
        std::cout << "TestClass destructed" << std::endl;
    }
    
    int getValue() const { return value_; }
    void setValue(int value) { value_ = value; }
    
private:
    int value_;
};

// 另一个测试类
class AnotherTestClass {
public:
    AnotherTestClass() : name_("AnotherTest") {
        std::cout << "AnotherTestClass constructed with name: " << name_ << std::endl;
    }
    
    ~AnotherTestClass() {
        std::cout << "AnotherTestClass destructed" << std::endl;
    }
    
    const std::string& getName() const { return name_; }
    void setName(const std::string& name) { name_ = name; }
    
private:
    std::string name_;
};

// 测试基本功能
void testBasicFunctionality() {
    std::cout << "\n=== 测试基本功能 ===" << std::endl;
    
    auto& registry = SingletonRegistry::getInstance();
    
    // 测试获取单例
    auto instance1 = registry.get<TestClass>();
    auto instance2 = registry.get<TestClass>();
    
    // 验证是同一个实例
    assert(instance1 == instance2);
    assert(instance1.get() == instance2.get());
    std::cout << "✓ 单例测试通过：两次获取返回相同实例" << std::endl;
    
    // 测试不同类型的单例
    auto another1 = registry.get<AnotherTestClass>();
    auto another2 = registry.get<AnotherTestClass>();
    
    assert(another1 == another2);
    assert(another1.get() != instance1.get()); // 不同类型应该是不同实例
    std::cout << "✓ 不同类型单例测试通过" << std::endl;
    
    // 测试 exists 方法
    assert(registry.exists<TestClass>());
    assert(registry.exists<AnotherTestClass>());
    std::cout << "✓ exists() 方法测试通过" << std::endl;
    
    // 测试 size 方法
    assert(registry.size() == 2);
    std::cout << "✓ size() 方法测试通过，当前注册了 " << registry.size() << " 个单例" << std::endl;
}

// 测试线程安全
void testThreadSafety() {
    std::cout << "\n=== 测试线程安全 ===" << std::endl;
    
    auto& registry = SingletonRegistry::getInstance();
    
    // 清空之前的实例
    registry.clear();
    
    const int numThreads = 10;
    const int numIterations = 100;
    std::vector<std::thread> threads;
    std::vector<std::shared_ptr<TestClass>> instances(numThreads);
    
    // 启动多个线程同时获取单例
    for (int i = 0; i < numThreads; ++i) {
        threads.emplace_back([&registry, &instances, i, numIterations]() {
            for (int j = 0; j < numIterations; ++j) {
                auto instance = registry.get<TestClass>();
                if (j == 0) {
                    instances[i] = instance;
                }
                // 验证每次获取的都是同一个实例
                assert(instance == instances[i]);
            }
        });
    }
    
    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }
    
    // 验证所有线程获取的都是同一个实例
    for (int i = 1; i < numThreads; ++i) {
        assert(instances[0] == instances[i]);
    }
    
    std::cout << "✓ 线程安全测试通过：" << numThreads << " 个线程，每个线程 " 
              << numIterations << " 次迭代，所有实例都相同" << std::endl;
}

// 测试移除功能
void testRemoveFunctionality() {
    std::cout << "\n=== 测试移除功能 ===" << std::endl;
    
    auto& registry = SingletonRegistry::getInstance();
    
    // 确保有实例存在
    auto instance = registry.get<TestClass>();
    assert(registry.exists<TestClass>());
    assert(registry.size() >= 1);
    
    // 移除实例
    bool removed = registry.remove<TestClass>();
    assert(removed);
    assert(!registry.exists<TestClass>());
    std::cout << "✓ remove() 方法测试通过" << std::endl;
    
    // 再次尝试移除不存在的实例
    removed = registry.remove<TestClass>();
    assert(!removed);
    std::cout << "✓ 移除不存在实例的测试通过" << std::endl;
    
    // 移除后重新获取应该创建新实例
    auto newInstance = registry.get<TestClass>();
    assert(newInstance != instance); // 应该是不同的实例
    std::cout << "✓ 移除后重新创建实例测试通过" << std::endl;
}

// 测试清空功能
void testClearFunctionality() {
    std::cout << "\n=== 测试清空功能 ===" << std::endl;
    
    auto& registry = SingletonRegistry::getInstance();
    
    // 创建多个不同类型的实例
    auto testInstance = registry.get<TestClass>();
    auto anotherInstance = registry.get<AnotherTestClass>();
    
    assert(registry.size() >= 2);
    std::cout << "清空前注册表大小: " << registry.size() << std::endl;
    
    // 清空所有实例
    registry.clear();
    
    assert(registry.size() == 0);
    assert(!registry.exists<TestClass>());
    assert(!registry.exists<AnotherTestClass>());
    
    std::cout << "✓ clear() 方法测试通过，注册表已清空" << std::endl;
}

// 性能测试
void testPerformance() {
    std::cout << "\n=== 性能测试 ===" << std::endl;
    
    auto& registry = SingletonRegistry::getInstance();
    registry.clear();
    
    const int numIterations = 1000000;
    
    // 测试获取单例的性能
    auto start = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < numIterations; ++i) {
        auto instance = registry.get<TestClass>();
        // 简单使用实例以防止编译器优化
        volatile int value = instance->getValue();
        (void)value;
    }
    
    auto end = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
    
    std::cout << "✓ 性能测试完成：" << numIterations << " 次获取单例操作耗时 " 
              << duration.count() << " 微秒" << std::endl;
    std::cout << "  平均每次操作耗时: " << (double)duration.count() / numIterations 
              << " 微秒" << std::endl;
}

int main() {
    std::cout << "开始 SingletonRegistry 测试..." << std::endl;
    
    try {
        testBasicFunctionality();
        testThreadSafety();
        testRemoveFunctionality();
        testClearFunctionality();
        testPerformance();
        
        std::cout << "\n🎉 所有测试通过！SingletonRegistry 实现正确。" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "❌ 测试失败: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ 测试失败: 未知异常" << std::endl;
        return 1;
    }
    
    return 0;
}
