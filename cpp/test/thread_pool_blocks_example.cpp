#include "soci/soci.h" 
#include "soci/mysql/soci-mysql.h"
#include <iostream>
#include <sstream>
#include <iomanip>
#include <vector>
#include "zexuan/thread_pool.hpp"
using namespace soci;
using namespace std;
using namespace zexuan;
// 测试线程池分块处理的函数
void test_thread_pool_blocks() {
    cout << "\n=== 测试线程池分块处理 ===\n";
    
    // 创建线程池
    thread_pool pool;
    std::vector<int> data(1000);
    for(int i = 0; i < 1000; i++) data[i] = i;
    
    // 1. detach_blocks 示例
    cout << "\n1. detach_blocks 示例：\n";
    pool.detach_blocks(0, data.size(), 
        [&data](int start, int end) {
            int sum = 0;
            for(int i = start; i < end; i++) {
                sum += data[i];
                data[i] *= 2; // 将每个元素乘以2
            }
            cout << "处理块 [" << start << ", " << end 
                 << ") 的和为: " << sum << endl;
        }
    );
    pool.wait(); // 等待所有任务完成
    
    // 2. submit_blocks 示例
    cout << "\n2. submit_blocks 示例：\n";
    auto block_futures = pool.submit_blocks(0, data.size(),
        [&data](int start, int end) {
            int sum = 0;
            for(int i = start; i < end; i++) {
                sum += data[i];
            }
            return sum;
        }
    );
    auto block_results = block_futures.get();
    cout << "所有块的结果: ";
    for(auto result : block_results) {
        cout << result << " ";
    }
    cout << endl;
    
    // 3. detach_loop 示例
    cout << "\n3. detach_loop 示例：\n";
    std::atomic<int> total_sum = 0;
    pool.detach_loop(0, data.size(),
        [&data, &total_sum](int i) {
            total_sum += data[i];
            data[i] /= 2; // 将每个元素除以2
        }
    );
    pool.wait();
    cout << "总和为: " << total_sum << endl;
    
    // 4. submit_loop 示例
    cout << "\n4. submit_loop 示例：\n";
    auto loop_futures = pool.submit_loop(0, data.size(),
        [&data](int i) {
            cout << "处理索引 " << i 
                 << " 的值: " << data[i] << endl;
        }
    );
    loop_futures.wait();
    
    // 5. 使用优先级的示例
    cout << "\n5. 带优先级的分块示例：\n";
    auto high_priority_futures = pool.submit_blocks(0, data.size(),
        [&data](int start, int end) {
            cout << "高优先级处理块 [" << start << ", " 
                 << end << ")" << endl;
            return end - start;
        },
        4,  // 分成4块
        zexuan::pr::high  // 高优先级
    );
    high_priority_futures.wait(); // 等待高优先级任务完成
    
    // 6. 自定义块大小的示例
    cout << "\n6. 自定义块大小示例：\n";
    auto custom_block_futures = pool.submit_blocks(0, data.size(),
        [&data](int start, int end) {
            cout << "处理自定义块 [" << start << ", " 
                 << end << "), 大小: " 
                 << (end - start) << endl;
            return end - start;
        },
        8  // 分成8块
    );
    // 获取并显示块大小结果
    auto block_sizes = custom_block_futures.get();
    cout << "各块大小: ";
    for(auto size : block_sizes) {
        cout << size << " ";
    }
    cout << endl;
}


int main()
{
    try {

        // 测试线程池分块处理
        test_thread_pool_blocks();
    }
    catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    return 0;
}