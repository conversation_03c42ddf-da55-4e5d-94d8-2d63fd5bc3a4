#include "soci/soci.h" 
#include "soci/mysql/soci-mysql.h"
#include <iostream>
#include <sstream>
#include <iomanip>
using namespace soci;
#include <cassert>
// 格式化时间戳的辅助函数
std::string format_timestamp(const std::tm& t) {
    std::ostringstream oss;
    oss << std::put_time(&t, "%Y-%m-%d %H:%M:%S");
    return oss.str();
}

struct Comic
{
    int id;
    std::string name;
    std::string path;
    std::tm created_at;
    std::tm updated_at;
};

namespace soci
{
    template<>
    struct type_conversion<Comic>
    {
        typedef values base_type;

        static void from_base(values const & v, indicator /* ind */, Comic & c)
        {
            c.id = v.get<int>("id");
            c.name = v.get<std::string>("name");
            c.path = v.get<std::string>("path");
            
            // 时间戳处理
            indicator ind_created = v.get_indicator("created_at");
            if (ind_created != i_null) {
                c.created_at = v.get<std::tm>("created_at");
            }
            
            indicator ind_updated = v.get_indicator("updated_at");
            if (ind_updated != i_null) {
                c.updated_at = v.get<std::tm>("updated_at");
            }
        }

        static void to_base(const Comic & c, values & v, indicator & ind)
        {
            v.set("id", c.id);
            v.set("name", c.name);
            v.set("path", c.path);
            v.set("created_at", c.created_at);
            v.set("updated_at", c.updated_at);
            ind = i_ok;
        }
    };
}

using namespace std;
int main()
{
    try {
        session sql(mysql, "db=comic user=root password='!furryzexuan0' unix_socket=/run/mysqld/mysqld.sock"); 
        
        // 使用类型转换
        Comic comic;
        sql << "select * from Ash where id = 1", into(comic);

        // 输出结果
        std::cout << "ID: " << comic.id << std::endl;
        std::cout << "Name: " << comic.name << std::endl;
        std::cout << "Path: " << comic.path << std::endl;
        std::cout << "Created: " << format_timestamp(comic.created_at) << std::endl;
        std::cout << "Updated: " << format_timestamp(comic.updated_at) << std::endl;

        
        // Comic c;
        // c.id = 3;
        // c.name = "test";
        // c.path = "path/to/test";
        
        // // 获取当前时间
        // std::time_t t = std::time(nullptr);
        // c.created_at = *std::localtime(&t);
        // c.updated_at = c.created_at;
        
        // sql << "insert into Ash(id, name, path, created_at, updated_at) "
        //         "values(:id, :name, :path, :created_at, :updated_at)", use(c);

        // Comic c1;
        // sql << "select * from Ash", into(c1);
        // assert(c1.id == 2);
        // assert(c1.name + c1.path == "testpath/to/test");

        // 方式1：简单接口
        cout << "\n=== 使用简单接口 ===\n";
        rowset<row> rs = (sql.prepare << "select id, name, path, created_at, updated_at from Ash");
        for (const row& r : rs) {
            cout << "----------------------------------------\n"
                 << "ID: " << r.get<int>(0) << '\n'
                 << "名称: " << r.get<string>(1) << '\n'
                 << "路径: " << r.get<string>(2) << '\n'
                 << "创建时间: " << format_timestamp(r.get<std::tm>(3)) << '\n'
                 << "更新时间: " << format_timestamp(r.get<std::tm>(4)) << '\n'
                 << "----------------------------------------" << endl;
        }

        // 方式2：Core 接口
        cout << "\n=== 使用 Core 接口 ===\n";
        row r;
        statement st(sql);
        st.alloc();
        st.prepare("select id, name, path, created_at, updated_at from Ash");
        st.define_and_bind();
        st.exchange_for_rowset(into(r));
        st.execute(false);

        rowset_iterator<row> it(st, r);
        rowset_iterator<row> end;
        for (; it != end; ++it) {
            const row& current_row = *it;
            cout << "----------------------------------------\n"
                 << "ID: " << current_row.get<int>(0) << '\n'
                 << "名称: " << current_row.get<string>(1) << '\n'
                 << "路径: " << current_row.get<string>(2) << '\n'
                 << "创建时间: " << format_timestamp(current_row.get<std::tm>(3)) << '\n'
                 << "更新时间: " << format_timestamp(current_row.get<std::tm>(4)) << '\n'
                 << "----------------------------------------" << endl;
        }

        // // 批量插入
        // const int BATCH_SIZE = 25;
        // vector<int> values(BATCH_SIZE);
        // statement st = (sql.prepare << 
        //     "insert into numbers(value) values(:val)", use(values));

        // for (int batch = 0; batch < 4; batch++) {
        //     // 填充数据
        //     for(int i = 0; i < BATCH_SIZE; i++) {
        //         values[i] = batch * BATCH_SIZE + i;
        //     }
        //     st.execute(true);
        // }

        // // 批量查询
        // vector<int> results(BATCH_SIZE);
        // statement st = (sql.prepare << 
        //     "select value from numbers", into(results));
        // st.execute();
        // while (st.fetch()) {
        //     // 处理一批数据
        //     for(int val : results) {
        //         cout << val << endl;
        //     }
        //     results.resize(BATCH_SIZE);  // 重置大小以接收下一批
        // }
    }
    catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    return 0;
}