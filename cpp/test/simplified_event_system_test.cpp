#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/plugin/plugin_communication.hpp"
#include "spdlog/spdlog.h"
#include "spdlog/sinks/stdout_color_sinks.h"

using namespace zexuan::plugin;
using namespace zexuan::core;

/**
 * 简单的测试插件A - 发送者
 */
class TestPluginA : public PluginBase<TestPluginA> {
public:
    TestPluginA() : PluginBase("test.plugin.a") {}

    const char* getName() const override { return "TestPluginA"; }
    const char* getVersion() const override { return "1.0.0"; }
    const char* getDescription() const override { return "Test Plugin A - Sender"; }

    // 添加一个方法来发送测试事件
    void sendTestEvents() {
        std::cout << "TestPluginA sending test events..." << std::endl;

        // 发送点对点消息给TestPluginB
        zexuan::plugin::publishMessage("Hello TestPluginB, this is a direct message!", getName(), "TestPluginB");

        // 发送广播消息
        zexuan::plugin::publishMessage("Hello everyone, this is a broadcast message!", getName());

        // 发送点对点数据给TestPluginB
        std::vector<int> numbers = {1, 2, 3, 4, 5};
        zexuan::plugin::publishData("test_numbers", numbers, getName(), "TestPluginB");

        // 发送广播数据
        zexuan::plugin::publishData("broadcast_data", std::string("This is broadcast data"), getName());
    }

protected:
    bool onInitialize() override {
        std::cout << "TestPluginA initialized" << std::endl;
        return true;
    }

    void onInitializationComplete() override {
        // 延迟发送事件，确保所有插件都已初始化
        std::cout << "TestPluginA initialization complete" << std::endl;
    }

    void setupEventSubscriptions() override {
        // 订阅消息事件
        subscribeToMessages([this](auto event) {
            std::cout << "TestPluginA received message from " << event->source
                      << " (target: " << event->getTarget() << "): " << event->message << std::endl;
        });

        // 订阅数据事件
        subscribeToData([this](auto event) {
            std::cout << "TestPluginA received data event: " << event->dataType
                      << " from " << event->source << " (target: " << event->getTarget() << ")" << std::endl;
        });
    }
};

/**
 * 简单的测试插件B - 接收者
 */
class TestPluginB : public PluginBase<TestPluginB> {
public:
    TestPluginB() : PluginBase("test.plugin.b") {}

    const char* getName() const override { return "TestPluginB"; }
    const char* getVersion() const override { return "1.0.0"; }
    const char* getDescription() const override { return "Test Plugin B - Receiver"; }

protected:
    bool onInitialize() override {
        std::cout << "TestPluginB initialized" << std::endl;
        return true;
    }

    void setupEventSubscriptions() override {
        // 订阅消息事件
        subscribeToMessages([this](auto event) {
            std::cout << "TestPluginB received message from " << event->source
                      << " (target: " << event->getTarget() << "): " << event->message << std::endl;

            // 如果是点对点消息，回复
            if (!event->isBroadcast()) {
                zexuan::plugin::publishMessage("Thanks for the direct message!", getName(), event->source);
            }
        });

        // 订阅数据事件
        subscribeToData([this](auto event) {
            std::cout << "TestPluginB received data event: " << event->dataType
                      << " from " << event->source << " (target: " << event->getTarget() << ")" << std::endl;

            if (event->dataType == "test_numbers") {
                auto numbers = event->template getData<std::vector<int>>();
                std::cout << "  Numbers received: ";
                for (int num : numbers) {
                    std::cout << num << " ";
                }
                std::cout << std::endl;

                // 如果是点对点数据，发送处理结果
                if (!event->isBroadcast()) {
                    zexuan::plugin::publishData("processed_numbers", numbers.size(), getName(), event->source);
                }
            }
        });
    }
};

int main() {
    std::cout << "=== 简化事件系统测试 ===" << std::endl;
    
    // 创建插件实例
    auto pluginA = std::make_unique<TestPluginA>();
    auto pluginB = std::make_unique<TestPluginB>();
    
    // 初始化插件
    if (!pluginA->initialize()) {
        std::cerr << "Failed to initialize TestPluginA" << std::endl;
        return 1;
    }

    if (!pluginB->initialize()) {
        std::cerr << "Failed to initialize TestPluginB" << std::endl;
        return 1;
    }

    // 等待插件完全初始化
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    std::cout << "\n=== 发送测试事件 ===" << std::endl;

    // 现在发送测试事件
    pluginA->sendTestEvents();

    // 等待事件处理完成
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    
    std::cout << "\n=== 测试点对点控制事件 ===" << std::endl;
    
    // 发送控制事件
    zexuan::plugin::publishControl(ControlEvent::Action::PAUSE, "TestPluginB", "TestPluginA");
    zexuan::plugin::publishControl(ControlEvent::Action::RESUME, "TestPluginB", "TestPluginA");
    
    // 等待处理
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    
    // 关闭插件
    pluginA->shutdown();
    pluginB->shutdown();
    
    return 0;
}
