#include "zexuan/config_loader.hpp"
#include <iostream>

// 示例配置结构
struct ServerConfig {
    std::string host;
    int port;
    int timeout;
};

struct DatabaseConfig {
    std::string host;
    int port;
    std::string username;
    std::string password;
    struct {
        int max_size;
        int idle_timeout;
    } pool;
};

struct AppConfig {
    ServerConfig server;
    DatabaseConfig database;
};

// 加载配置的辅助函数
AppConfig loadAppConfig(std::shared_ptr<zexuan::ConfigLoader> config) {
    AppConfig app_config;
    
    // 加载服务器配置
    app_config.server.host = config->get<std::string>("server.host", "localhost");
    app_config.server.port = config->get<int>("server.port", 8080);
    app_config.server.timeout = config->get<int>("server.timeout", 30);

    // 加载数据库配置（这些是必需的配置，没有默认值）
    try {
        app_config.database.host = config->get<std::string>("database.host");
        app_config.database.port = config->get<int>("database.port");
        app_config.database.username = config->get<std::string>("database.username");
        app_config.database.password = config->get<std::string>("database.password");
    } catch (const zexuan::ConfigLoader::ConfigError& e) {
        throw std::runtime_error("Missing required database configuration: " + std::string(e.what()));
    }

    // 加载数据库连接池配置（带默认值）
    app_config.database.pool.max_size = config->get<int>("database.pool.max_size", 10);
    app_config.database.pool.idle_timeout = config->get<int>("database.pool.idle_timeout", 300);
    
    return app_config;
}

// 打印配置的辅助函数
void printAppConfig(const AppConfig& config) {
    std::cout << "Application Configuration:\n";
    std::cout << "\nServer Configuration:\n"
              << "  Host: " << config.server.host << "\n"
              << "  Port: " << config.server.port << "\n"
              << "  Timeout: " << config.server.timeout << " seconds\n";
              
    std::cout << "\nDatabase Configuration:\n"
              << "  Host: " << config.database.host << "\n"
              << "  Port: " << config.database.port << "\n"
              << "  Username: " << config.database.username << "\n"
              << "  Password: " << std::string(config.database.password.length(), '*') << "\n"
              << "  Pool:\n"
              << "    Max Size: " << config.database.pool.max_size << "\n"
              << "    Idle Timeout: " << config.database.pool.idle_timeout << " seconds\n";
}

int main() {
    try {
        auto config = zexuan::ConfigLoader::getInstance();
        
        // 1. 从字符串加载示例配置
        config->loadFromString(R"(
{
    "server": {
        "host": "api.example.com",
        "port": 8080,
        "timeout": 30
    },
    "database": {
        "host": "db.example.com",
        "port": 5432,
        "username": "admin",
        "password": "secret123",
        "pool": {
            "max_size": 20,
            "idle_timeout": 600
        }
    }
}
        )");
        
        // 2. 演示基本的配置访问
        std::cout << "Basic Configuration Access:\n";
        
        // 2.1 使用默认值
        auto api_version = config->get<std::string>("api.version", "v1");
        std::cout << "API Version: " << api_version << " (default value)\n";

        // 2.2 检查配置是否存在
        if (config->exists("server.ssl")) {
            std::cout << "SSL configuration exists\n";
        } else {
            std::cout << "SSL configuration not found\n";
        }

        // 2.3 使用可选值
        if (auto log_level = config->getOptional<std::string>("logging.level")) {
            std::cout << "Log level: " << *log_level << "\n";
        } else {
            std::cout << "Log level not specified\n";
        }
        
        std::cout << "\n";
        
        // 3. 加载完整的应用配置
        auto app_config = loadAppConfig(config);
        printAppConfig(app_config);
        
        // 4. 演示配置修改
        std::cout << "\nModifying Configuration:\n";
        
        // 4.1 修改现有值
        config->set("server.port", 9000);
        std::cout << "Updated server port: " << config->get<int>("server.port") << "\n";

        // 4.2 添加新的配置节点
        config->set("server.ssl.enabled", true);
        config->set("server.ssl.cert_file", "/path/to/cert.pem");
        std::cout << "Added SSL configuration\n";

        // 4.3 删除配置节点
        config->remove("database.password");
        std::cout << "Removed database password from configuration\n";

        // 5. 保存修改后的配置
        config->saveToFile("modified_config.json", true);
        std::cout << "\nSaved modified configuration to 'modified_config.json'\n";

        // 6. 展示格式化的配置输出
        std::cout << "\nFormatted Configuration Output:\n";
        std::cout << config->toString(true) << "\n";
        
    } catch (const zexuan::ConfigLoader::ConfigError& e) {
        std::cerr << "Configuration error: " << e.what() << std::endl;
        return 1;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
} 