#include <iostream>
#include "zexuan/logger.hpp"
#include "zexuan/base/singleton_registry.hpp"

int main() {
    try {
        // 初始化日志系统（会从配置文件加载所有设置）
        auto logger = zexuan::getSingleton<zexuan::Logger>();
        logger->init();

        // 使用日志
        zexuan::info("Application started");

        // 程序结束前关闭日志
        logger->shutdown();
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    return 0;
}