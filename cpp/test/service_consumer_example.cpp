#include "zexuan/plugin/service_registry.hpp"
#include "zexuan/services/common_services.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include <iostream>
#include <memory>

// 示例：如何在其他插件中使用服务
namespace zexuan {
namespace examples {

class ServiceConsumerPlugin : public plugin::PluginInterface {
public:
    const char* getName() const override { return "service_consumer"; }
    const char* getVersion() const override { return "1.0.0"; }
    const char* getDescription() const override { return "Service consumer example"; }
    
    bool initialize(const nlohmann::json& config = {}) override {
        // 获取服务注册表
        auto registry = zexuan::getSingleton<plugin::ServiceRegistry>();
        
        // 查找数据处理服务
        data_processor_ = registry->getService<services::DataProcessorService>("EchoDataProcessor");
        if (data_processor_) {
            std::cout << "ServiceConsumer: Found DataProcessor service" << std::endl;
        } else {
            std::cout << "ServiceConsumer: DataProcessor service not available" << std::endl;
        }
        
        state_ = plugin::PluginState::INITIALIZED;
        return true;
    }
    
    void shutdown() override {
        data_processor_.reset();
        state_ = plugin::PluginState::UNLOADED;
    }
    
    plugin::PluginState getState() const override {
        return state_;
    }
    
    // === 移除了processMessage方法 ===
    // 这个示例插件现在应该通过事件系统与其他插件通信

private:
    plugin::PluginState state_ = plugin::PluginState::UNLOADED;
    std::shared_ptr<services::DataProcessorService> data_processor_;
};

} // namespace examples
} // namespace zexuan
