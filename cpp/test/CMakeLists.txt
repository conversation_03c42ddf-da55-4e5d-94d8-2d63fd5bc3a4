# 配置示例
add_executable(config_example config_example.cpp)
target_link_libraries(config_example
    PRIVATE
        core
        nlohmann_json::nlohmann_json
)

# 日志示例
add_executable(logger_example loggger_example.cpp)
target_link_libraries(logger_example
    PRIVATE
        core
        spdlog::spdlog
)

# 数据库连接池示例
add_executable(soci_connpool_example soci_connpool_example.cpp)
target_link_libraries(soci_connpool_example
    PRIVATE
        core
        soci::soci
)

# SOCI基础示例
add_executable(soci_example soci_example.cpp)
target_link_libraries(soci_example
    PRIVATE
        core
        soci::soci
)

# 线程池分块示例
add_executable(thread_pool_blocks_example thread_pool_blocks_example.cpp)
target_link_libraries(thread_pool_blocks_example
    PRIVATE
        core
)

# 工厂模式示例
add_executable(factory factory.cpp)
target_link_libraries(factory
    PRIVATE
        core
)

# 简化事件系统测试
add_executable(simplified_event_system_test simplified_event_system_test.cpp)
target_link_libraries(simplified_event_system_test
    PRIVATE
        core
        plugin_interface
)
target_include_directories(simplified_event_system_test PRIVATE
    ${CMAKE_SOURCE_DIR}/core/include
    ${CMAKE_SOURCE_DIR}/interface/include
)
set_target_properties(simplified_event_system_test PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)

# 单例注册表测试
add_executable(singleton_registry_test singleton_registry_test.cpp)
target_link_libraries(singleton_registry_test
    PRIVATE
        core
)
set_target_properties(singleton_registry_test PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
)


# 设置所有示例的输出目录
set_target_properties(
    config_example
    logger_example
    soci_connpool_example
    soci_example
    thread_pool_blocks_example
    factory
    simplified_event_system_test
    singleton_registry_test
    PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/bin/test"
)