/**
 * @file simplified_usage_example.cpp
 * @brief 展示简化后的ConfigLoader和Logger使用方式
 */

#include "zexuan/config_loader.hpp"
#include "zexuan/logger.hpp"
#include <iostream>

using namespace zexuan;

void demonstrateConfigLoader() {
    std::cout << "\n=== ConfigLoader 使用示例 ===\n";
    
    // 方式1：直接创建实例（推荐）
    ConfigLoader config("config/app.json");
    
    // 获取配置值
    std::string db_host = config.get<std::string>("database.host", "localhost");
    int db_port = config.get<int>("database.port", 3306);
    bool debug_mode = config.get<bool>("debug", false);
    
    std::cout << "Database Host: " << db_host << std::endl;
    std::cout << "Database Port: " << db_port << std::endl;
    std::cout << "Debug Mode: " << (debug_mode ? "true" : "false") << std::endl;
    
    // 方式2：创建多个配置实例
    ConfigLoader user_config("config/user.json");
    ConfigLoader system_config("config/system.json");
    
    // 方式3：使用全局配置（可选）
    initGlobalConfig("config/global.json");
    std::string app_name = getGlobalConfigValue<std::string>("app.name", "MyApp");
    std::cout << "App Name: " << app_name << std::endl;
}

void demonstrateLogger() {
    std::cout << "\n=== Logger 使用示例 ===\n";
    
    // 方式1：使用全局日志接口（推荐）
    initGlobalLogger("config/logger.json");
    
    // 直接使用便捷函数
    info("应用程序启动");
    debug("调试信息: {}", 42);
    warn("警告信息");
    error("错误信息");
    
    // 方式2：获取命名logger
    auto file_logger = getNamedLogger("file_operations");
    file_logger->info("文件操作日志");
    
    // 方式3：直接创建Logger实例
    Logger custom_logger;
    custom_logger.init("config/custom_logger.json");
    auto console_logger = custom_logger.getDefaultLogger();
    console_logger->info("自定义logger消息");
    
    shutdownGlobalLogger();
}

void demonstrateFlexibility() {
    std::cout << "\n=== 灵活性展示 ===\n";
    
    // 可以创建多个配置实例用于不同目的
    ConfigLoader app_config;
    ConfigLoader user_config;
    ConfigLoader temp_config;
    
    // 从不同来源加载配置
    app_config.loadFromFile("app.json");
    user_config.loadFromString(R"({"theme": "dark", "language": "zh-CN"})");
    temp_config.loadFromJson(nlohmann::json{{"temp", true}});
    
    // 可以创建多个Logger实例用于不同模块
    Logger main_logger;
    Logger network_logger;
    Logger database_logger;
    
    main_logger.init("config/main_logger.json");
    network_logger.init("config/network_logger.json");
    database_logger.init("config/db_logger.json");
    
    std::cout << "创建了多个独立的配置和日志实例" << std::endl;
}

void demonstrateTestability() {
    std::cout << "\n=== 可测试性展示 ===\n";
    
    // 在测试中可以轻松创建临时配置
    ConfigLoader test_config;
    test_config.loadFromString(R"({
        "test_mode": true,
        "database": {
            "host": "test_db",
            "port": 5432
        }
    })");
    
    // 验证配置
    assert(test_config.get<bool>("test_mode") == true);
    assert(test_config.get<std::string>("database.host") == "test_db");
    
    // 在测试中可以创建独立的Logger
    Logger test_logger;
    // 不需要配置文件，使用默认设置
    test_logger.init("");
    
    auto logger = test_logger.getDefaultLogger();
    logger->info("测试日志消息");
    
    std::cout << "测试配置和日志创建成功" << std::endl;
}

int main() {
    std::cout << "简化后的ConfigLoader和Logger使用示例\n";
    std::cout << "========================================\n";
    
    try {
        demonstrateConfigLoader();
        demonstrateLogger();
        demonstrateFlexibility();
        demonstrateTestability();
        
        std::cout << "\n所有示例执行完成！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
