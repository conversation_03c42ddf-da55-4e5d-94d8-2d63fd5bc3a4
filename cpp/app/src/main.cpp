#include "system_initializer.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include <iostream>
#include <exception>

using namespace zexuan;

/**
 * 演示 SingletonRegistry 的基本使用
 */
void demonstrateBasicUsage() {
    std::cout << "\n=== SingletonRegistry 基本使用演示 ===" << std::endl;

    // 1. 通过 SingletonRegistry 直接获取
    auto& registry = SingletonRegistry::getInstance();
    auto logger1 = registry.get<Logger>();
    auto config1 = registry.get<ConfigLoader>();

    std::cout << "✓ 通过 SingletonRegistry 获取实例成功" << std::endl;

    // 2. 通过便捷函数获取
    auto logger2 = getSingleton<Logger>();
    auto config2 = getSingleton<ConfigLoader>();

    std::cout << "✓ 通过便捷函数获取实例成功" << std::endl;

    // 3. 通过兼容性接口获取
    auto logger3 = Logger::getInstance();
    auto config3 = ConfigLoader::getInstance();

    std::cout << "✓ 通过兼容性接口获取实例成功" << std::endl;

    // 4. 验证都是同一个实例
    assert(logger1 == logger2);
    assert(logger2 == logger3);
    assert(config1 == config2);
    assert(config2 == config3);

    std::cout << "✓ 所有获取方式返回相同实例" << std::endl;
    std::cout << "  当前注册表中有 " << registry.size() << " 个单例实例" << std::endl;
}

/**
 * 演示实际的日志和配置使用
 */
void demonstrateRealUsage() {
    std::cout << "\n=== 实际使用演示 ===" << std::endl;

    try {
        // 1. 获取配置加载器并加载配置
        auto config = getSingleton<ConfigLoader>();

        // 创建一个简单的配置用于演示
        config->loadFromString(R"({
            "logger": {
                "level": "info",
                "pattern": "[%Y-%m-%d %H:%M:%S.%e] [%n] [%^%l%$] %v",
                "log_dir": "logs",
                "max_file_size": 5242880,
                "max_files": 3,
                "async": false
            },
            "app": {
                "name": "SingletonRegistry Demo",
                "version": "1.0.0"
            }
        })");

        std::cout << "✓ 配置加载成功" << std::endl;

        // 2. 获取日志器并使用
        auto logger = getSingleton<Logger>();

        // 获取默认控制台日志器
        auto console_logger = logger->getDefaultLogger();
        if (console_logger) {
            console_logger->info("SingletonRegistry 系统初始化完成");
            console_logger->info("应用名称: {}", config->get<std::string>("app.name", "Unknown"));
            console_logger->info("应用版本: {}", config->get<std::string>("app.version", "Unknown"));
        }

        // 获取文件日志器
        auto file_logger = logger->getLogger("app");
        if (file_logger) {
            file_logger->info("这是写入文件的日志消息");
            file_logger->warn("SingletonRegistry 工作正常！");
        }

        std::cout << "✓ 日志系统使用成功" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "实际使用演示失败: " << e.what() << std::endl;
    }
}

/**
 * 演示线程安全性
 */
void demonstrateThreadSafety() {
    std::cout << "\n=== 线程安全演示 ===" << std::endl;

    const int numThreads = 10;
    const int numIterations = 100;
    std::vector<std::thread> threads;
    std::vector<std::shared_ptr<Logger>> loggers(numThreads);

    // 启动多个线程同时获取 Logger 单例
    for (int i = 0; i < numThreads; ++i) {
        threads.emplace_back([&loggers, i, numIterations]() {
            for (int j = 0; j < numIterations; ++j) {
                auto logger = getSingleton<Logger>();
                if (j == 0) {
                    loggers[i] = logger;
                }
                // 验证每次获取的都是同一个实例
                assert(logger == loggers[i]);
            }
        });
    }

    // 等待所有线程完成
    for (auto& thread : threads) {
        thread.join();
    }

    // 验证所有线程获取的都是同一个实例
    for (int i = 1; i < numThreads; ++i) {
        assert(loggers[0] == loggers[i]);
    }

    std::cout << "✓ 线程安全测试通过：" << numThreads << " 个线程，每个线程 "
              << numIterations << " 次迭代，所有实例都相同" << std::endl;
}

int main() {
    std::cout << "=== SingletonRegistry 应用演示 ===" << std::endl;

    try {
        // 1. 初始化系统（使用 RAII 管理器）
        SystemManager systemManager;
        std::cout << "✓ 系统初始化完成" << std::endl;

        // 2. 演示基本使用
        demonstrateBasicUsage();

        // 3. 演示实际使用场景
        demonstrateRealUsage();

        // 4. 演示线程安全
        demonstrateThreadSafety();

        std::cout << "\n🎉 所有演示完成！SingletonRegistry 工作正常。" << std::endl;
        std::cout << "系统将在 SystemManager 析构时自动清理资源。" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ 程序异常: 未知异常" << std::endl;
        return 1;
    }

    return 0;
}
