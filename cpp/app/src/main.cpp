#include "system_initializer.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include <iostream>
#include <exception>

using namespace zexuan;

int main() {
    std::cout << "=== SingletonRegistry 应用演示 ===" << std::endl;

    try {
        // 1. 初始化系统（使用 RAII 管理器）
        SystemManager systemManager;
        std::cout << "✓ 系统初始化完成" << std::endl;

    
    } catch (const std::exception& e) {
        std::cerr << "❌ 程序异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ 程序异常: 未知异常" << std::endl;
        return 1;
    }

    return 0;
}
