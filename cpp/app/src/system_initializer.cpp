#include "system_initializer.hpp"

namespace zexuan {

void SystemInitializer::initialize() {
    auto& registry = SingletonRegistry::getInstance();

    // 1. 首先创建 ConfigLoader 实例
    auto config_loader = registry.get<ConfigLoader>();

    // 2. 然后创建 Logger 实例
    auto logger = registry.get<Logger>();

    // 3. 显式初始化 Logger（避免构造函数中的循环依赖）
    try {
        logger->init("config/config.json");
    } catch (const std::exception& e) {
        // 如果配置文件不存在，使用默认配置
        std::cerr << "Warning: Failed to load config file, using defaults: " << e.what() << std::endl;
        // Logger 会使用默认配置继续工作
    }
}

void SystemInitializer::cleanup() {
    // 在程序正常退出前显式清理 Logger，避免析构顺序问题
    try {
        if (SingletonRegistry::getInstance().exists<Logger>()) {
            auto logger = getSingleton<Logger>();
            logger->shutdown();
        }
    } catch (const std::exception& e) {
        std::cerr << "Logger cleanup warning: " << e.what() << std::endl;
    }

    // 可选：清理整个注册表
    // SingletonRegistry::getInstance().clear();
}

} // namespace zexuan
