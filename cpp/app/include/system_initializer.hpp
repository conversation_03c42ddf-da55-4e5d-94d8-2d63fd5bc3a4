#pragma once

#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include "zexuan/base/noncopyable.hpp"
#include <thread>
#include <chrono>
#include <iostream>

namespace zexuan {

/**
 * 系统初始化器 - 自动注册核心单例
 */
class SystemInitializer {
public:
    /**
     * 初始化系统单例注册表
     */
    static void initialize();

    /**
     * 清理系统
     */
    static void cleanup();
};

/**
 * RAII风格的系统管理器
 */
class SystemManager : public noncopyable {
public:
    SystemManager() {
        SystemInitializer::initialize();
    }

    ~SystemManager() {
        SystemInitializer::cleanup();
    }
};

} // namespace zexuan