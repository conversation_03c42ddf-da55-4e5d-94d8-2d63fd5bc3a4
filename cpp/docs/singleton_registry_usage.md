# 单例注册表使用指南

## 概述

单例注册表采用**惰性初始化模式**，支持跨动态库的单例管理。本文档说明正确的使用方式。

## 核心设计原则

### 1. 惰性初始化
- **注册时**：只标记类型，不创建实例
- **首次访问时**：才真正构造单例实例
- **后续访问**：直接返回缓存的实例

### 2. 跨库唯一性保证
- 主程序统一管理所有单例的注册
- 所有动态库通过同一个注册表实例访问单例
- 使用 `singleton_registry()` 函数获取统一的注册表

## 正确使用模式

### 主程序初始化

```cpp
#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"

int main() {
    // 1. 设置跨库访问器（可选，用于插件环境）
    zexuan::setSingletonRegistryAccessor([]() -> zexuan::SingletonRegistry& {
        return zexuan::SingletonRegistry::getInstance();
    });
    
    // 2. 主程序统一注册所有需要跨库共享的单例
    auto& registry = zexuan::singleton_registry();
    
    registry.registerSingleton<zexuan::ConfigLoader>();
    registry.registerSingleton<zexuan::Logger>();
    // 注册其他需要跨库共享的单例...
    
    // 3. 正常使用单例
    auto logger = zexuan::getSingleton<zexuan::Logger>();
    auto config = zexuan::getSingleton<zexuan::ConfigLoader>();
    
    // ... 程序逻辑
    
    return 0;
}
```

### 插件中使用

```cpp
// 插件代码
#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/logger.hpp"

void plugin_function() {
    // 插件通过同一个注册表访问单例
    auto logger = zexuan::getSingleton<zexuan::Logger>();
    
    // 使用logger...
    auto console_logger = logger->getDefaultLogger();
    console_logger->info("插件：使用主程序的Logger实例");
}
```

## 关键要点

### ✅ 正确做法

1. **主程序负责注册**：所有需要跨库共享的单例都在主程序中注册
2. **统一访问接口**：所有库都通过 `getSingleton<T>()` 访问
3. **惰性初始化**：首次访问时才构造实例，避免循环依赖
4. **跨库访问器**：在插件环境中设置访问器确保使用同一注册表

### ❌ 错误做法

1. **插件中注册单例**：可能导致不同库有不同的实例
2. **直接调用 T::getInstance()**：绕过了统一管理
3. **多个注册表实例**：破坏了单例的唯一性
4. **忘记设置跨库访问器**：插件可能使用独立的注册表

## 技术细节

### typeid(T).name() 的使用

- **优点**：简单直接，编译时确定
- **注意**：在同一进程内是唯一的，但需要确保所有库使用同一注册表实例
- **保证**：通过 `singleton_registry()` 函数确保访问同一实例

### 线程安全

- 使用 `std::mutex` 保护注册表操作
- 惰性初始化时的构造过程是线程安全的
- 多线程环境下的首次访问会正确同步

### 内存管理

- 使用 `std::shared_ptr` 管理单例生命周期
- 自定义删除器确保不会删除单例对象
- 注册表清理时会正确释放所有引用

## 性能特性

### 注册性能
- **极快**：只存储nullptr占位符
- **无开销**：不创建实例，不调用构造函数

### 访问性能
- **首次访问**：需要构造实例，稍慢
- **后续访问**：直接返回缓存实例，极快
- **锁开销**：普通锁，开销很小

## 故障排除

### 问题：插件获取的单例与主程序不同

**原因**：插件使用了独立的注册表实例

**解决**：
1. 确保主程序调用了 `setSingletonRegistryAccessor()`
2. 插件通过 `singleton_registry()` 访问注册表
3. 检查链接配置，确保符号正确导出

### 问题：单例未注册错误

**原因**：主程序忘记注册某个单例类型

**解决**：
1. 在主程序初始化时添加 `registry.registerSingleton<T>()`
2. 确保注册在首次访问之前完成

### 问题：循环依赖

**原因**：单例构造函数中访问了未注册的其他单例

**解决**：
1. 确保所有依赖的单例都已注册
2. 利用惰性初始化的特性，注册顺序不重要
3. 检查构造函数中的依赖关系

## 最佳实践

1. **集中注册**：在主程序的一个地方统一注册所有单例
2. **文档化**：记录哪些单例需要跨库共享
3. **测试验证**：编写测试确保跨库单例唯一性
4. **错误处理**：妥善处理单例未注册的情况
5. **生命周期管理**：在程序结束时正确清理注册表

## 总结

惰性初始化的单例注册表提供了简洁、高效、安全的跨库单例管理方案。关键是要理解**主程序统一注册，所有库统一访问**的使用模式。正确使用可以完全避免循环依赖问题，同时保证跨库的单例唯一性。
