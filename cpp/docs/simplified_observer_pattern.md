# 简化的Observer/Subject/Mediator模式实现

## 概述

本文档描述了基于IEC 60870-5-103协议的简化Observer/Subject/Mediator模式实现，该实现使用Message内部地址进行自动路由。

## 核心改进

### 1. 统一的Message类
- 基于IEC 60870-5-103协议规范
- 包含完整的路由信息：`source_`（源地址）和`target_`（目标地址）
- 支持文本内容传输和序列化/反序列化

### 2. 简化的消息路由
- **之前**：`sendMessage(message, targetId, sourceId)`
- **现在**：`sendMessage(message)` - 路由信息包含在Message内部

### 3. 自动地址设置
- Subject发送消息时自动设置`source_`字段为自己的ID
- 目标地址从Message的`target_`字段获取

## 类结构

### Message类
```cpp
class Message {
private:
    uint8_t typ_;        // 类型标识
    uint8_t vsq_;        // 可变结构限定词
    uint8_t cot_;        // 传送原因
    uint8_t source_;     // 源地址
    uint8_t target_;     // 目标地址
    uint8_t fun_;        // 功能类型
    uint8_t inf_;        // 信息序号
    std::vector<uint8_t> variableStructure_; // 可变结构体
};
```

### Observer接口
```cpp
class Observer {
public:
    virtual void onNotify(Subject* subject, const Message& message) = 0;
    virtual int getId() const = 0;
    virtual std::string getDescription() const = 0;
};
```

### Subject接口
```cpp
class Subject {
public:
    virtual void attach(std::shared_ptr<Observer> observer) = 0;
    virtual void detach(std::shared_ptr<Observer> observer) = 0;
    virtual void notify(const Message& message) = 0;
    virtual int sendMessage(const Message& message) = 0; // 简化接口
};
```

### Mediator接口
```cpp
class Mediator {
public:
    virtual bool initialize() = 0;
    virtual bool registerObserver(int observerId, Observer* observer, std::string& errorMsg) = 0;
    virtual bool unregisterObserver(int observerId, std::string& errorMsg) = 0;
    virtual int sendMessage(const Message& message, std::string& description) = 0; // 简化接口
};
```

## 使用示例

### 1. 创建和配置系统
```cpp
// 创建Mediator
auto mediator = std::make_shared<BaseMediator>();
mediator->initialize();

// 创建Observer
auto observer = std::make_shared<TestObserver>(10, "温度监控器");
mediator->registerObserver(10, observer.get(), errorMsg);

// 创建Subject
auto subject = std::make_shared<TestSubject>(100, "设备控制器");
subject->setMediator(mediator);
```

### 2. 发送消息
```cpp
// 创建消息
Message msg(0x01, 0x81, 0x03, 0, 0xB2, 0x0E);
msg.setTarget(10);  // 设置目标地址
msg.setTextContent("温度设置为25°C");

// 发送消息（source会自动设置为subject的ID）
subject->sendMessage(msg);
```

### 3. 直接通过Mediator发送
```cpp
Message directMsg(0x02, 0x01, 0x06, 200, 0xC0, 0x01);  // source=200
directMsg.setTarget(30);  // target=30
directMsg.setTextContent("直接消息内容");

std::string description;
mediator->sendMessage(directMsg, description);
```

## 优势

### 1. 简化的API
- 消息发送方法只需要一个Message参数
- 路由信息封装在Message内部
- 减少了方法参数的复杂性

### 2. 自动路由
- 基于Message内部的source和target字段
- 发送方自动设置源地址
- Mediator根据目标地址自动路由

### 3. 协议兼容
- 完全符合IEC 60870-5-103协议规范
- 支持标准的ASDU结构
- 可扩展的可变结构体

### 4. 类型安全
- 使用uint8_t确保地址范围正确
- 编译时类型检查
- 异常安全的实现

## 错误处理

系统提供完善的错误处理机制：
- 目标Observer不存在时返回错误码
- 详细的错误描述信息
- 异常安全的消息传递

## 性能特点

- **内存效率**：Message结构紧凑，固定头部仅7字节
- **线程安全**：使用mutex保护共享资源
- **零拷贝**：消息传递使用引用，避免不必要的拷贝
- **可扩展**：支持任意长度的可变结构体数据

## 总结

简化后的Observer/Subject/Mediator模式实现提供了：
1. 更简洁的API接口
2. 基于协议标准的消息结构
3. 自动化的消息路由机制
4. 完善的错误处理和类型安全

这个实现特别适合需要标准化消息格式和简化API的工业控制系统。
