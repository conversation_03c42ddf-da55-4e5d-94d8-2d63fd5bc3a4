# MessageMediator 更新说明

## 概述

MessageMediator类已更新为使用简化的Observer/Subject/Mediator模式和新的Message类结构。

## 主要更新

### 1. 移除的功能
- ❌ 移除了所有EventMessage相关的方法和处理逻辑
- ❌ 移除了复杂的事件类型过滤逻辑
- ❌ 移除了旧的Message API（getType(), getContent(), getSourceId()）
- ❌ 移除了便利的消息创建方法（createMessage, sendTextMessage）

### 2. 更新的核心功能

#### 消息发送接口简化
```cpp
// 旧接口（已移除）
int sendMessageToObserver(int observerId, const Message& message, 
                         std::string& description, int sourceId);

// 新接口
int sendMessage(const Message& message, std::string& description);
```

#### 自动路由机制
- 消息路由完全基于Message内部的`source_`和`target_`字段
- 不再需要手动指定目标Observer ID
- MessageMediator自动从Message.getTarget()获取路由目标

### 3. 增强的统计功能

```cpp
struct CommunicationStats {
    size_t observerCount;     // 注册的Observer数量
    size_t messagesSent;      // 成功发送的消息数量
    size_t messagesReceived;  // 接收的消息数量
    size_t routingErrors;     // 路由错误数量
};
```

### 4. 保留的功能
- ✅ Observer注册/注销功能
- ✅ 插件名称列表获取
- ✅ 统计信息收集和重置
- ✅ 单例模式实现
- ✅ 线程安全的操作

## 使用方式

### 基本使用
```cpp
// 获取MessageMediator实例
auto& mediator = MessageMediator::getInstance();

// 注册Observer
std::string errorMsg;
mediator.registerObserver(10, observer.get(), errorMsg);

// 创建并发送消息
Message msg(0x01, 0x01, 0x03, 10, 0xB2, 0x0E);  // source=10
msg.setTarget(20);  // target=20
msg.setTextContent("Hello, Plugin!");

std::string description;
int result = mediator.sendMessage(msg, description);
```

### 统计信息查看
```cpp
auto stats = mediator.getStats();
std::cout << "发送成功: " << stats.messagesSent << std::endl;
std::cout << "路由错误: " << stats.routingErrors << std::endl;
```

## 向后兼容性

### 破坏性变更
- 移除了所有EventMessage相关的API
- 移除了便利的消息创建方法
- 消息发送接口参数发生变化

### 迁移指南
1. **消息创建**: 直接使用Message构造函数
   ```cpp
   // 旧方式（已移除）
   auto msg = mediator.createMessage(typ, source, target, fun, inf, content);
   
   // 新方式
   Message msg(typ, vsq, cot, source, fun, inf);
   msg.setTarget(target);
   msg.setTextContent(content);
   ```

2. **消息发送**: 使用简化的sendMessage接口
   ```cpp
   // 旧方式（已移除）
   mediator.sendTextMessage(source, target, content, description);
   
   // 新方式
   Message msg(0x01, 0x01, 0x03, source, 0xB2, 0x0E);
   msg.setTarget(target);
   msg.setTextContent(content);
   mediator.sendMessage(msg, description);
   ```

## 优势

1. **简化的API**: 消息路由逻辑更加清晰
2. **协议兼容**: 完全符合IEC 60870-5-103标准
3. **自动路由**: 基于Message内部地址，减少参数传递
4. **更好的统计**: 提供详细的通信统计信息
5. **类型安全**: 使用标准的Message结构

## 性能特点

- **内存效率**: Message结构紧凑，7字节固定头部
- **线程安全**: 使用mutex保护共享资源
- **错误处理**: 完善的错误统计和日志记录
- **可扩展性**: 支持任意长度的消息内容

MessageMediator现在提供了一个更加简洁、高效且符合标准的插件间通信机制。
