# Example programs


# SingletonRegistry 演示程序（推荐的轻量级方案）
add_executable(singleton_registry_demo
    singleton_registry_demo.cpp
)

target_link_libraries(singleton_registry_demo
    PRIVATE
    core
)

# 配置示例
add_executable(plugin_test 
    plugin_test.cpp)

target_link_libraries(plugin_test 
    PRIVATE
    plugin_interface
    ${CMAKE_DL_LIBS} # 动态库加载支持
)

# 设置所有示例的输出目录
set_target_properties(
    singleton_registry_demo
    plugin_test 
    PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/bin/example"
)