#include "zexuan/plugin/plugin_communication.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/config_loader.hpp"
#include "zexuan/logger.hpp"
#include <iostream>
#include <memory>
#include <vector>
#include <string>
#include <filesystem>
#include <thread>
#include <chrono>

using namespace zexuan::plugin;
using namespace zexuan;

/**
 * 从配置文件加载插件
 */
bool loadPluginsFromConfig() {
    try {
        // 从SingletonRegistry获取配置服务
        auto config = getSingleton<ConfigLoader>();
        config->loadFromFile("config/config.json");

        // 使用统一的插件API

        // 获取应用程序logger
        auto logger = getSingleton<Logger>()->getLogger("zexuan.app");

        // 获取插件目录
        std::string pluginDir = config->get<std::string>("plugin_directory", "../libs/plugins");
        logger->info("插件目录: {}", pluginDir);

        // 检查插件目录是否存在
        if (!std::filesystem::exists(pluginDir)) {
            logger->warn("插件目录不存在，尝试创建: {}", pluginDir);
            std::filesystem::create_directories(pluginDir);
        }

        // 获取插件列表
        auto plugins = config->getRawJson()["plugins"];
        if (!plugins.is_array()) {
            logger->error("配置文件中没有找到插件数组");
            return false;
        }

        logger->info("开始加载插件...");
        bool success = true;
        int loadedCount = 0;
        
        for (const auto& plugin : plugins) {
            if (!plugin.contains("name") || !plugin.contains("path")) {
                logger->warn("跳过无效的插件配置项");
                continue;
            }

            // 检查插件是否启用
            bool enabled = plugin.value("enabled", true);
            if (!enabled) {
                logger->info("跳过已禁用的插件: {}", plugin["name"].get<std::string>());
                continue;
            }
            
            std::string name = plugin["name"];
            std::string path = plugin["path"];
            std::string description = plugin.value("description", "");
            
            // 构建完整路径
            std::filesystem::path fullPath = std::filesystem::path(pluginDir) / path;
            std::string libPath = fullPath.string();
            
            if (!description.empty()) {
                logger->info("加载插件: {} ({})", name, description);
            } else {
                logger->info("加载插件: {}", name);
            }
            logger->debug("  路径: {}", libPath);

            // 检查文件是否存在
            if (!std::filesystem::exists(fullPath)) {
                logger->error(" - 文件不存在!");
                success = false;
                continue;
            }

            // 加载动态库
            if (loadPlugin(libPath)) {
                logger->info(" - 成功");
                loadedCount++;
            } else {
                logger->error(" - 失败");
                success = false;
            }
        }
        
        logger->info("插件加载完成，成功加载 {} 个插件", loadedCount);
        return success;

    } catch (const std::exception& e) {
        auto logger = getSingleton<Logger>()->getLogger("zexuan.app");
        logger->error("加载配置文件失败: {}", e.what());
        return false;
    }
}

/**
 * 设置通信系统的过滤器和转换器
 */
void setupCommunicationFilters() {
    // 过滤器和转换器功能已简化，这里只做初始化确认
    auto logger = getSingleton<Logger>()->getLogger("zexuan.app");
    logger->info("通信系统设置完成");
}

/**
 * 演示插件间通信
 */
void demonstrateInterPluginCommunication() {
    auto logger = getSingleton<Logger>()->getLogger("zexuan.app");
    logger->info("=== 插件间通信演示 ===");

    auto activePlugins = getActivePlugins();
    if (activePlugins.size() < 2) {
        logger->warn("需要至少2个插件来演示通信功能");
        return;
    }

    // 等待插件初始化完成
    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    logger->info("1. 测试消息广播...");
    publishMessage("Hello all plugins! This is a broadcast message.", "System");

    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    logger->info("2. 测试点对点消息...");
    if (activePlugins.size() >= 2) {
        std::string plugin1 = activePlugins[0]->getName();
        std::string plugin2 = activePlugins[1]->getName();

        publishMessage("Direct message to " + plugin1, "System", plugin1);
        publishMessage("Direct message to " + plugin2, "System", plugin2);
    }

    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    logger->info("3. 测试数据事件...");
    std::vector<int> testData = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
    publishData("numbers", testData, "System");

    std::map<std::string, double> systemStats = {
        {"cpu_usage", 45.2},
        {"memory_usage", 78.5},
        {"network_io", 123.4}
    };
    publishData("system_stats", systemStats, "System");

    std::this_thread::sleep_for(std::chrono::milliseconds(500));

    logger->info("4. 测试控制事件...");
    publishControl(zexuan::core::ControlEvent::Action::PAUSE, "", "System");
    std::this_thread::sleep_for(std::chrono::milliseconds(200));
    publishControl(zexuan::core::ControlEvent::Action::RESUME, "", "System");

    std::this_thread::sleep_for(std::chrono::milliseconds(500));
}

/**
 * 增强的插件系统演示
 */
void pluginSystemDemo() {
    // 获取logger（SystemManager已在main函数中初始化）
    auto logger = getSingleton<Logger>()->getLogger("zexuan.app");
    logger->info("=== 增强插件系统演示 ===");
    logger->info("SingletonRegistry 已初始化，使用统一单例管理");

    // 1. 初始化通信系统
    logger->info("初始化通信系统...");
    initialize();

    // 2. 设置通信过滤器
    setupCommunicationFilters();

    // 3. 从配置文件加载插件
    logger->info("正在加载配置文件...");
    if (!loadPluginsFromConfig()) {
        logger->warn("插件加载失败，但系统将继续运行...");
    }

    // 4. 显示所有活跃的插件
    auto activePlugins = getActivePlugins();
    logger->info("活跃插件列表：");

    if (activePlugins.empty()) {
        logger->warn("  没有找到活跃插件");
    } else {
        for (const auto& plugin : activePlugins) {
            logger->info("  - {} (v{})", plugin->getName(), plugin->getVersion());
        }
    }

    // 6. 演示插件间通信
    if (!activePlugins.empty()) {
        demonstrateInterPluginCommunication();
    }
    
    // 7. 测试插件功能
    logger->info("=== 测试插件功能 ===");
    std::cout << "\n=== 测试插件功能 ===" << std::endl;

    // 测试所有活跃插件
    for (const auto& plugin : activePlugins) {
        logger->info("测试插件: {}", plugin->getName());
        std::cout << "\n--- 测试插件: " << plugin->getName() << " ---" << std::endl;
        std::cout << "插件信息：" << std::endl;
        std::cout << "  名称: " << plugin->getName() << std::endl;
        std::cout << "  版本: " << plugin->getVersion() << std::endl;
        std::cout << "  描述: " << plugin->getDescription() << std::endl;

        // 根据不同插件类型测试不同功能
        std::vector<std::string> testMessages;
        std::string pluginName = plugin->getName();

        if (pluginName == "EchoPlugin") {
            testMessages = {
                "Hello EchoPlugin!",
                "/reply ChatPlugin Hello from Echo!",
                "Simple echo test"
            };
        } else if (pluginName == "ChatPlugin") {
            testMessages = {
                "Hello ChatPlugin!",
                "/send EchoPlugin Hello from Chat!",
                "/broadcast System message from Chat"
            };
        } else if (pluginName == "DataProcessorPlugin") {
            testMessages = {
                "Hello DataProcessor!",
                "process_data",
                "data analysis request"
            };
        } else {
            // 默认测试消息
            testMessages = {
                "Hello " + pluginName + "!",
                "test message"
            };
        }

        std::cout << "功能测试：" << std::endl;
        for (const auto& msg : testMessages) {
            try {
                std::cout << "  输入: \"" << msg << "\"" << std::endl;
                logger->debug("发送消息到 {}: {}", pluginName, msg);

                // 使用事件系统发送消息，而不是processMessage
                zexuan::plugin::publishMessage(msg, "System", pluginName);
                std::cout << "  消息已通过事件系统发送到 " << pluginName << std::endl;
                logger->debug("通过事件系统发送消息到 {}: {}", pluginName, msg);

                // 给异步事件处理时间
                std::this_thread::sleep_for(std::chrono::milliseconds(300));

            } catch (const std::exception& e) {
                logger->error("测试插件 {} 时出错: {}", pluginName, e.what());
                std::cout << "  错误: " << e.what() << std::endl;
            }
        }

        std::cout << "  --- 插件测试完成 ---" << std::endl;
    }
    
    // 8. 显示系统统计信息
    logger->info("=== 系统统计 ===");
    logger->info("活跃插件数量: {}", activePlugins.size());

    auto registeredPlugins = getRegisteredParticipants();
    logger->info("已注册插件数量: {}", registeredPlugins.size());

    // 显示通信系统统计
    logger->info("通信系统运行正常");

    // 9. 等待一段时间让异步事件处理完成
    logger->info("等待异步事件处理完成...");
    std::this_thread::sleep_for(std::chrono::seconds(2));

    // 10. 清理资源
    logger->info("清理系统资源...");
    zexuan::plugin::api::shutdown();
}

int main() {
    try {
        // 0. 首先初始化SingletonRegistry（自动注册核心服务）
        SystemManager systemManager;
        std::cout << "SingletonRegistry 初始化完成" << std::endl;

        // 1. 运行插件系统演示
        pluginSystemDemo();

        // 2. 从SingletonRegistry获取logger
        auto logger = getSingleton<Logger>()->getLogger("zexuan.app");
        logger->info("=== 演示结束 ===");

        return 0;
    } catch (const std::exception& e) {
        std::cerr << "程序异常退出: " << e.what() << std::endl;
        return 1;
    }
}