#include "zexuan/factory.hpp"
#include <iostream>
#include <memory>

// 示例：定义一个基类接口
class Animal {
public:
    virtual ~Animal() = default;
    virtual void makeSound() const = 0;
    virtual const char* getName() const = 0;
};

// 示例：实现具体的动物类
class Dog : public Animal {
public:
    void makeSound() const override {
        std::cout << "Woof! Woof!" << std::endl;
    }
    
    const char* getName() const override {
        return "Dog";
    }
};

class Cat : public Animal {
public:
    void makeSound() const override {
        std::cout << "Meow! Meow!" << std::endl;
    }
    
    const char* getName() const override {
        return "Cat";
    }
};

class Bird : public Animal {
public:
    Bird(const std::string& species = "Unknown") : species_(species) {}
    
    void makeSound() const override {
        std::cout << "Tweet! Tweet!" << std::endl;
    }
    
    const char* getName() const override {
        return species_.c_str();
    }
    
private:
    std::string species_;
};

// 定义动物工厂
typedef zexuan::SingletonFactory<Animal> AnimalFactory;

// 使用宏自动注册动物类
ZEXUAN_AUTO_REGISTER(AnimalFactory, Dog)
ZEXUAN_AUTO_REGISTER(AnimalFactory, Cat)
ZEXUAN_AUTO_REGISTER_WITH_STRING(AnimalFactory, Bird, "Sparrow")

// 使用带参数的注册（需要手动注册，因为有构造参数）
static auto registerEagle = []() {
    return AnimalFactory::getInstance().registerClass("Eagle", []() {
        return std::make_shared<Bird>("Eagle");
    });
}();

// 示例使用函数
void factoryExample() {
    std::cout << "=== 工厂模式使用示例 ===" << std::endl;
    
    // 获取所有已注册的动物
    auto registeredAnimals = AnimalFactory::getInstance().getRegisteredNames();
    std::cout << "已注册的动物类型：";
    for (const auto& name : registeredAnimals) {
        std::cout << name << " ";
    }
    std::cout << std::endl << std::endl;
    
    // 创建不同类型的动物
    std::vector<std::string> animalTypes = {"Dog", "Cat", "Sparrow", "Eagle"};
    
    for (const auto& type : animalTypes) {
        auto animal = AnimalFactory::getInstance().create(type);
        if (animal) {
            std::cout << "创建了 " << animal->getName() << ": ";
            animal->makeSound();
        } else {
            std::cout << "无法创建 " << type << std::endl;
        }
    }
    
    std::cout << std::endl;
    
    // 展示工厂统计信息
    std::cout << "工厂统计信息：" << std::endl;
    std::cout << "  已注册类型数量: " << AnimalFactory::getInstance().getRegisteredCount() << std::endl;
    std::cout << "  Dog 是否已注册: " << (AnimalFactory::getInstance().isRegistered("Dog") ? "是" : "否") << std::endl;
    std::cout << "  Lion 是否已注册: " << (AnimalFactory::getInstance().isRegistered("Lion") ? "是" : "否") << std::endl;
}

// main 函数（如果这个文件被编译为可执行文件）
#ifdef FACTORY_EXAMPLE_MAIN
int main() {
    factoryExample();
    return 0;
}
#endif
