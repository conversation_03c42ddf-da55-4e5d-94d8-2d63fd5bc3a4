#include <iostream>
#include <map>
#include <string>
#include <memory>
#include <functional>

// 简化的工厂类
class SimpleFactory {
public:
    typedef std::function<std::shared_ptr<void>()> CreateFn;
    static std::map<std::string, CreateFn> registry;
    
    static void registerClass(const std::string& name, CreateFn fn) {
        std::cout << "注册类: " << name << std::endl;
        registry[name] = fn;
    }
    
    static std::shared_ptr<void> create(const std::string& name) {
        auto it = registry.find(name);
        return (it != registry.end()) ? it->second() : nullptr;
    }
};

// 静态成员定义
std::map<std::string, SimpleFactory::CreateFn> SimpleFactory::registry;

// 简化的自动注册器
template<typename FactoryType>
class SimpleAutoRegister {
public:
    template<typename CreateFn>
    SimpleAutoRegister(const std::string& name, CreateFn createFn) {
        std::cout << "AutoRegister 构造函数被调用，注册: " << name << std::endl;
        FactoryType::registerClass(name, createFn);
    }
};

// 测试类
class TestClass {
public:
    TestClass() { std::cout << "TestClass 被创建!" << std::endl; }
};

// 关键：这些静态对象在 main() 之前就被构造了！
std::cout << "=== 程序开始，准备创建静态对象 ===" << std::endl;

static auto createFuncTestClass = []() {
    std::cout << "Lambda 创建函数被调用" << std::endl;
    return std::make_shared<TestClass>();
};

static SimpleAutoRegister<SimpleFactory> autoRegTestClass("TestClass", createFuncTestClass);

std::cout << "=== 静态对象创建完成 ===" << std::endl;

int main() {
    std::cout << "\n=== main() 函数开始执行 ===" << std::endl;
    
    // 此时 TestClass 已经被注册了！
    std::cout << "尝试创建 TestClass..." << std::endl;
    auto obj = SimpleFactory::create("TestClass");
    
    if (obj) {
        std::cout << "成功创建了对象!" << std::endl;
    } else {
        std::cout << "创建失败!" << std::endl;
    }
    
    return 0;
}
