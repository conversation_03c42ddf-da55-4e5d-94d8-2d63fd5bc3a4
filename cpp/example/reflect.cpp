#include <map>
#include <string>
#include <algorithm>
#include <memory>
#include <vector>
#include <iostream>
#include <cstring>

/**
 * 反射工厂类 - 通过类名字符串创建对象实例
 * 实现了类似 Java 反射机制的功能
 */
class ReflectFactory {
public:
    // 类型定义
    typedef void* pObj;
    typedef void* (*PFnCreateObj)(void);
    typedef std::map<std::string, PFnCreateObj> MapClassFactory;

    /**
     * 辅助注册类 - 用于自动注册类的创建函数
     */
    class Register {
    public:
        Register(const std::string& className, PFnCreateObj ptrCreateFn) {
            getInstance().mpClassFn_[className] = ptrCreateFn;
        }
    };

    /**
     * 根据类名创建对象实例
     * @param className 类名字符串
     * @return 创建的对象指针，失败返回nullptr
     */
    static pObj forName(const std::string& className) {
        pObj tmp = find(className); // 先在区分大小写集合查询
        if (tmp) {
            return tmp;
        }
        // 没找到则到不区分大小写集合内查询
        return findIgnoreCase(className);
    }

    /**
     * 获取所有已注册的类名
     */
    static std::vector<std::string> getRegisteredClasses() {
        std::vector<std::string> classNames;
        for (const auto& pair : getInstance().mpClassFn_) {
            classNames.push_back(pair.first);
        }
        return classNames;
    }

private:
    /**
     * 单例模式 - 获取工厂实例
     */
    static ReflectFactory& getInstance() {
        static ReflectFactory classFactory;
        return classFactory;
    }

    /**
     * 区分大小写查找并创建对象
     */
    static pObj find(const std::string& className) {
        auto& instance = getInstance();
        auto it = instance.mpClassFn_.find(className);
        if (it != instance.mpClassFn_.end()) {
            return it->second(); // 调用创建函数
        }
        return nullptr;
    }

    /**
     * 不区分大小写查找并创建对象
     */
    static pObj findIgnoreCase(const std::string& className) {
        auto& instance = getInstance();
        for (const auto& pair : instance.mpClassFn_) {
            if (strcasecmp(pair.first.c_str(), className.c_str()) == 0) {
                return pair.second(); // 调用创建函数
            }
        }
        return nullptr;
    }

    // 私有构造函数
    ReflectFactory() = default;
    
    // 禁用拷贝构造和赋值
    ReflectFactory(const ReflectFactory&) = delete;
    ReflectFactory& operator=(const ReflectFactory&) = delete;

    // 存储类名到创建函数的映射
    MapClassFactory mpClassFn_;
};

// 注册宏 - 简化类的注册过程
#define REGISTER_CLASS(className) \
    static void* create##className() { return new className(); } \
    static ReflectFactory::Register reg##className(#className, create##className);

// 使用示例
class TestClass {
public:
    TestClass() {
        std::cout << "TestClass created!" << std::endl;
    }
    virtual ~TestClass() = default;
};

// 注册TestClass到反射工厂
REGISTER_CLASS(TestClass)

// 使用示例函数
void reflectExample() {
    // 通过类名字符串创建对象
    void* obj = ReflectFactory::forName("TestClass");
    if (obj) {
        TestClass* testObj = static_cast<TestClass*>(obj);
        // 使用对象...
        delete testObj;
    }
    
    // 打印所有已注册的类
    auto classes = ReflectFactory::getRegisteredClasses();
    for (const auto& className : classes) {
        std::cout << "Registered class: " << className << std::endl;
    }
}