#include <iostream>
#include <memory>
#include <thread>
#include <chrono>
#include <filesystem>
#include "zexuan/plugin_manager.hpp"
#include "zexuan/plugin/plugin_communication.hpp"
#include "system_initializer.hpp"
#include "zexuan/base/singleton_registry.hpp"
#include "spdlog/spdlog.h"
#include "spdlog/sinks/stdout_color_sinks.h"
#include "zexuan/thread_pool.hpp"
namespace fs = std::filesystem;

void printUsage(const char* programName) {
    std::cout << "Usage: " << programName << " <folder_path>" << std::endl;
    std::cout << "  folder_path: Path to the folder containing files to rename" << std::endl;
    std::cout << std::endl;
    std::cout << "Example: " << programName << " /path/to/comic/folder" << std::endl;
    std::cout << std::endl;
    std::cout << "Files will be renamed to format: YYYYMMDDHHMMSSMMM_UUID.ext" << std::endl;
    std::cout << "Example: 20240823133804123_c87398b4-0e7e-4ef5-9d8f-7e2dbde914cc.jpg" << std::endl;
}

// 使用 SimplePluginManager 替代自定义的 PluginLoader

int main(int argc, char* argv[]) {
    try {
        // 首先初始化系统管理器（必须在最开始）
        zexuan::SystemManager systemManager;
        BS::thread_pool threadpool__;
        // 检查命令行参数
        if (argc != 2) {
            printUsage(argv[0]);
            return 1;
        }

        std::string folderPath = argv[1];

        // 验证文件夹路径
        if (!fs::exists(folderPath)) {
            std::cerr << "Error: Folder does not exist: " << folderPath << std::endl;
            return 1;
        }

        if (!fs::is_directory(folderPath)) {
            std::cerr << "Error: Path is not a directory: " << folderPath << std::endl;
            return 1;
        }

        std::cout << "=== Comic Plugin Batch Renamer ===" << std::endl;
        std::cout << "Target folder: " << folderPath << std::endl;

        // SystemManager 已经自动初始化了插件管理器并加载了配置文件中的插件
        std::cout << "=== 开始文件重命名处理 ===" << std::endl;
        std::cout << "使用 ComicPlugin 进行文件重命名..." << std::endl;

        // 检查插件是否可用
        auto comicPlugin = zexuan::plugin::api::getPlugin("ComicPlugin");
        if (!comicPlugin) {
            std::cerr << "错误: 无法获取 ComicPlugin 实例" << std::endl;
            return 1;
        }

        try {
            // 发送文件夹路径给 ComicPlugin 进行批量重命名
            std::cout << "发送文件夹路径到 ComicPlugin..." << std::endl;
            zexuan::plugin::api::publishMessage("Folder path: " + folderPath, "MainApp", "ComicPlugin");

            // 发送开始处理命令
            std::cout << "发送开始处理命令..." << std::endl;
            zexuan::plugin::api::publishMessage("start_processing", "MainApp", "ComicPlugin");

            std::cout << "文件重命名处理完成!" << std::endl;

        } catch (const std::exception& e) {
            std::cerr << "文件处理过程中发生异常: " << e.what() << std::endl;
            return 1;
        }

        // 2. 显示最终状态
        std::cout << "\n=== 系统状态检查 ===" << std::endl;
        std::cout << "2. 最终系统状态:" << std::endl;

        auto finalParticipants = zexuan::plugin::api::getRegisteredParticipants();
        std::cout << "    通信参与者: ";
        for (const auto& participant : finalParticipants) {
            std::cout << participant << " ";
        }
        std::cout << std::endl;

        auto finalManagerState = zexuan::plugin::api::getManagerState();
        std::cout << "    管理器状态: ";
        switch (finalManagerState) {
            case zexuan::plugin::PluginManagerState::RUNNING:
                std::cout << "运行中" << std::endl;
                break;
            case zexuan::plugin::PluginManagerState::STOPPED:
                std::cout << "已停止" << std::endl;
                break;
            default:
                std::cout << "其他状态" << std::endl;
                break;
        }

        std::cout << "\n=== 系统关闭 ===" << std::endl;
        std::cout << "    ✓ SystemManager 将自动处理插件管理器的关闭和清理工作" << std::endl;
        std::cout << "\n=== 程序执行完成 ===" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }

    return 0;
}