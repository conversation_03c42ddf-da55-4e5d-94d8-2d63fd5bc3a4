#include "zexuan/base/singleton_registry.hpp"
#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include <iostream>

using namespace zexuan;

int main() {
    try {
        std::cout << "=== SingletonRegistry 演示 ===" << std::endl;
        
        // 1. 初始化系统（自动注册核心单例）
        SystemManager systemManager;
        auto& registry = singleton_registry();
        
        std::cout << "系统初始化完成，已注册单例数量: " << registry.size() << std::endl;
        
        // 2. 获取核心单例
        std::cout << "\n2. 获取核心单例：" << std::endl;
        
        auto logger = registry.get<Logger>();
        auto config = registry.get<ConfigLoader>();
        
        std::cout << "  - Logger获取成功" << std::endl;
        std::cout << "  - ConfigLoader获取成功" << std::endl;
        
        // 3. 使用便捷函数
        std::cout << "\n3. 使用便捷函数：" << std::endl;
        
        auto logger2 = getSingleton<Logger>();
        auto config2 = getSingleton<ConfigLoader>();
        
        std::cout << "  - 便捷函数获取Logger成功" << std::endl;
        std::cout << "  - 便捷函数获取ConfigLoader成功" << std::endl;
        
        // 4. 验证单例行为
        std::cout << "\n4. 验证单例行为：" << std::endl;
        
        std::cout << "  - Logger是同一实例: " 
                  << (logger.get() == logger2.get() ? "是" : "否") << std::endl;
        std::cout << "  - ConfigLoader是同一实例: " 
                  << (config.get() == config2.get() ? "是" : "否") << std::endl;
        
        // 5. 测试contains方法
        std::cout << "\n5. 测试服务存在性检查：" << std::endl;
        
        std::cout << "  - Logger已注册: " << (registry.contains<Logger>() ? "是" : "否") << std::endl;
        std::cout << "  - ConfigLoader已注册: " << (registry.contains<ConfigLoader>() ? "是" : "否") << std::endl;
        
        // 6. 测试tryGet方法
        std::cout << "\n6. 测试tryGet方法：" << std::endl;
        
        auto logger3 = registry.tryGet<Logger>();
        std::cout << "  - tryGet Logger: " << (logger3 ? "成功" : "失败") << std::endl;
        
        // 7. 实际使用Logger
        std::cout << "\n7. 实际使用Logger：" << std::endl;
        
        auto appLogger = logger->getLogger("singleton.demo");
        appLogger->info("这是通过SingletonRegistry获取的Logger记录的日志");
        appLogger->warn("SingletonRegistry工作正常！");
        
        std::cout << "  - Logger使用成功，请查看日志输出" << std::endl;
        
        // 8. 错误处理测试
        std::cout << "\n8. 错误处理测试：" << std::endl;
        
        try {
            // 尝试获取未注册的单例
            auto nonExistent = registry.get<int>();
        } catch (const RegistryError& e) {
            std::cout << "  - 正确捕获了未注册单例异常: " << e.what() << std::endl;
        }
        
        auto nullSingleton = registry.tryGet<int>();
        std::cout << "  - tryGet未注册单例返回nullptr: " << (nullSingleton == nullptr ? "是" : "否") << std::endl;
        
        std::cout << "\n=== 演示完成 ===" << std::endl;
        std::cout << "SingletonRegistry提供了简单、轻量级的单例管理方案！" << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
