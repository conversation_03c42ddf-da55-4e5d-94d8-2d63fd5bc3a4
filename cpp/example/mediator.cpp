/**
 * @file main.cpp
 * @brief Test program for Observer and Mediator patterns
 * @date 2024
 */

#include <iostream>
#include <memory>
#include <string>
#include <vector>
#include <thread>
#include <chrono>

// Include our migrated pattern implementations
#include "zexuan/base/observer.hpp"
#include "zexuan/base/subject.hpp"
#include "zexuan/base/mediator.hpp"

// Include system initializer
#include "system_initializer.hpp"

using namespace zexuan::base;

/**
 * @brief Test Observer implementation
 */
class TestObserver : public BaseObserver {
public:
    TestObserver(int id, const std::string& description)
        : BaseObserver(id, description), messageCount_(0), eventCount_(0) {

        // Set up callbacks
        setMessageCallback([this](const Message& msg) {
            std::cout << "  [Observer " << getId() << "] 收到消息: 类型=" << msg.getType()
                      << ", 内容=" << msg.getContent() << ", 来源=" << msg.getSourceId() << std::endl;
            messageCount_++;
        });

        setEventCallback([this](const EventMessage& event) {
            std::cout << "  [Observer " << getId() << "] 收到事件: 类型=" << event.getEventType()
                      << ", 设备=" << event.getDeviceId() << ", 数据=" << event.getEventData()
                      << ", 来源=" << event.getSourceId() << std::endl;
            eventCount_++;
        });

        // 设置关心的事件类型和设备
        careEventTypes_ = {1, 2, 3}; // 关心事件类型 1, 2, 3
        careDeviceIds_ = {"device1", "device2"}; // 关心设备 device1, device2
    }

    int getMessageCount() const { return messageCount_; }
    int getEventCount() const { return eventCount_; }

private:
    int messageCount_;
    int eventCount_;
};

/**
 * @brief Test Subject implementation
 */
class TestSubject : public BaseSubject {
public:
    TestSubject(int id, const std::string& description)
        : BaseSubject(id, description), commandCount_(0) {

        // Set up command callback
        setCommandCallback([this](const Message& msg) {
            std::cout << "  [Subject " << getId() << "] 处理命令: 类型=" << msg.getType()
                      << ", 内容=" << msg.getContent() << std::endl;
            commandCount_++;

            // 模拟处理后发送结果
            Message result(msg.getType() + 100, "处理完成: " + msg.getContent(), getId());
            result.setInvokeId(msg.getInvokeId());
            sendResult(result);
        });

        // 设置管理的设备
        setManagedDevices({"device1", "device3"});
        setCareEventTypes({1, 3});
    }

    int getCommandCount() const { return commandCount_; }

    void simulateEvent(int eventType, const std::string& deviceId, const std::string& data) {
        EventMessage event(eventType, deviceId, data, getId());
        sendEventNotify(event);
    }

private:
    int commandCount_;
};

/**
 * @brief 测试Observer和Mediator模式
 */
void testObserverMediatorPatterns() {
    std::cout << "\n=== Observer和Mediator模式测试 ===" << std::endl;

    // 1. 创建Mediator
    std::cout << "1. 创建Mediator..." << std::endl;
    auto mediator = std::make_shared<BaseMediator>();
    if (!mediator->initialize()) {
        std::cerr << "   ❌ Mediator初始化失败" << std::endl;
        return;
    }
    std::cout << "   ✓ Mediator初始化成功" << std::endl;

    // 2. 创建Observer
    std::cout << "2. 创建Observer..." << std::endl;
    auto observer1 = std::make_shared<TestObserver>(1, "测试观察者1");
    auto observer2 = std::make_shared<TestObserver>(2, "测试观察者2");

    std::string errorMsg;
    if (!mediator->registerObserver(1, observer1.get(), errorMsg)) {
        std::cerr << "   ❌ Observer1注册失败: " << errorMsg << std::endl;
        return;
    }
    if (!mediator->registerObserver(2, observer2.get(), errorMsg)) {
        std::cerr << "   ❌ Observer2注册失败: " << errorMsg << std::endl;
        return;
    }
    std::cout << "   ✓ 成功注册 " << mediator->getObserverCount() << " 个Observer" << std::endl;

    // 3. 创建Subject
    std::cout << "3. 创建Subject..." << std::endl;
    auto subject1 = std::make_shared<TestSubject>(101, "测试主题1");
    auto subject2 = std::make_shared<TestSubject>(102, "测试主题2");

    subject1->setMediator(mediator);
    subject2->setMediator(mediator);

    if (!mediator->registerSubject(101, subject1.get(), errorMsg)) {
        std::cerr << "   ❌ Subject1注册失败: " << errorMsg << std::endl;
        return;
    }
    if (!mediator->registerSubject(102, subject2.get(), errorMsg)) {
        std::cerr << "   ❌ Subject2注册失败: " << errorMsg << std::endl;
        return;
    }
    std::cout << "   ✓ 成功注册 " << mediator->getSubjectCount() << " 个Subject" << std::endl;

    // 4. 测试直接Observer-Subject通信
    std::cout << "4. 测试直接Observer-Subject通信..." << std::endl;
    subject1->attach(observer1);
    subject1->attach(observer2);

    Message directMsg(10, "直接通信测试", 101);
    subject1->notify(directMsg);
    std::cout << "   ✓ 直接通信测试完成" << std::endl;

    // 5. 测试通过Mediator的命令发送
    std::cout << "5. 测试通过Mediator的命令发送..." << std::endl;
    Message command(20, "通过Mediator的命令", 1);
    command.setInvokeId("1#EC#test_command");

    std::string description;
    int result = mediator->sendMessageToSubject(101, command, description, 1);
    if (result == 0) {
        std::cout << "   ✓ 命令发送成功: " << description << std::endl;
    } else {
        std::cout << "   ❌ 命令发送失败: " << description << std::endl;
    }

    // 6. 测试事件广播
    std::cout << "6. 测试事件广播..." << std::endl;
    subject1->simulateEvent(1, "device1", "设备1状态变化");
    subject1->simulateEvent(2, "device2", "设备2报警");
    subject2->simulateEvent(3, "device3", "设备3数据更新");

    // 短暂等待，让异步处理完成
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 7. 显示统计信息
    std::cout << "7. 统计信息:" << std::endl;
    std::cout << "   Observer1: 收到 " << observer1->getMessageCount() << " 条消息, "
              << observer1->getEventCount() << " 个事件" << std::endl;
    std::cout << "   Observer2: 收到 " << observer2->getMessageCount() << " 条消息, "
              << observer2->getEventCount() << " 个事件" << std::endl;
    std::cout << "   Subject1: 处理了 " << subject1->getCommandCount() << " 个命令" << std::endl;
    std::cout << "   Subject2: 处理了 " << subject2->getCommandCount() << " 个命令" << std::endl;

    // 8. 清理测试
    std::cout << "8. 清理测试..." << std::endl;
    subject1->detach(observer1);
    subject1->detach(observer2);

    mediator->unregisterObserver(1, errorMsg);
    mediator->unregisterObserver(2, errorMsg);
    mediator->unregisterSubject(101, errorMsg);
    mediator->unregisterSubject(102, errorMsg);

    std::cout << "   ✓ 清理完成" << std::endl;
    std::cout << "\n=== Observer和Mediator模式测试完成 ===" << std::endl;
}

int main() {
    try {
        std::cout << "=== zexuan项目 - Observer和Mediator模式测试程序 ===" << std::endl;

        // 初始化系统
        std::cout << "\n--- 系统初始化 ---" << std::endl;
        zexuan::SystemInitializer::initialize();

        // 运行模式测试
        testObserverMediatorPatterns();

        // 系统清理
        std::cout << "\n--- 系统清理 ---" << std::endl;
        zexuan::SystemInitializer::cleanup();

        std::cout << "\n✨ 程序执行完成！" << std::endl;
        return 0;

    } catch (const std::exception& e) {
        std::cerr << "❌ 程序执行出错: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "❌ 程序执行出现未知错误" << std::endl;
        return 1;
    }
}