#pragma once
#include "comic.hpp"

namespace soci {
    template<>
    struct type_conversion<Comic> {
        typedef values base_type;

        static void from_base(values const & v, indicator /* ind */, Comic & c) {
            c.id = v.get<int>("id");
            c.name = v.get<std::string>("name");
            c.path = v.get<std::string>("path");
            
            indicator ind_created = v.get_indicator("created_at");
            if (ind_created != i_null) {
                c.created_at = v.get<std::tm>("created_at");
            }
            
            indicator ind_updated = v.get_indicator("updated_at");
            if (ind_updated != i_null) {
                c.updated_at = v.get<std::tm>("updated_at");
            }
        }

        static void to_base(const Comic & c, values & v, indicator & ind) {
            v.set("id", c.id);
            v.set("name", c.name);
            v.set("path", c.path);
            v.set("created_at", c.created_at);
            v.set("updated_at", c.updated_at);
            ind = i_ok;
        }
    };
}
