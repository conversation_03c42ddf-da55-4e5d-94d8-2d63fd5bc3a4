#pragma once

#include "zexuan/base/singleton.hpp"
#include "zexuan/base/noncopyable.hpp"
#include <memory>
#include <unordered_map>
#include <typeindex>
#include <mutex>
#include <shared_mutex>
#include <functional>
#include <vector>
#include <atomic>

namespace zexuan {

/**
 * @brief 线程安全的单例注册表类
 *
 * SingletonRegistry 提供了一个全局的单例对象管理器，能够为任意类型创建和管理单例实例。
 * 主要特性：
 * - 线程安全：使用 std::call_once 确保每个类型只创建一次实例
 * - 内存管理：使用 std::shared_ptr 进行自动内存管理
 * - 生命周期管理：支持程序退出时的清理机制
 * - 类型安全：基于 std::type_index 进行类型识别
 *
 * 使用示例：
 * @code
 * class MyClass {
 * public:
 *     MyClass() = default;
 *     void doSomething() { ... }
 * };
 *
 * // 获取单例实例
 * auto instance = SingletonRegistry::getInstance().get<MyClass>();
 * instance->doSomething();
 * @endcode
 */
class SingletonRegistry : public singleton<SingletonRegistry> {
private:
    SingletonRegistry() = default;
    ~SingletonRegistry() {
        // 程序退出时自动清理所有实例
        clear();
    }

    friend class singleton<SingletonRegistry>;

public:

    /**
     * @brief 获取指定类型 T 的单例实例
     * @tparam T 要获取单例的类型
     * @return 指向类型 T 单例实例的 shared_ptr
     *
     * 该方法是线程安全的，确保每个类型 T 只会创建一次实例。
     * 如果实例不存在，会自动创建；如果已存在，直接返回现有实例。
     */
    template<typename T>
    std::shared_ptr<T> get() {
        std::type_index typeIndex(typeid(T));

        // 首先尝试快速路径：检查实例是否已存在
        {
            std::shared_lock<std::shared_mutex> lock(mutex_);
            auto it = instances_.find(typeIndex);
            if (it != instances_.end()) {
                return std::static_pointer_cast<T>(it->second);
            }
        }

        // 实例不存在，需要创建
        std::unique_lock<std::shared_mutex> lock(mutex_);

        // 双重检查锁定模式：再次检查实例是否已被其他线程创建
        auto it = instances_.find(typeIndex);
        if (it != instances_.end()) {
            return std::static_pointer_cast<T>(it->second);
        }

        // 创建新实例
        auto instance = std::make_shared<T>();
        instances_[typeIndex] = instance;

        // 注册清理函数
        registerCleanup(typeIndex);

        return instance;
    }

    /**
     * @brief 检查指定类型的单例是否已存在
     * @tparam T 要检查的类型
     * @return 如果单例已存在返回 true，否则返回 false
     */
    template<typename T>
    bool exists() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        std::type_index typeIndex(typeid(T));
        return instances_.find(typeIndex) != instances_.end();
    }

    /**
     * @brief 移除指定类型的单例实例
     * @tparam T 要移除的类型
     * @return 如果成功移除返回 true，如果实例不存在返回 false
     *
     * 注意：这个操作会立即移除实例，如果其他地方还持有该实例的 shared_ptr，
     * 实例不会被立即销毁，但不会再从注册表中获取到该实例。
     */
    template<typename T>
    bool remove() {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        std::type_index typeIndex(typeid(T));
        return instances_.erase(typeIndex) > 0;
    }

    /**
     * @brief 清空所有单例实例
     *
     * 这个方法会移除所有已注册的单例实例。通常在程序退出时调用。
     */
    void clear() {
        std::unique_lock<std::shared_mutex> lock(mutex_);
        instances_.clear();
        cleanupFunctions_.clear();
    }

    /**
     * @brief 获取当前注册的单例实例数量
     * @return 单例实例的数量
     */
    size_t size() const {
        std::shared_lock<std::shared_mutex> lock(mutex_);
        return instances_.size();
    }

private:
    /**
     * @brief 注册清理函数
     * @param typeIndex 类型索引
     */
    void registerCleanup(const std::type_index& typeIndex) {
        // 如果需要特殊的清理逻辑，可以在这里添加
        // 目前使用 shared_ptr 的自动管理机制
        cleanupFunctions_.emplace_back([typeIndex, this]() {
            instances_.erase(typeIndex);
        });
    }

private:
    // 使用 shared_mutex 支持多读单写，提高并发性能
    mutable std::shared_mutex mutex_;

    // 存储类型索引到实例的映射
    std::unordered_map<std::type_index, std::shared_ptr<void>> instances_;

    // 清理函数列表，用于程序退出时的清理
    std::vector<std::function<void()>> cleanupFunctions_;
};

/**
 * @brief 便捷的单例获取函数
 * @tparam T 要获取的单例类型
 * @return 指向类型 T 单例实例的 shared_ptr
 *
 * 这是一个全局便捷函数，等价于 SingletonRegistry::getInstance().get<T>()
 *  动态库中绝对不能使用这个函数，要用传入的指针去调用
 */
template<typename T>
std::shared_ptr<T> getSingleton() {
    return SingletonRegistry::getInstance().get<T>();
}

} // namespace zexuan
