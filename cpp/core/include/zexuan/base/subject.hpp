/**
 * @file subject.hpp
 * @brief Simplified Subject pattern implementation for zexuan project
 * <AUTHOR> from NXSubject.h
 * @date 2024
 *
 * Simplified subject pattern implementation using unified Message class
 * based on IEC 60870-5-103 protocol.
 */

#ifndef ZEXUAN_BASE_SUBJECT_HPP
#define ZEXUAN_BASE_SUBJECT_HPP

#include "message.hpp"
#include <memory>
#include <vector>
#include <string>
#include <mutex>

namespace zexuan {
namespace base {

// Forward declarations
class Observer;
class Mediator;

/**
 * @brief Subject interface for notifying observers
 *
 * Simplified subject interface that only handles Message notifications.
 */
class Subject {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~Subject() = default;

    /**
     * @brief Attach an observer to this subject
     * @param observer The observer to attach
     */
    virtual void attach(std::shared_ptr<Observer> observer) = 0;

    /**
     * @brief Detach an observer from this subject
     * @param observer The observer to detach
     */
    virtual void detach(std::shared_ptr<Observer> observer) = 0;

    /**
     * @brief Notify all attached observers
     * @param message The message to send to observers
     */
    virtual void notify(const Message& message) = 0;

    /**
     * @brief Get the subject's unique identifier
     * @return Subject ID
     */
    virtual int getId() const = 0;

    /**
     * @brief Get the subject's description
     * @return Subject description
     */
    virtual std::string getDescription() const = 0;
};

/**
 * @brief Base implementation of Subject interface
 *
 * Simplified base subject implementation with basic observer management.
 */
class BaseSubject : public Subject {
public:
    /**
     * @brief Constructor
     * @param id Subject unique identifier
     * @param description Subject description
     */
    BaseSubject(int id, const std::string& description);

    /**
     * @brief Virtual destructor
     */
    virtual ~BaseSubject() = default;

    // Subject interface implementation
    void attach(std::shared_ptr<Observer> observer) override;
    void detach(std::shared_ptr<Observer> observer) override;
    void notify(const Message& message) override;
    int getId() const override { return id_; }
    std::string getDescription() const override { return description_; }

    /**
     * @brief Set the mediator for this subject
     * @param mediator The mediator to use for communication
     */
    void setMediator(std::shared_ptr<Mediator> mediator);

    /**
     * @brief Send a message through the mediator
     * @param message The message to send (contains target address)
     * @return 0 on success, negative on failure
     */
    virtual int sendMessage(const Message& message);

protected:
    int id_;                                            ///< Subject unique identifier
    std::string description_;                           ///< Subject description
    std::vector<std::shared_ptr<Observer>> observers_; ///< List of attached observers
    std::shared_ptr<Mediator> mediator_;               ///< Mediator for communication
    mutable std::mutex observersMutex_;                ///< Mutex for thread-safe observer operations
};

} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_SUBJECT_HPP
