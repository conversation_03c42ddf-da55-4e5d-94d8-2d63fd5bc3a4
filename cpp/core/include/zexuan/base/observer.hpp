/**
 * @file observer.hpp
 * @brief Simplified Observer pattern interface definitions for zexuan project
 * <AUTHOR> from NXObserver.h
 * @date 2024
 *
 * Simplified observer pattern implementation using unified Message class
 * based on IEC 60870-5-103 protocol.
 */

#ifndef ZEXUAN_BASE_OBSERVER_HPP
#define ZEXUAN_BASE_OBSERVER_HPP

#include "message.hpp"
#include <functional>
#include <string>

namespace zexuan {
namespace base {

// Forward declarations
class Subject;

/**
 * @brief Observer interface for receiving notifications
 *
 * Simplified observer interface that only handles Message notifications.
 */
class Observer {
public:
    /**
     * @brief Virtual destructor
     */
    virtual ~Observer() = default;

    /**
     * @brief Called when the observed subject changes
     * @param subject The subject that changed
     * @param message The message containing change information
     */
    virtual void onNotify(Subject* subject, const Message& message) = 0;

    /**
     * @brief Get the observer's unique identifier
     * @return Observer ID
     */
    virtual int getId() const = 0;

    /**
     * @brief Get the observer's description
     * @return Observer description
     */
    virtual std::string getDescription() const = 0;
};

/**
 * @brief Base implementation of Observer interface
 *
 * Simplified base observer implementation with message callback support.
 */
class BaseObserver : public Observer {
public:
    /**
     * @brief Constructor
     * @param id Observer unique identifier
     * @param description Observer description
     */
    BaseObserver(int id, const std::string& description);

    /**
     * @brief Virtual destructor
     */
    virtual ~BaseObserver() = default;

    // Observer interface implementation
    void onNotify(Subject* subject, const Message& message) override;
    int getId() const override { return id_; }
    std::string getDescription() const override { return description_; }

    /**
     * @brief Set callback function for message handling
     * @param callback The callback function
     */
    void setMessageCallback(std::function<void(const Message&)> callback);

protected:
    int id_;                                                ///< Observer unique identifier
    std::string description_;                               ///< Observer description
    std::function<void(const Message&)> messageCallback_;  ///< Message handling callback
};



} // namespace base
} // namespace zexuan

#endif // ZEXUAN_BASE_OBSERVER_HPP
