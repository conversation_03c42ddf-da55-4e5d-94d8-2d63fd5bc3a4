#pragma once

#include <string>
#include <map>
#include <memory>
#include <functional>
#include <iostream>
#include <vector>

#ifdef _WIN32
    #include <windows.h>
    typedef HMODULE LibraryHandle;
#else
    #include <dlfcn.h>
    typedef void* LibraryHandle;
#endif

namespace zexuan {
namespace base {

/**
 * @brief 动态库加载器类
 * 提供跨平台的动态库加载、卸载和符号解析功能
 * 参考 NXEcLoadProLib 的设计模式，提供更通用的动态库管理功能
 */
class DynamicLibraryLoader {
public:
    /**
     * @brief 构造函数
     */
    DynamicLibraryLoader();

    /**
     * @brief 析构函数
     * 自动卸载所有已加载的动态库
     */
    ~DynamicLibraryLoader();

    /**
     * @brief 加载动态库
     * @param libPath 动态库路径
     * @return 加载成功返回true，失败返回false
     */
    bool loadLibrary(const std::string& libPath);

    /**
     * @brief 卸载动态库
     * @param libPath 动态库路径
     * @return 卸载成功返回true，失败返回false
     */
    bool unloadLibrary(const std::string& libPath);

    /**
     * @brief 获取函数地址
     * @param libPath 动态库路径
     * @param functionName 函数名称
     * @return 函数指针，失败返回nullptr
     */
    void* getFunctionAddress(const std::string& libPath, const std::string& functionName);

    /**
     * @brief 模板方法：获取类型安全的函数指针
     * @tparam FuncType 函数指针类型
     * @param libPath 动态库路径
     * @param functionName 函数名称
     * @return 类型安全的函数指针，失败返回nullptr
     */
    template<typename FuncType>
    FuncType getFunction(const std::string& libPath, const std::string& functionName) {
        void* addr = getFunctionAddress(libPath, functionName);
        return reinterpret_cast<FuncType>(addr);
    }

    /**
     * @brief 检查动态库是否已加载
     * @param libPath 动态库路径
     * @return 已加载返回true，否则返回false
     */
    bool isLibraryLoaded(const std::string& libPath) const;

    /**
     * @brief 卸载所有动态库
     */
    void unloadAllLibraries();

    /**
     * @brief 获取最后的错误信息
     * @return 错误信息字符串
     */
    std::string getLastError() const;

    /**
     * @brief 获取已加载的动态库列表
     * @return 动态库路径列表
     */
    std::vector<std::string> getLoadedLibraries() const;

private:
    /**
     * @brief 标准化动态库名称
     * 根据平台自动添加适当的前缀和后缀
     * @param libName 库名称
     * @return 标准化后的库名称
     */
    std::string normalizeLibraryName(const std::string& libName);

    /**
     * @brief 获取系统错误信息
     * @return 系统错误信息字符串
     */
    std::string getSystemError() const;

    // 已加载的动态库句柄映射
    std::map<std::string, LibraryHandle> loadedLibraries_;

    // 最后的错误信息
    mutable std::string lastError_;
};

} // namespace base
} // namespace zexuan
