#pragma once

#include <string>
#include <functional>
#include <iostream>

namespace zexuan {

/**
 * 自动注册器模板类
 * 用于在静态初始化阶段自动注册类到指定的工厂中
 * 
 * @tparam FactoryType 目标工厂类型，必须有 registerClass 静态方法
 */
template<typename FactoryType>
class AutoRegister {
public:
    /**
     * 构造函数 - 自动执行注册
     * @param name 要注册的类名称（字符串）
     * @param createFn 创建函数
     */
    template<typename CreateFn>
    AutoRegister(const std::string& name, CreateFn createFn) {
        std::cout << "AutoRegister: Attempting to register '" << name << "'..." << std::endl;
        bool success = FactoryType::registerClass(name, createFn);
        if (success) {
            std::cout << "AutoRegister: Successfully registered '" << name << "'" << std::endl;
        } else {
            std::cout << "AutoRegister: Failed to register '" << name << "' (already exists?)" << std::endl;
        }
    }
};

/**
 * 基本注册宏 - 使用类名作为注册字符串
 * 
 * 使用方式：
 * ZEXUAN_AUTO_REGISTER(FactoryClass, ClassName)
 * 
 * @param FactoryType 工厂类型
 * @param ClassName 要注册的类名
 */
#define ZEXUAN_AUTO_REGISTER(FactoryType, ClassName) \
    static auto createFunc##ClassName = []() { \
        return std::make_shared<ClassName>(); \
    }; \
    static zexuan::AutoRegister<FactoryType> autoReg##ClassName(#ClassName, createFunc##ClassName);

/**
 * 字符串注册宏 - 使用自定义字符串作为注册名
 * 
 * 使用方式：
 * ZEXUAN_REGISTER_AS(FactoryClass, ClassName, "custom_name")
 * 
 * @param FactoryType 工厂类型
 * @param ClassName 要注册的类名
 * @param StringName 注册名称（字符串字面量）
 */
#define ZEXUAN_REGISTER_AS(FactoryType, ClassName, StringName) \
    static auto createFunc##ClassName##__LINE__ = []() { \
        return std::make_shared<ClassName>(); \
    }; \
    static zexuan::AutoRegister<FactoryType> autoReg##ClassName##__LINE__(StringName, createFunc##ClassName##__LINE__);

} // namespace zexuan
