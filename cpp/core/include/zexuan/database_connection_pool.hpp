#ifndef ZEXUAN_DATABASE_CONNECTION_POOL_HPP
#define ZEXUAN_DATABASE_CONNECTION_POOL_HPP

#include "soci/soci.h"
#include "soci/mysql/soci-mysql.h"
#include <mutex>
#include <queue>
#include <memory>
#include <string>
#include <thread>
#include <condition_variable>
#include "zexuan/base/singleton.hpp"

namespace zexuan {

class DatabaseConnectionPool : public singleton<DatabaseConnectionPool> {
private:
    DatabaseConnectionPool() {
        init("config/config.json");
    }
    ~DatabaseConnectionPool() {
        shutdown();
    }

    friend class singleton<DatabaseConnectionPool>;

public:
    // 获取连接（带超时）
    std::shared_ptr<soci::session> getConnection(
        std::chrono::milliseconds timeout = std::chrono::milliseconds(1000));

    // 释放连接
    void releaseConnection(std::shared_ptr<soci::session> conn);

    // 获取连接池大小
    size_t getPoolSize() const { return pool_size_; }

    // 获取当前可用连接数
    size_t getAvailableConnections() const;

    // 关闭连接池
    void shutdown();

private:
    // 从配置文件初始化
    void init(const std::string& config_path);

    // 创建新的数据库连接
    std::shared_ptr<soci::session> createConnection();
    // 构建连接字符串
    std::string buildConnectionString(const std::string& type,
        const std::string& db_name,
        const std::string& user,
        const std::string& password,
        const std::string& unix_socket);
private:
    std::string connection_string_;
    size_t pool_size_ = 0;
    bool is_initialized_ = false;
    bool is_shutdown_ = false;

    mutable std::mutex mutex_;
    std::condition_variable cv_;
    std::queue<std::shared_ptr<soci::session>> connections_;
};

// RAII 风格的连接获取器
class ConnectionGuard {
public:
    explicit ConnectionGuard(
        std::chrono::milliseconds timeout = std::chrono::milliseconds(1000))
        : conn_(DatabaseConnectionPool::getInstance().getConnection(timeout)) {}
    
    ~ConnectionGuard() {
        if (conn_) {
            DatabaseConnectionPool::getInstance().releaseConnection(conn_);
        }
    }

    // 获取原始连接
    soci::session* operator->() { return conn_.get(); }
    soci::session& operator*() { return *conn_; }

private:
    std::shared_ptr<soci::session> conn_;
};

} // namespace zexuan

#endif // ZEXUAN_DATABASE_CONNECTION_POOL_HPP
