#pragma once

#include <nlohmann/json.hpp>
#include <string>

namespace zexuan {

/**
 * @brief 极简配置加载器
 *
 * 只提供最基本的JSON配置文件加载和读取功能
 */
class ConfigLoader {
public:
    ConfigLoader() = default;

    // 从文件加载JSON配置
    void loadFromFile(const std::string& filepath);

    // 获取配置值（带默认值）
    template<typename T>
    T get(const std::string& key, const T& default_value) const {
        if (config_.contains(key)) {
            return config_[key].get<T>();
        }
        return default_value;
    }

private:
    nlohmann::json config_;
};

} // namespace zexuan
