#pragma once

#include <nlohmann/json.hpp>
#include <string>
#include <string_view>
#include <optional>
#include <filesystem>
#include <type_traits>
#include "zexuan/base/singleton.hpp"
#include "zexuan/base/singleton_registry.hpp"

namespace zexuan {

class ConfigLoader {
public:
    ConfigLoader() = default;
    ~ConfigLoader() = default;

public:
    /**
     * @brief 获取 ConfigLoader 单例实例（兼容性接口）
     * @return ConfigLoader 实例的 shared_ptr
     */
    static std::shared_ptr<ConfigLoader> getInstance() {
        return getSingleton<ConfigLoader>();
    }

public:
    using JsonType = nlohmann::json;
    using PathType = std::filesystem::path;

    // 配置加载异常类
    class ConfigError : public std::runtime_error {
        using std::runtime_error::runtime_error;
    };

    // 加载配置
    void loadFromFile(const PathType& filepath);
    void loadFromString(std::string_view json_str);
    void loadFromJson(const JsonType& json);

    // 保存配置
    void saveToFile(const PathType& filepath, bool pretty = true) const;
    std::string toString(bool pretty = true) const;

    // 获取配置项（带默认值）
    template<typename T>
    T get(const std::string& path, const T& default_value) const {
        try {
            return getValueByPath<T>(path);
        } catch (const std::exception&) {
            return default_value;
        }
    }

    // 获取配置项（无默认值，如果不存在则抛出异常）
    template<typename T>
    T get(const std::string& path) const {
        return getValueByPath<T>(path);
    }

    // 获取可选配置项
    template<typename T>
    std::optional<T> getOptional(const std::string& path) const {
        try {
            return getValueByPath<T>(path);
        } catch (const std::exception&) {
            return std::nullopt;
        }
    }

    // 设置配置项
    template<typename T>
    void set(const std::string& path, T&& value) {
        setValueByPath(path, std::forward<T>(value));
    }

    // 检查配置项是否存在
    bool exists(const std::string& path) const;

    // 移除配置项
    void remove(const std::string& path);

    // 清空所有配置
    void clear();

    // 获取原始JSON对象（只读）
    const JsonType& getRawJson() const { return config_; }

private:
    JsonType config_;

    // 通过路径获取JSON节点（支持点号分隔的路径，如 "database.host"）
    const JsonType& getNodeByPath(const std::string& path) const;
    JsonType& getNodeByPath(const std::string& path);

    // 通过路径获取值
    template<typename T>
    T getValueByPath(const std::string& path) const {
        const auto& node = getNodeByPath(path);
        try {
            if constexpr (std::is_same_v<T, bool>) {
                // 特殊处理布尔值，支持 "true"/"false" 字符串
                if (node.is_string()) {
                    std::string value = node.get<std::string>();
                    if (value == "true") return true;
                    if (value == "false") return false;
                    throw ConfigError("Invalid boolean value");
                }
            }
            return node.get<T>();
        } catch (const nlohmann::json::exception& e) {
            throw ConfigError(std::string("Type conversion error at '") + path + "': " + e.what());
        }
    }

    // 通过路径设置值
    template<typename T>
    void setValueByPath(const std::string& path, T&& value) {
        std::vector<std::string> parts = splitPath(path);
        JsonType* current = &config_;
        
        for (size_t i = 0; i < parts.size() - 1; ++i) {
            if (!current->contains(parts[i])) {
                (*current)[parts[i]] = JsonType::object();
            }
            current = &(*current)[parts[i]];
        }
        
        (*current)[parts.back()] = std::forward<T>(value);
    }

    // 分割路径字符串
    static std::vector<std::string> splitPath(const std::string& path);
};

} // namespace zexuan
