#pragma once

#include <nlohmann/json.hpp>
#include <string>
#include <string_view>
#include <optional>
#include <filesystem>
#include <type_traits>

namespace zexuan {

/**
 * @brief 简化的配置加载器
 *
 * 特点：
 * - 移除单例模式，支持创建多个实例
 * - 保持完整的JSON配置功能
 * - 简化的接口设计
 * - 更好的可测试性
 */
class ConfigLoader {
public:
    using JsonType = nlohmann::json;
    using PathType = std::filesystem::path;

    // 配置加载异常类
    class ConfigError : public std::runtime_error {
        using std::runtime_error::runtime_error;
    };

    // 构造函数
    ConfigLoader() = default;
    explicit ConfigLoader(const PathType& filepath) { loadFromFile(filepath); }
    explicit ConfigLoader(std::string_view json_str) { loadFromString(json_str); }
    explicit ConfigLoader(const JsonType& json) : config_(json) {}

    // 加载配置
    void loadFromFile(const PathType& filepath);
    void loadFromString(std::string_view json_str);
    void loadFromJson(const JsonType& json);

    // 保存配置
    void saveToFile(const PathType& filepath, bool pretty = true) const;
    std::string toString(bool pretty = true) const;

    // 获取配置项（带默认值）
    template<typename T>
    T get(const std::string& path, const T& default_value) const {
        try {
            return getValueByPath<T>(path);
        } catch (const std::exception&) {
            return default_value;
        }
    }

    // 获取配置项（无默认值，如果不存在则抛出异常）
    template<typename T>
    T get(const std::string& path) const {
        return getValueByPath<T>(path);
    }

    // 获取可选配置项
    template<typename T>
    std::optional<T> getOptional(const std::string& path) const {
        try {
            return getValueByPath<T>(path);
        } catch (const std::exception&) {
            return std::nullopt;
        }
    }

    // 设置配置项
    template<typename T>
    void set(const std::string& path, T&& value) {
        setValueByPath(path, std::forward<T>(value));
    }

    // 检查配置项是否存在
    bool exists(const std::string& path) const;

    // 移除配置项
    void remove(const std::string& path);

    // 清空所有配置
    void clear();

    // 获取原始JSON对象（只读）
    const JsonType& getRawJson() const { return config_; }

private:
    JsonType config_;

    // 通过路径获取JSON节点（支持点号分隔的路径，如 "database.host"）
    const JsonType& getNodeByPath(const std::string& path) const;
    JsonType& getNodeByPath(const std::string& path);

    // 通过路径获取值
    template<typename T>
    T getValueByPath(const std::string& path) const {
        const auto& node = getNodeByPath(path);
        try {
            if constexpr (std::is_same_v<T, bool>) {
                // 特殊处理布尔值，支持 "true"/"false" 字符串
                if (node.is_string()) {
                    std::string value = node.get<std::string>();
                    if (value == "true") return true;
                    if (value == "false") return false;
                    throw ConfigError("Invalid boolean value");
                }
            }
            return node.get<T>();
        } catch (const nlohmann::json::exception& e) {
            throw ConfigError(std::string("Type conversion error at '") + path + "': " + e.what());
        }
    }

    // 通过路径设置值
    template<typename T>
    void setValueByPath(const std::string& path, T&& value) {
        std::vector<std::string> parts = splitPath(path);
        JsonType* current = &config_;
        
        for (size_t i = 0; i < parts.size() - 1; ++i) {
            if (!current->contains(parts[i])) {
                (*current)[parts[i]] = JsonType::object();
            }
            current = &(*current)[parts[i]];
        }
        
        (*current)[parts.back()] = std::forward<T>(value);
    }

    // 分割路径字符串
    static std::vector<std::string> splitPath(const std::string& path);
};

// ============================================================================
// 便捷的全局函数接口（可选使用）
// ============================================================================

/**
 * @brief 全局配置实例（可选使用）
 *
 * 提供一个全局配置实例，用于需要全局访问配置的场景。
 * 但推荐在大多数情况下直接创建ConfigLoader实例。
 */
ConfigLoader& getGlobalConfig();

/**
 * @brief 初始化全局配置
 * @param config_path 配置文件路径
 */
void initGlobalConfig(const std::filesystem::path& config_path);

/**
 * @brief 便捷的全局配置获取函数
 */
template<typename T>
T getGlobalConfigValue(const std::string& path, const T& default_value = T{}) {
    return getGlobalConfig().get<T>(path, default_value);
}

} // namespace zexuan
