#pragma once

#include <memory>
#include <string>
#include <filesystem>
#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/basic_file_sink.h>

namespace zexuan {

/**
 * @brief 极简日志管理器
 *
 * 只提供获取控制台logger和文件logger的基本功能
 */
class Logger {
public:
    // 获取控制台logger
    static std::shared_ptr<spdlog::logger> getConsoleLogger() {
        static auto console_logger = []() {
            auto logger = spdlog::stdout_color_mt("console");
            // 设置格式：时间 + 线程 + 日志级别 + 消息
            logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [thread %t] [%^%l%$] %v");
            return logger;
        }();
        return console_logger;
    }

    // 获取文件logger（保存在logs文件夹中）
    static std::shared_ptr<spdlog::logger> getFileLogger(const std::string& filename) {
        try {
            // 确保logs目录存在
            std::filesystem::create_directories("logs");

            // 创建文件logger，文件保存在logs文件夹中
            auto logger = spdlog::basic_logger_mt(filename, "logs/" + filename + ".log");

            // 设置格式：时间 + 线程 + 日志级别 + 消息
            logger->set_pattern("[%Y-%m-%d %H:%M:%S.%e] [thread %t] [%^%l%$] %v");

            return logger;
        } catch (const spdlog::spdlog_ex&) {
            // 如果已存在，直接获取
            return spdlog::get(filename);
        }
    }
};

} // namespace zexuan
