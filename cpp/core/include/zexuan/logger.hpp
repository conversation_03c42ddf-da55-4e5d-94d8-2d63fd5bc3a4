#pragma once

#include <memory>
#include <string>
#include <spdlog/spdlog.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/basic_file_sink.h>

namespace zexuan {

/**
 * @brief 极简日志管理器
 *
 * 只提供获取控制台logger和文件logger的基本功能
 */
class Logger {
public:
    // 获取控制台logger
    static std::shared_ptr<spdlog::logger> getConsoleLogger() {
        static auto console_logger = spdlog::stdout_color_mt("console");
        return console_logger;
    }

    // 获取文件logger
    static std::shared_ptr<spdlog::logger> getFileLogger(const std::string& filename) {
        try {
            return spdlog::basic_logger_mt(filename, filename + ".log");
        } catch (const spdlog::spdlog_ex&) {
            // 如果已存在，直接获取
            return spdlog::get(filename);
        }
    }
};

} // namespace zexuan
