#pragma once

#include <memory>
#include <string>
#include <unordered_map>
#include <spdlog/spdlog.h>
#include <spdlog/async.h>
#include <spdlog/sinks/stdout_color_sinks.h>
#include <spdlog/sinks/rotating_file_sink.h>

namespace zexuan {

/**
 * @brief 简化的日志管理器
 *
 * 特点：
 * - 移除单例模式，但提供全局访问函数
 * - 基于spdlog的轻量级封装
 * - 简化的初始化和使用接口
 * - 更好的可测试性
 */
class Logger {
public:
    Logger() = default;
    ~Logger() = default;

    // 获取默认的控制台logger
    std::shared_ptr<spdlog::logger> getDefaultLogger() const { return default_logger_; }

    // 获取或创建一个文件logger
    std::shared_ptr<spdlog::logger> getLogger(const std::string& name);

    // 初始化默认logger和全局配置
    void init(const std::string& config_path = "config/config.json");

    // 关闭所有logger
    void shutdown();

private:
    // 创建一个新的文件logger（简化版本，只需要name参数）
    std::shared_ptr<spdlog::logger> createFileLogger(const std::string& name);

private:
    std::shared_ptr<spdlog::logger> default_logger_{nullptr};  // 默认的控制台logger
    std::unordered_map<std::string, std::shared_ptr<spdlog::logger>> loggers_;  // 文件logger映射
    bool is_async_{false};
    size_t max_file_size_{5 * 1024 * 1024};
    size_t max_files_{3};
    std::string log_level_{"info"};
    std::string log_pattern_{"[%Y-%m-%d %H:%M:%S.%e] [%n] [%^%l%$] [thread %t] %v"};
    std::string log_dir_{"logs"};
    bool is_initialized_{false};

    // 静态标志，跟踪线程池初始化状态
    static bool thread_pool_initialized_;
};

// ============================================================================
// 全局日志接口（推荐使用）
// ============================================================================

/**
 * @brief 获取全局日志管理器
 */
Logger& getGlobalLogger();

/**
 * @brief 初始化全局日志系统
 * @param config_path 配置文件路径（可选）
 */
void initGlobalLogger(const std::string& config_path = "");

/**
 * @brief 关闭全局日志系统
 */
void shutdownGlobalLogger();

/**
 * @brief 获取默认控制台logger
 */
std::shared_ptr<spdlog::logger> getDefaultLogger();

/**
 * @brief 获取或创建命名logger
 */
std::shared_ptr<spdlog::logger> getNamedLogger(const std::string& name);

// ============================================================================
// 便捷的日志函数（直接使用spdlog的全局默认logger）
// ============================================================================

template<typename... Args>
void trace(const std::string& format, Args&&... args) {
    spdlog::trace(format, std::forward<Args>(args)...);
}

template<typename... Args>
void debug(const std::string& format, Args&&... args) {
    spdlog::debug(format, std::forward<Args>(args)...);
}

template<typename... Args>
void info(const std::string& format, Args&&... args) {
    spdlog::info(format, std::forward<Args>(args)...);
}

template<typename... Args>
void warn(const std::string& format, Args&&... args) {
    spdlog::warn(format, std::forward<Args>(args)...);
}

template<typename... Args>
void error(const std::string& format, Args&&... args) {
    spdlog::error(format, std::forward<Args>(args)...);
}

template<typename... Args>
void critical(const std::string& format, Args&&... args) {
    spdlog::critical(format, std::forward<Args>(args)...);
}

} // namespace zexuan
