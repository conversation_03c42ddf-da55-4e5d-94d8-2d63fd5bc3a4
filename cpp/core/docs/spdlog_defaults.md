# spdlog 默认设置说明

## 当前Logger配置

### 日志格式
```
[%Y-%m-%d %H:%M:%S.%e] [thread %t] [%^%l%$] %v
```

**格式说明：**
- `%Y-%m-%d %H:%M:%S.%e` - 完整的时间戳（年-月-日 时:分:秒.毫秒）
- `[thread %t]` - 线程ID
- `[%^%l%$]` - 日志级别（带颜色，仅控制台有效）
- `%v` - 实际的日志消息

**示例输出：**
```
[2024-01-15 14:30:25.123] [thread 12345] [info] 这是一条信息日志
[2024-01-15 14:30:25.124] [thread 12345] [warn] 这是一条警告日志
[2024-01-15 14:30:25.125] [thread 12345] [error] 这是一条错误日志
```

### 文件存储
- **位置**: `logs/` 文件夹
- **命名**: `logs/{filename}.log`
- **示例**: 
  - `Logger::getFileLogger("app")` → `logs/app.log`
  - `Logger::getFileLogger("network")` → `logs/network.log`

## spdlog 默认设置详解

### 1. 日志级别
**默认级别**: `info`

**级别层次**（从低到高）：
- `trace` - 最详细的调试信息
- `debug` - 调试信息
- `info` - 一般信息（默认）
- `warn` - 警告信息
- `error` - 错误信息
- `critical` - 严重错误

### 2. 线程安全
- **控制台logger**: 线程安全（`stdout_color_mt`中的`mt`表示multi-thread）
- **文件logger**: 线程安全（`basic_logger_mt`中的`mt`表示multi-thread）

### 3. 缓冲策略
- **默认**: 自动刷新
- **控制台**: 立即输出
- **文件**: 缓冲写入，程序结束时自动刷新

### 4. 文件处理
- **文件模式**: 追加模式（append）
- **编码**: UTF-8
- **文件权限**: 系统默认权限

### 5. 性能特性
- **异步**: 默认为同步模式
- **队列**: 无队列（同步模式）
- **内存使用**: 低内存占用

### 6. 错误处理
- **文件创建失败**: 抛出异常
- **重复创建**: 返回已存在的logger
- **目录不存在**: 自动创建`logs`目录

## 格式化选项参考

### 时间格式
- `%Y` - 4位年份 (2024)
- `%m` - 月份 (01-12)
- `%d` - 日期 (01-31)
- `%H` - 小时 (00-23)
- `%M` - 分钟 (00-59)
- `%S` - 秒 (00-59)
- `%e` - 毫秒 (000-999)

### 线程信息
- `%t` - 线程ID
- `%P` - 进程ID

### 日志级别
- `%l` - 日志级别名称
- `%^` - 颜色开始标记
- `%$` - 颜色结束标记

### 其他
- `%v` - 实际消息
- `%n` - logger名称
- `%%` - 字面量%

## 使用建议

### 1. 日志级别使用
```cpp
auto logger = Logger::getFileLogger("app");

logger->trace("详细的调试信息");      // 开发阶段使用
logger->debug("调试信息");           // 开发阶段使用
logger->info("正常运行信息");        // 生产环境使用
logger->warn("警告信息");           // 生产环境使用
logger->error("错误信息");          // 生产环境使用
logger->critical("严重错误");       // 生产环境使用
```

### 2. 文件组织
```cpp
// 按模块分离日志文件
auto main_logger = Logger::getFileLogger("main");
auto network_logger = Logger::getFileLogger("network");
auto database_logger = Logger::getFileLogger("database");
auto auth_logger = Logger::getFileLogger("auth");
```

### 3. 性能考虑
- 控制台输出比文件输出慢
- 字符串格式化有开销，避免在高频路径中使用复杂格式
- 文件logger会自动缓冲，性能较好

### 4. 生产环境建议
- 设置合适的日志级别（通常为`info`或`warn`）
- 定期清理日志文件
- 考虑日志轮转（当前版本未实现）

## 当前实现的特点

✅ **优点**：
- 极简设计，易于使用
- 自动创建目录
- 线程安全
- 包含时间和线程信息
- 零配置即可使用

⚠️ **限制**：
- 无日志轮转功能
- 无日志级别动态调整
- 无配置文件支持
- 无异步日志支持

这些限制符合极简设计的理念，如果需要更高级的功能，可以直接使用spdlog的原生API。
