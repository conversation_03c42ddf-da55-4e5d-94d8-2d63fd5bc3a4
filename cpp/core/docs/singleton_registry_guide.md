# 单例注册表系统使用指南

## 概述

本单例注册表系统解决了传统单例模式在复杂应用中的三个核心问题：

1. **跨动态库单例唯一性**：确保同一个单例类在不同动态库中只有一个实例
2. **线程安全**：多线程环境下的安全访问和初始化
3. **依赖关系处理**：解决单例之间的循环依赖问题

## 核心特性

### 1. 跨动态库唯一性
- 使用全局单例注册表管理所有单例实例
- 通过导出符号确保跨动态库的唯一性
- 避免了传统静态局部变量在不同动态库中创建多个实例的问题

### 2. 线程安全
- 使用读写锁保护注册表访问
- 每个单例有独立的创建锁，避免全局锁竞争
- 支持并发读取已创建的单例
- 防止递归创建和死锁

### 3. 依赖关系管理
- 声明式依赖关系定义
- 自动依赖解析和创建顺序管理
- 循环依赖检测和报告
- 按依赖关系逆序销毁

## 使用方法

### 1. 定义单例类

```cpp
#include "zexuan/base/singleton_registry.hpp"

class ConfigManager : public zexuan::Singleton<ConfigManager> {
public:
    ConfigManager() {
        // 初始化代码
    }
    
    // 公共接口
    void setConfig(const std::string& key, const std::string& value);
    std::string getConfig(const std::string& key) const;

private:
    std::unordered_map<std::string, std::string> config_;
};
```

### 2. 注册单例

```cpp
// 无依赖的单例
REGISTER_SINGLETON(ConfigManager);

// 有依赖的单例
REGISTER_SINGLETON(LogManager, ConfigManager);
REGISTER_SINGLETON(DatabaseManager, ConfigManager, LogManager);
```

### 3. 使用单例

```cpp
// 获取单例实例
auto config = ConfigManager::getInstance();
config->setConfig("db_host", "localhost");

// 在单例内部获取依赖
class LogManager : public zexuan::Singleton<LogManager> {
public:
    LogManager() {
        // 安全地获取依赖的单例
        auto config = getDependency<ConfigManager>();
        log_level_ = config->getConfig("log_level");
    }
};
```

### 4. 状态检查

```cpp
auto& registry = zexuan::SingletonRegistry::getInstance();

// 检查是否已注册
bool registered = registry.isRegistered<ConfigManager>();

// 检查是否已创建
bool created = registry.isCreated<ConfigManager>();

// 获取状态
auto state = registry.getState<ConfigManager>();
```

## 高级特性

### 1. 超时控制

```cpp
auto& registry = zexuan::SingletonRegistry::getInstance();
registry.setCreationTimeout(std::chrono::seconds(10));
```

### 2. 手动销毁

```cpp
// 销毁所有单例（按依赖关系逆序）
registry.destroyAll();
```

### 3. 错误处理

```cpp
try {
    auto instance = MySingleton::getInstance();
} catch (const zexuan::SingletonException& e) {
    // 处理单例相关错误
} catch (const zexuan::CircularDependencyException& e) {
    // 处理循环依赖错误
} catch (const zexuan::SingletonCreationTimeoutException& e) {
    // 处理创建超时错误
}
```

## 最佳实践

### 1. 依赖声明
- 明确声明所有依赖关系
- 避免在构造函数中访问未声明的依赖
- 使用 `getDependency<T>()` 而不是直接调用 `T::getInstance()`

### 2. 线程安全
- 单例的成员函数需要自行保证线程安全
- 注册表只保证创建过程的线程安全

### 3. 生命周期管理
- 程序结束时会自动销毁所有单例
- 可以手动调用 `destroyAll()` 提前销毁
- 销毁顺序与依赖关系相反

### 4. 错误处理
- 总是捕获并处理单例相关异常
- 在开发阶段启用循环依赖检测
- 设置合理的创建超时时间

## 性能考虑

1. **创建开销**：首次创建时需要解析依赖关系，后续访问开销很小
2. **内存开销**：每个单例类型有少量元数据开销
3. **锁竞争**：使用细粒度锁减少竞争，已创建的单例访问无锁

## 迁移指南

从传统单例模式迁移：

1. 将基类从 `singleton<T>` 改为 `Singleton<T>`
2. 添加依赖声明 `REGISTER_SINGLETON`
3. 在构造函数中使用 `getDependency<T>()` 获取依赖
4. 处理可能的异常情况

## 编译要求

- C++17 或更高版本
- 支持 `std::shared_mutex`
- 链接 pthread 库（Linux/Unix）

## 示例代码

参见：
- `examples/singleton_registry_example.cpp` - 基本使用示例
- `tests/singleton_registry_test.cpp` - 功能测试示例
