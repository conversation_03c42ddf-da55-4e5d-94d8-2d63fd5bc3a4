#include "zexuan/logger.hpp"
#include "zexuan/config_loader.hpp"
#include <iostream>
#include <filesystem>

namespace zexuan {

// 静态变量定义
bool Logger::thread_pool_initialized_ = false;

void Logger::init(const std::string& config_path) {
    if (is_initialized_) {
        return;
    }

    try {
        // 尝试加载配置文件
        bool config_loaded = false;
        ConfigLoader cfg_loader;
        try {
            cfg_loader.loadFromFile(config_path);

            if (cfg_loader.exists("logger")) {
                // 读取配置文件中的设置
                log_level_ = cfg_loader.get<std::string>("logger.level", "info");
                log_pattern_ = cfg_loader.get<std::string>("logger.pattern", "[%Y-%m-%d %H:%M:%S.%e] [%n] [%^%l%$] [thread %t] %v");
                log_dir_ = cfg_loader.get<std::string>("logger.log_dir", "logs");
                max_file_size_ = cfg_loader.get<size_t>("logger.max_file_size", 5 * 1024 * 1024);
                max_files_ = cfg_loader.get<size_t>("logger.max_files", 3);
                is_async_ = cfg_loader.get<bool>("logger.async", false);
                config_loaded = true;
            }
        } catch (const std::exception& e) {
            // 配置文件加载失败，使用默认配置
            std::cerr << "Warning: Failed to load logger config, using defaults: " << e.what() << std::endl;
        }

        if (!config_loaded) {
            // 使用默认配置
            log_level_ = "info";
            log_pattern_ = "[%Y-%m-%d %H:%M:%S.%e] [%n] [%^%l%$] [thread %t] %v";
            log_dir_ = "logs";
            max_file_size_ = 5 * 1024 * 1024;
            max_files_ = 3;
            is_async_ = false;
        }

        // 创建日志目录
        std::filesystem::create_directories(log_dir_);

        // 如果是异步模式，初始化线程池
        if (is_async_ && !thread_pool_initialized_) {
            // 使用默认值，如果需要从配置文件读取，可以在上面的配置加载部分添加
            size_t queue_size = 8192;
            size_t thread_count = 1;

            // 如果配置已加载，尝试从配置中读取异步参数
            if (config_loaded) {
                try {
                    queue_size = cfg_loader.get<size_t>("logger.async_queue_size", 8192);
                    thread_count = cfg_loader.get<size_t>("logger.thread_count", 1);
                } catch (const std::exception&) {
                    // 使用默认值
                }
            }

            try {
                spdlog::init_thread_pool(queue_size, thread_count);
                thread_pool_initialized_ = true;
            } catch (const spdlog::spdlog_ex& e) {
                throw std::runtime_error(std::string("Failed to initialize async thread pool: ") + e.what());
            }
        }

        // 创建默认的控制台logger
        default_logger_ = spdlog::stdout_color_mt("console");
        default_logger_->set_pattern(log_pattern_);
        
        // 设置日志级别
        if (log_level_ == "trace") default_logger_->set_level(spdlog::level::trace);
        else if (log_level_ == "debug") default_logger_->set_level(spdlog::level::debug);
        else if (log_level_ == "info") default_logger_->set_level(spdlog::level::info);
        else if (log_level_ == "warn") default_logger_->set_level(spdlog::level::warn);
        else if (log_level_ == "error") default_logger_->set_level(spdlog::level::err);
        else if (log_level_ == "critical") default_logger_->set_level(spdlog::level::critical);
        else if (log_level_ == "off") default_logger_->set_level(spdlog::level::off);
        else throw std::runtime_error("Invalid log level: " + log_level_);

        spdlog::set_default_logger(default_logger_);
        is_initialized_ = true;

    } catch (const ConfigLoader::ConfigError& e) {
        throw std::runtime_error(std::string("Failed to load logger configuration: ") + e.what());
    } catch (const std::exception& e) {
        throw std::runtime_error(std::string("Logger initialization failed: ") + e.what());
    }
}

std::shared_ptr<spdlog::logger> Logger::getLogger(const std::string& name) {
    // 先查找是否已存在
    auto it = loggers_.find(name);
    if (it != loggers_.end()) {
        return it->second;
    }

    // 不存在则创建新的文件logger
    auto logger = createFileLogger(name);
    loggers_[name] = logger;
    return logger;
}

std::shared_ptr<spdlog::logger> Logger::createFileLogger(const std::string& name)
{
    try {
        // 构建文件名
        std::string filename = log_dir_ + "/" + name + ".log";

        // 创建文件sink，使用成员变量中的配置
        auto file_sink = std::make_shared<spdlog::sinks::rotating_file_sink_mt>(
            filename, max_file_size_, max_files_);

        std::shared_ptr<spdlog::logger> logger;
        if (is_async_) {
            // 创建异步logger
            logger = std::make_shared<spdlog::async_logger>(
                name,
                file_sink,
                spdlog::thread_pool(),
                spdlog::async_overflow_policy::block
            );
        } else {
            // 创建同步logger
            logger = std::make_shared<spdlog::logger>(name, file_sink);
        }

        // 设置日志格式和级别
        logger->set_pattern(log_pattern_);
        if (log_level_ == "trace") logger->set_level(spdlog::level::trace);
        else if (log_level_ == "debug") logger->set_level(spdlog::level::debug);
        else if (log_level_ == "info") logger->set_level(spdlog::level::info);
        else if (log_level_ == "warn") logger->set_level(spdlog::level::warn);
        else if (log_level_ == "error") logger->set_level(spdlog::level::err);
        else if (log_level_ == "critical") logger->set_level(spdlog::level::critical);
        else if (log_level_ == "off") logger->set_level(spdlog::level::off);

        // 注册logger
        spdlog::register_logger(logger);
        return logger;

    } catch (const spdlog::spdlog_ex& ex) {
        if (default_logger_) {
            default_logger_->error("Failed to create logger: {}", ex.what());
        }
        throw std::runtime_error(std::string("Failed to create logger: ") + ex.what());
    }
}

void Logger::shutdown() {
    if (!is_initialized_) {
        return;
    }

    try {
        // 1. 先刷新所有logger的缓冲区
        for (auto& pair : loggers_) {
            if (pair.second) {
                try {
                    pair.second->flush();
                } catch (const std::exception&) {
                    // 忽略单个logger的flush错误
                }
            }
        }
        if (default_logger_) {
            try {
                default_logger_->flush();
            } catch (const std::exception&) {
                // 忽略默认logger的flush错误
            }
        }

        // 2. 清除默认logger引用，避免spdlog::shutdown()时的冲突
        spdlog::set_default_logger(nullptr);

        // 3. 清理所有logger引用
        loggers_.clear();
        default_logger_.reset();

        // 4. 最后关闭spdlog系统（包括线程池）
        // 使用 try-catch 包装，避免异步线程池相关的错误
        try {
            spdlog::shutdown();
        } catch (const std::exception& e) {
            std::cerr << "spdlog::shutdown() warning: " << e.what() << std::endl;
        }

        // 5. 重置状态标志
        is_initialized_ = false;
        thread_pool_initialized_ = false;

    } catch (const std::exception& e) {
        // 忽略shutdown过程中的异常，避免程序崩溃
        // 但可以记录到stderr
        std::cerr << "Logger shutdown warning: " << e.what() << std::endl;

        // 即使出错也要重置状态
        is_initialized_ = false;
        thread_pool_initialized_ = false;
    }
}

// ============================================================================
// 全局日志接口实现
// ============================================================================

Logger& getGlobalLogger() {
    static Logger instance;
    return instance;
}

void initGlobalLogger(const std::string& config_path) {
    getGlobalLogger().init(config_path);
}

void shutdownGlobalLogger() {
    getGlobalLogger().shutdown();
}

std::shared_ptr<spdlog::logger> getDefaultLogger() {
    return getGlobalLogger().getDefaultLogger();
}

std::shared_ptr<spdlog::logger> getNamedLogger(const std::string& name) {
    return getGlobalLogger().getLogger(name);
}

} // namespace zexuan
