#include "zexuan/base/dynamic_library_loader.hpp"
#include <algorithm>

#ifdef _WIN32
    #include <windows.h>
#else
    #include <dlfcn.h>
#endif

namespace zexuan {
namespace base {

DynamicLibraryLoader::DynamicLibraryLoader() {
    // 构造函数实现
}

DynamicLibraryLoader::~DynamicLibraryLoader() {
    unloadAllLibraries();
}

bool DynamicLibraryLoader::loadLibrary(const std::string& libPath) {
    // 检查是否已经加载
    if (isLibraryLoaded(libPath)) {
        lastError_ = "Library already loaded: " + libPath;
        return true;
    }

    // 标准化库名称
    std::string normalizedPath = normalizeLibraryName(libPath);

#ifdef _WIN32
    LibraryHandle handle = LoadLibraryA(normalizedPath.c_str());
    if (!handle) {
        lastError_ = "Failed to load library: " + normalizedPath + " - " + getSystemError();
        return false;
    }
#else
    LibraryHandle handle = dlopen(normalizedPath.c_str(), RTLD_LAZY);
    if (!handle) {
        lastError_ = "Failed to load library: " + normalizedPath + " - " + getSystemError();
        return false;
    }
#endif

    // 保存库句柄
    loadedLibraries_[libPath] = handle;
    lastError_.clear();
    
    std::cout << "Successfully loaded library: " << normalizedPath << std::endl;
    return true;
}

bool DynamicLibraryLoader::unloadLibrary(const std::string& libPath) {
    auto it = loadedLibraries_.find(libPath);
    if (it == loadedLibraries_.end()) {
        lastError_ = "Library not loaded: " + libPath;
        return false;
    }

    LibraryHandle handle = it->second;

#ifdef _WIN32
    if (!FreeLibrary(handle)) {
        lastError_ = "Failed to unload library: " + libPath + " - " + getSystemError();
        return false;
    }
#else
    if (dlclose(handle) != 0) {
        lastError_ = "Failed to unload library: " + libPath + " - " + getSystemError();
        return false;
    }
#endif

    loadedLibraries_.erase(it);
    lastError_.clear();
    
    std::cout << "Successfully unloaded library: " << libPath << std::endl;
    return true;
}

void* DynamicLibraryLoader::getFunctionAddress(const std::string& libPath, const std::string& functionName) {
    auto it = loadedLibraries_.find(libPath);
    if (it == loadedLibraries_.end()) {
        lastError_ = "Library not loaded: " + libPath;
        return nullptr;
    }

    LibraryHandle handle = it->second;

#ifdef _WIN32
    void* funcAddr = GetProcAddress(handle, functionName.c_str());
    if (!funcAddr) {
        lastError_ = "Function not found: " + functionName + " in " + libPath + " - " + getSystemError();
        return nullptr;
    }
#else
    // 清除之前的错误
    dlerror();
    void* funcAddr = dlsym(handle, functionName.c_str());
    const char* error = dlerror();
    if (error) {
        lastError_ = "Function not found: " + functionName + " in " + libPath + " - " + std::string(error);
        return nullptr;
    }
#endif

    lastError_.clear();
    return funcAddr;
}

bool DynamicLibraryLoader::isLibraryLoaded(const std::string& libPath) const {
    return loadedLibraries_.find(libPath) != loadedLibraries_.end();
}

void DynamicLibraryLoader::unloadAllLibraries() {
    for (auto& pair : loadedLibraries_) {
        LibraryHandle handle = pair.second;
        
#ifdef _WIN32
        FreeLibrary(handle);
#else
        dlclose(handle);
#endif
    }
    
    loadedLibraries_.clear();
    std::cout << "All libraries unloaded" << std::endl;
}

std::string DynamicLibraryLoader::getLastError() const {
    return lastError_;
}

std::vector<std::string> DynamicLibraryLoader::getLoadedLibraries() const {
    std::vector<std::string> libraries;
    for (const auto& pair : loadedLibraries_) {
        libraries.push_back(pair.first);
    }
    return libraries;
}

std::string DynamicLibraryLoader::normalizeLibraryName(const std::string& libName) {
    std::string normalized = libName;
    
    // 移除 "lib" 前缀（如果存在）
    if (normalized.length() >= 3 && normalized.substr(0, 3) == "lib") {
        normalized = normalized.substr(3);
    }
    
    // 移除扩展名
    size_t dotPos = normalized.find_last_of('.');
    if (dotPos != std::string::npos) {
        normalized = normalized.substr(0, dotPos);
    }
    
    // 根据平台添加适当的前缀和后缀
#ifdef _WIN32
    normalized = normalized + ".dll";
#else
    normalized = "lib" + normalized + ".so";
#endif
    
    return normalized;
}

std::string DynamicLibraryLoader::getSystemError() const {
#ifdef _WIN32
    DWORD errorCode = GetLastError();
    if (errorCode == 0) {
        return "No error";
    }
    
    LPSTR messageBuffer = nullptr;
    size_t size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        NULL, errorCode, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPSTR)&messageBuffer, 0, NULL);
    
    std::string message(messageBuffer, size);
    LocalFree(messageBuffer);
    return message;
#else
    const char* error = dlerror();
    return error ? std::string(error) : "No error";
#endif
}

} // namespace base
} // namespace zexuan
