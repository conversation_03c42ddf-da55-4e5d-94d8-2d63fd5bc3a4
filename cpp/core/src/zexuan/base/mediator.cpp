/**
 * @file mediator.cpp
 * @brief Simplified Mediator pattern implementation for zexuan project
 * <AUTHOR> from NXLoadSrvMedLib.cpp
 * @date 2024
 */

#include "zexuan/base/mediator.hpp"
#include "zexuan/base/observer.hpp"

namespace zexuan {
namespace base {

// BaseMediator implementation

BaseMediator::BaseMediator() : initialized_(false) {
}

bool BaseMediator::initialize() {
    if (initialized_) {
        return true; // Already initialized
    }

    // Clear any existing registrations
    {
        std::lock_guard<std::mutex> lock(observersMutex_);
        observers_.clear();
    }

    initialized_ = true;
    return true;
}

bool BaseMediator::registerObserver(int observerId, Observer* observer, std::string& errorMsg) {
    if (!initialized_) {
        errorMsg = "Mediator not initialized";
        return false;
    }

    if (!observer) {
        errorMsg = "Observer cannot be null";
        return false;
    }

    if (observerId < 0) {
        errorMsg = "Observer ID must be non-negative";
        return false;
    }

    std::lock_guard<std::mutex> lock(observersMutex_);

    // Check if observer is already registered
    if (observers_.find(observerId) != observers_.end()) {
        errorMsg = "Observer with ID " + std::to_string(observerId) + " already registered";
        return false;
    }

    // Register the observer
    observers_[observerId] = observer;

    return true;
}

bool BaseMediator::unregisterObserver(int observerId, std::string& errorMsg) {
    if (!initialized_) {
        errorMsg = "Mediator not initialized";
        return false;
    }

    std::lock_guard<std::mutex> lock(observersMutex_);

    auto it = observers_.find(observerId);
    if (it == observers_.end()) {
        errorMsg = "Observer with ID " + std::to_string(observerId) + " not found";
        return false;
    }

    observers_.erase(it);

    return true;
}

int BaseMediator::sendMessage(const Message& message, std::string& description) {
    if (!initialized_) {
        description = "Mediator not initialized";
        return -1;
    }

    // Get target address from message
    uint8_t targetId = message.getTarget();

    std::lock_guard<std::mutex> lock(observersMutex_);

    auto it = observers_.find(static_cast<int>(targetId));
    if (it == observers_.end()) {
        description = "Observer with ID " + std::to_string(targetId) + " not found";
        return -1;
    }

    Observer* observer = it->second;
    if (!observer) {
        description = "Observer pointer is null";
        return -1;
    }

    try {
        observer->onNotify(nullptr, message);
        description = "Message sent to observer " + std::to_string(targetId) + " successfully";
        return 0;
    } catch (const std::exception& e) {
        description = "Exception in observer notification: " + std::string(e.what());
        return -1;
    }
}

size_t BaseMediator::getObserverCount() const {
    std::lock_guard<std::mutex> lock(observersMutex_);
    return observers_.size();
}

bool BaseMediator::isObserverRegistered(int observerId) const {
    std::lock_guard<std::mutex> lock(observersMutex_);
    return observers_.find(observerId) != observers_.end();
}
} // namespace base
} // namespace zexuan
