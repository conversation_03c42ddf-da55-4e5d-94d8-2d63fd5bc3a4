/**
 * @file subject.cpp
 * @brief Simplified Subject pattern implementation for zexuan project
 * <AUTHOR> from NXSubject.cpp
 * @date 2024
 */

#include "zexuan/base/subject.hpp"
#include "zexuan/base/observer.hpp"
#include "zexuan/base/mediator.hpp"
#include <algorithm>
#include <stdexcept>

namespace zexuan {
namespace base {

// BaseSubject implementation

BaseSubject::BaseSubject(int id, const std::string& description)
    : id_(id), description_(description) {
    if (id < 0) {
        throw std::invalid_argument("Subject ID must be non-negative");
    }
}

void BaseSubject::attach(std::shared_ptr<Observer> observer) {
    if (!observer) {
        throw std::invalid_argument("Observer cannot be null");
    }

    std::lock_guard<std::mutex> lock(observersMutex_);

    // Check if observer is already attached
    auto it = std::find(observers_.begin(), observers_.end(), observer);
    if (it == observers_.end()) {
        observers_.push_back(observer);
    }
}

void BaseSubject::detach(std::shared_ptr<Observer> observer) {
    if (!observer) {
        return;
    }

    std::lock_guard<std::mutex> lock(observersMutex_);

    auto it = std::find(observers_.begin(), observers_.end(), observer);
    if (it != observers_.end()) {
        observers_.erase(it);
    }
}

void BaseSubject::notify(const Message& message) {
    std::lock_guard<std::mutex> lock(observersMutex_);

    for (auto& observer : observers_) {
        if (observer) {
            observer->onNotify(this, message);
        }
    }
}

void BaseSubject::setMediator(std::shared_ptr<Mediator> mediator) {
    mediator_ = mediator;
}

int BaseSubject::sendMessage(const Message& message) {
    if (!mediator_) {
        return -1; // No mediator available
    }

    // Create a copy of the message and set the source address
    Message msgCopy = message;
    msgCopy.setSource(static_cast<uint8_t>(id_));

    std::string description;
    return mediator_->sendMessage(msgCopy, description);
}

} // namespace base
} // namespace zexuan
