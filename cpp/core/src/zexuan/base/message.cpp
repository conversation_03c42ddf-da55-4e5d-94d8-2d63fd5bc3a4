/**
 * @file message.cpp
 * @brief Message implementation based on IEC 60870-5-103 protocol
 * <AUTHOR> from observer.cpp
 * @date 2024
 */

#include "zexuan/base/message.hpp"

namespace zexuan {
namespace base {

// Message implementation

Message::Message()
    : typ_(0), vsq_(0), cot_(0), source_(0), target_(0), fun_(0), inf_(0) {
}

Message::Message(uint8_t typ, uint8_t vsq, uint8_t cot, uint8_t source,
                uint8_t fun, uint8_t inf)
    : typ_(typ), vsq_(vsq), cot_(cot), source_(source), target_(0), fun_(fun), inf_(inf) {
}

size_t Message::getMessageSize() const {
    // Fixed header size: TYP(1) + VSQ(1) + COT(1) + SOURCE(1) + TARGET(1) + FUN(1) + INF(1) = 7 bytes
    return 7 + variableStructure_.size();
}

size_t Message::serialize(std::vector<uint8_t>& buffer) const {
    size_t totalSize = getMessageSize();
    buffer.clear();
    buffer.reserve(totalSize);

    // Serialize fixed header fields
    buffer.push_back(typ_);
    buffer.push_back(vsq_);
    buffer.push_back(cot_);
    buffer.push_back(source_);
    buffer.push_back(target_);
    buffer.push_back(fun_);
    buffer.push_back(inf_);

    // Serialize variable structure
    buffer.insert(buffer.end(), variableStructure_.begin(), variableStructure_.end());

    return totalSize;
}

size_t Message::deserialize(const std::vector<uint8_t>& buffer, size_t offset) {
    if (buffer.size() < offset + 7) {
        return 0; // Not enough data for fixed header
    }

    size_t pos = offset;

    // Deserialize fixed header fields
    typ_ = buffer[pos++];
    vsq_ = buffer[pos++];
    cot_ = buffer[pos++];
    source_ = buffer[pos++];
    target_ = buffer[pos++];
    fun_ = buffer[pos++];
    inf_ = buffer[pos++];

    // Deserialize variable structure (remaining bytes)
    variableStructure_.clear();
    if (pos < buffer.size()) {
        variableStructure_.assign(buffer.begin() + pos, buffer.end());
        pos = buffer.size();
    }

    return pos - offset;
}

// 文本处理方法的实现已经在头文件中内联实现

} // namespace base
} // namespace zexuan
