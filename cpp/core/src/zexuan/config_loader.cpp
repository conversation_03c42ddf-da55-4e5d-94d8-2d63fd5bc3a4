#include "zexuan/config_loader.hpp"
#include "zexuan/utils/string_utils.hpp"
#include <fstream>
#include <iostream>

namespace zexuan {

void ConfigLoader::loadFromFile(const PathType& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        throw ConfigError("Failed to open config file: " + filepath.string());
    }

    try {
        file >> config_;
    } catch (const nlohmann::json::exception& e) {
        throw ConfigError("Failed to parse config file: " + std::string(e.what()));
    }
}

void ConfigLoader::loadFromString(std::string_view json_str) {
    try {
        config_ = JsonType::parse(json_str);
    } catch (const nlohmann::json::exception& e) {
        throw ConfigError("Failed to parse JSON string: " + std::string(e.what()));
    }
}

void ConfigLoader::loadFromJson(const JsonType& json) {
    config_ = json;
}

void ConfigLoader::saveToFile(const PathType& filepath, bool pretty) const {
    std::ofstream file(filepath);
    if (!file.is_open()) {
        throw ConfigError("Failed to open file for writing: " + filepath.string());
    }

    try {
        if (pretty) {
            file << config_.dump(4);
        } else {
            file << config_.dump();
        }
    } catch (const nlohmann::json::exception& e) {
        throw ConfigError("Failed to write config: " + std::string(e.what()));
    }
}

std::string ConfigLoader::toString(bool pretty) const {
    try {
        return pretty ? config_.dump(4) : config_.dump();
    } catch (const nlohmann::json::exception& e) {
        throw ConfigError("Failed to serialize config: " + std::string(e.what()));
    }
}

bool ConfigLoader::exists(const std::string& path) const {
    try {
        getNodeByPath(path);
        return true;
    } catch (const ConfigError&) {
        return false;
    }
}

void ConfigLoader::remove(const std::string& path) {
    auto parts = utils::StringUtils::split(path, '.');
    if (parts.empty()) {
        throw ConfigError("Invalid path: empty path");
    }

    try {
        JsonType* current = &config_;
        for (size_t i = 0; i < parts.size() - 1; ++i) {
            if (!current->contains(parts[i])) {
                throw ConfigError("Path not found: " + path);
            }
            current = &(*current)[parts[i]];
        }

        if (!current->contains(parts.back())) {
            throw ConfigError("Path not found: " + path);
        }

        current->erase(parts.back());
    } catch (const nlohmann::json::exception& e) {
        throw ConfigError("Failed to remove path: " + std::string(e.what()));
    }
}

void ConfigLoader::clear() {
    config_.clear();
}

const ConfigLoader::JsonType& ConfigLoader::getNodeByPath(const std::string& path) const {
    if (path.empty()) {
        return config_;
    }

    const JsonType* current = &config_;
    for (const auto& part : utils::StringUtils::split(path, '.')) {
        if (!current->contains(part)) {
            throw ConfigError("Path not found: " + path);
        }
        current = &(*current)[part];
    }
    return *current;
}

ConfigLoader::JsonType& ConfigLoader::getNodeByPath(const std::string& path) {
    return const_cast<JsonType&>(const_cast<const ConfigLoader*>(this)->getNodeByPath(path));
}

std::vector<std::string> ConfigLoader::splitPath(const std::string& path) {
    return utils::StringUtils::split(path, '.');
}

// ============================================================================
// 全局配置实例实现
// ============================================================================

ConfigLoader& getGlobalConfig() {
    static ConfigLoader instance;
    return instance;
}

void initGlobalConfig(const std::filesystem::path& config_path) {
    getGlobalConfig().loadFromFile(config_path);
}

} // namespace zexuan
