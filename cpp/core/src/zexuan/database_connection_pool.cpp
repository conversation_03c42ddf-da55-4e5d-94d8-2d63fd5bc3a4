#include "zexuan/database_connection_pool.hpp"
#include "zexuan/config_loader.hpp"
#include <stdexcept>
#include <sstream>

namespace zexuan {

void DatabaseConnectionPool::init(const std::string& config_path) {
    try {
        std::lock_guard<std::mutex> lock(mutex_);
        
        if (is_initialized_) {
            throw std::runtime_error("数据库连接池已经初始化");
        }

        // 加载配置文件
        auto cfg_loader = ConfigLoader::getInstance();
        cfg_loader->loadFromFile(config_path);

        // 检查必需的配置节点是否存在
        if (!cfg_loader->exists("database")) {
            throw std::runtime_error("数据库配置节点未找到");
        }

        // 读取数据库配置
        std::string type = cfg_loader->get<std::string>("database.type");
        std::string db_name = cfg_loader->get<std::string>("database.db_name");
        std::string user = cfg_loader->get<std::string>("database.user");
        std::string password = cfg_loader->get<std::string>("database.password");
        std::string unix_socket = cfg_loader->get<std::string>("database.unix_socket");


        pool_size_ = cfg_loader->get<size_t>("database.pool_size");
        

        // 构建连接字符串
        connection_string_ = buildConnectionString(type, db_name, user, password, unix_socket);
        
        is_initialized_ = true;
        is_shutdown_ = false;

        // 预创建连接
        for (size_t i = 0; i < pool_size_; ++i) {
            connections_.push(createConnection());
        }
    } catch (const ConfigLoader::ConfigError& e) {
        throw std::runtime_error(std::string("加载数据库配置失败: ") + e.what());
    } catch (const std::exception& e) {
        throw std::runtime_error(std::string("数据库连接池初始化失败: ") + e.what());
    }
}

std::string DatabaseConnectionPool::buildConnectionString(
    const std::string& type,
    const std::string& db_name,
    const std::string& user,
    const std::string& password,
    const std::string& unix_socket)
{
    std::ostringstream conn_str;
    conn_str << type << "://";
    
    if (!db_name.empty()) {
        conn_str << "db=" << db_name << " ";
    }
    
    if (!user.empty()) {
        conn_str << "user=" << user << " ";
    }
    
    if (!password.empty()) {
        conn_str << "password='" << password << "' ";
    }
    
    if (!unix_socket.empty()) {
        conn_str << "unix_socket=" << unix_socket;
    }
    
    return conn_str.str();
}

std::shared_ptr<soci::session> DatabaseConnectionPool::getConnection(
    std::chrono::milliseconds timeout) 
{
    std::unique_lock<std::mutex> lock(mutex_);

    if (!is_initialized_) {
        throw std::runtime_error("数据库连接池未初始化");
    }

    if (is_shutdown_) {
        throw std::runtime_error("数据库连接池已关闭");
    }

    // 等待可用连接
    bool has_connection = cv_.wait_for(lock, timeout, 
        [this] { return !connections_.empty(); });

    if (!has_connection) {
        throw std::runtime_error("获取数据库连接超时");
    }

    auto conn = connections_.front();
    connections_.pop();
    
    return conn;
}

void DatabaseConnectionPool::releaseConnection(
    std::shared_ptr<soci::session> conn) 
{
    if (!conn) return;

    std::lock_guard<std::mutex> lock(mutex_);
    
    if (is_shutdown_) {
        return;  // 如果连接池已关闭，直接丢弃连接
    }

    try {
        // 测试连接是否有效
        *conn << "SELECT 1";
        
        // 连接有效，放回池中
        connections_.push(conn);
        cv_.notify_one();
    }
    catch (...) {
        // 连接无效，创建新连接放回池中
        connections_.push(createConnection());
        cv_.notify_one();
    }
}

size_t DatabaseConnectionPool::getAvailableConnections() const {
    std::lock_guard<std::mutex> lock(mutex_);
    return connections_.size();
}

void DatabaseConnectionPool::shutdown() {
    std::lock_guard<std::mutex> lock(mutex_);
    
    if (!is_initialized_ || is_shutdown_) {
        return;
    }

    is_shutdown_ = true;
    
    // 清空连接池
    while (!connections_.empty()) {
        connections_.pop();
    }
}

std::shared_ptr<soci::session> DatabaseConnectionPool::createConnection() {
    try {
        return std::make_shared<soci::session>(connection_string_);
    }
    catch (const std::exception& e) {
        throw std::runtime_error(
            std::string("创建数据库连接失败: ") + e.what());
    }
}

} // namespace zexuan
