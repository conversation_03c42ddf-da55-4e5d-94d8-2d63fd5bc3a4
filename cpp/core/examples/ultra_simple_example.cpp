/**
 * @file ultra_simple_example.cpp
 * @brief 展示极简化后的ConfigLoader和Logger使用方式
 */

#include "zexuan/config_loader.hpp"
#include "zexuan/logger.hpp"
#include <iostream>

using namespace zexuan;

int main() {
    std::cout << "极简ConfigLoader和Logger使用示例\n";
    std::cout << "==================================\n\n";
    
    // === ConfigLoader 使用示例 ===
    std::cout << "=== ConfigLoader 使用 ===\n";
    
    ConfigLoader config;
    config.loadFromFile("config.json");
    
    // 获取配置值（带默认值）
    std::string db_host = config.get<std::string>("host", "localhost");
    int db_port = config.get<int>("port", 3306);
    bool debug = config.get<bool>("debug", false);
    
    std::cout << "Database Host: " << db_host << std::endl;
    std::cout << "Database Port: " << db_port << std::endl;
    std::cout << "Debug Mode: " << (debug ? "true" : "false") << std::endl;
    
    // === Logger 使用示例 ===
    std::cout << "\n=== Logger 使用 ===\n";
    
    // 获取控制台logger
    auto console_logger = Logger::getConsoleLogger();
    console_logger->info("这是控制台日志消息");
    console_logger->warn("这是警告消息");
    console_logger->error("这是错误消息");
    
    // 获取文件logger
    auto file_logger = Logger::getFileLogger("app");
    file_logger->info("这是文件日志消息");
    file_logger->debug("这是调试消息");
    
    // 获取另一个文件logger
    auto network_logger = Logger::getFileLogger("network");
    network_logger->info("网络模块日志");
    
    std::cout << "\n所有示例执行完成！" << std::endl;
    std::cout << "检查 app.log 和 network.log 文件查看日志输出" << std::endl;
    
    return 0;
}
