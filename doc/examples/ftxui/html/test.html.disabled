<!doctype html>
<html lang="en_US">
<head>
  <meta charset="utf-8">


  <style>
    html, body, textarea{
      margin:0;
      padding:0;
      width: 100%;
      height: 100%;
      overflow:hidden;
    }

    body {
      background-color:gray;
    };

    textarea {
      white-space: pre-wrap;
      background-color:white;
    }
  </style>

  <script type="text/c++">
    #include <iostream>

    #include <ftxui/screen/screen.hpp>
    #include <ftxui/dom/elements.hpp>

    int main() {
      using namespace ftxui;
      auto document =
        hbox(
          window(text(" main frame ") | hcenter,
            vbox(
              text("Line 1"),
              text("Line 2"),
              text("Line 3"),
              vbox(
                text("Line 4"),
                text("Line 5"),
                text("Line 6")
              ) | border,
              hbox(
                window(text("frame 2"),
                  vbox(
                    text("Line 4"),
                    gauge(0.5) | border,
                    text("Line 6")
                  )
                ),
                window(text("frame 3"),
                  vbox(
                    text("Line 7"),
                    text("Line 8"),
                    text("Line 9")
                  )
                )
              ),
              text("footer footer footer footer footer")
            )
          ),
          filler()
        );
      auto screen = Screen::Create(Dimension::Fit(document));
      Render(screen, document);
      std::cout << screen.ToString() << std::endl;
      return 0;
    }
  </script> 
</head>
<body>
  <textarea id="box"></textarea>
</body>
  <script>
    window.Module = {
      'print': function(text) {
        console.log(text);
        document.getElementById("box").value += text + '\n';
      }
    };
  </script>
</html>
