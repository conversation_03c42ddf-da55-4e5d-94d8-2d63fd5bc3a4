set(DIRECTORY_LIB component)

example(button)
example(button_animated)
example(button_in_frame)
example(button_style)
example(canvas_animated)
example(checkbox)
example(checkbox_in_frame)
example(collapsible)
example(composition)
example(custom_loop)
example(dropdown)
example(dropdown_custom)
example(flexbox_gallery)
example(focus)
example(focus_cursor)
example(gallery)
example(homescreen)
example(input)
example(input_in_frame)
example(input_style)
example(linear_gradient_gallery)
example(maybe)
example(menu)
example(menu2)
example(menu_entries)
example(menu_entries_animated)
example(menu_in_frame)
example(menu_in_frame_horizontal)
example(menu_multiple)
example(menu_style)
example(menu_underline_animated_gallery)
example(modal_dialog)
example(modal_dialog_custom)
example(nested_screen)
example(print_key_press)
example(radiobox)
example(radiobox_in_frame)
example(renderer)
example(resizable_split)
example(scrollbar)
example(selection)
example(slider)
example(slider_direction)
example(slider_rgb)
example(tab_horizontal)
example(tab_vertical)
example(textarea)
example(toggle)
example(window)
example(with_restored_io)
