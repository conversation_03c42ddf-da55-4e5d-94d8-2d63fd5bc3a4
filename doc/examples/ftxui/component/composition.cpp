// Copyright 2021 <PERSON>. All rights reserved.
// Use of this source code is governed by the MIT license that can be found in
// the LICENSE file.
#include <memory>  // for allocator, shared_ptr, __shared_ptr_access
#include <string>  // for operator+, to_string

#include "ftxui/component/captured_mouse.hpp"  // for ftxui
#include "ftxui/component/component.hpp"  // for Button, Horizontal, Renderer
#include "ftxui/component/component_base.hpp"      // for ComponentBase
#include "ftxui/component/screen_interactive.hpp"  // for ScreenInteractive
#include "ftxui/dom/elements.hpp"  // for text, separator, Element, operator|, vbox, border

using namespace ftxui;

// An example of how to compose multiple components into one and maintain their
// interactiveness.
int main() {
  auto left_count = 0;
  auto right_count = 0;

  auto left_buttons = Container::Horizontal({
      Button("Decrease", [&] { left_count--; }),
      <PERSON><PERSON>("Increase", [&] { left_count++; }),
  });

  auto right_buttons = Container::<PERSON><PERSON>({
      But<PERSON>("Decrease", [&] { right_count--; }),
      <PERSON><PERSON>("Increase", [&] { right_count++; }),
  });

  // Renderer decorates its child with a new rendering function. The way the
  // children reacts to events is maintained.
  auto leftpane = Renderer(left_buttons, [&] {
    return vbox({
               text("This is the left control"),
               separator(),
               text("Left button count: " + std::to_string(left_count)),
               left_buttons->Render(),
           }) |
           border;
  });

  auto rightpane = Renderer(right_buttons, [&] {
    return vbox({
               text("This is the right control"),
               separator(),
               text("Right button count: " + std::to_string(right_count)),
               right_buttons->Render(),
           }) |
           border;
  });

  // Container groups components togethers. To render a Container::Horizontal,
  // it render its children side by side. It maintains their interactiveness and
  // provide the logic to navigate from one to the other using the arrow keys.
  auto composition = Container::Horizontal({leftpane, rightpane});

  auto screen = ScreenInteractive::FitComponent();
  screen.Loop(composition);
  return 0;
}

// Thanks to Chris Morgan for this example!
