@import url(https://fonts.googleapis.com/css?family=Khula:700);

body {
  background-color:#EEE;
  padding:0px;
  margin:0px;
  font-family: Khula, Helvetica, sans-serif;
  font-size: 130%;
}

.page {
  max-width:1300px;
  margin: auto;
  padding: 10px;
}

a {
  box-shadow: inset 0 0 0 0 #54b3d6;
  color: #0087b9;
  margin: 0 -.25rem;
  padding: 0 .25rem;
  transition: color .3s ease-in-out,
              box-shadow .3s ease-in-out;
}

a:hover {
  box-shadow: inset 120px 0 0 0 #54b3d6;
  color: white;
}

h1 {
  text-decoration: underline;
  width:100%;
  background-color: rgba(100,100,255,0.5);
  padding: 10px;
  margin: 0;
}


#selectExample {
  flex:1;
}

#selectExample, #selectExample option {
  font-size: 16px;
  font-family: sans-serif;
  font-weight: 700;
  line-height: 1.3;
  border:0px;
  background-color: #bbb;
  color:black;
}

#selectExample:focus {
  outline:none;
}

#terminal {
  width:100%;
  height 500px;
  height: calc(clamp(200px, 100vh - 300px, 900px));
  overflow: hidden;
  border:none;
  background-color:black;
}

#terminalContainer {
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.75),
  0px 2px 80px 0px rgba(0,0,0,0.50);
}

.fakeButtons {
  height: 10px;
  width: 10px;
  border-radius: 50%;
  border: 1px solid #000;
  margin:6px;
  background-color: #ff3b47;
  border-color: #9d252b;
  display: inline-block;
}

.fakeMinimize {
  left: 11px;
  background-color: #ffc100;
  border-color: #9d802c;
}

.fakeZoom {
  left: 16px;
  background-color: #00d742;
  border-color: #049931;
}

.fakeMenu {
  display:flex;
  flex-direction: row;
  width:100%;
  box-sizing: border-box;
  height: 25px;
  background-color: #bbb;
  color:black;
  margin: 0 auto;
  overflow: hidden;
}
