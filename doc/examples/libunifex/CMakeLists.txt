# Copyright (c) 2019-present, Facebook, Inc.
#
# This source code is licensed under the license found in the
# LICENSE.txt file in the root directory of this source tree.

file(GLOB example-sources "*.cpp")
foreach(file-path ${example-sources})
    string( REPLACE ".cpp" "" file-path-without-ext ${file-path} )
    get_filename_component(file-name ${file-path-without-ext} NAME)
    add_executable( ${file-name} ${file-path})
    target_link_libraries(${file-name} PUBLIC unifex)
    add_test(NAME "example-${file-name}" COMMAND ${file-name})
endforeach()

if (CMAKE_SYSTEM_NAME STREQUAL "Linux")
  file(GLOB linux-example-sources "linux/*.cpp")
  foreach(file-path ${linux-example-sources})
    string( REPLACE ".cpp" "" file-path-without-ext ${file-path} )
    get_filename_component(file-name ${file-path-without-ext} NAME)
    add_executable( ${file-name} ${file-path})
    target_link_libraries(${file-name} PUBLIC unifex)
    add_test(NAME "example-${file-name}" COMMAND ${file-name})
  endforeach()
else()
  if (NOT UNIFEX_NO_EPOLL)
    file(GLOB epoll-example-sources "linux/*epoll_test.cpp")
    foreach(file-path ${epoll-example-sources})
      string( REPLACE ".cpp" "" file-path-without-ext ${file-path} )
      get_filename_component(file-name ${file-path-without-ext} NAME)
      add_executable( ${file-name} ${file-path})
      target_link_libraries(${file-name} PUBLIC unifex)
      add_test(NAME "example-${file-name}" COMMAND ${file-name})
    endforeach()
  endif()
  if (NOT UNIFEX_NO_LIBURING)
    file(GLOB liburing-example-sources "linux/*uring_test.cpp")
    foreach(file-path ${liburing-example-sources})
      string( REPLACE ".cpp" "" file-path-without-ext ${file-path} )
      get_filename_component(file-name ${file-path-without-ext} NAME)
      add_executable( ${file-name} ${file-path})
      target_link_libraries(${file-name} PUBLIC unifex)
      add_test(NAME "example-${file-name}" COMMAND ${file-name})
    endforeach()
  endif()
endif()
