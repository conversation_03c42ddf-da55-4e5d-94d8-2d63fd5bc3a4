# variaidc template
```cpp
void print()
{
}

template<typename T,typename... Types>
void print (const T& firstArg,const Types&... args)
{
    cout<<firstARG<<endl;
    print(arg...);
}
```
作用就是把一堆参数分为一个和其他，然后做递归
... 被称为pack（包）
注意：
```
template<typename...Types>
void test(const Types&... args)
{
    cout<<"Types only :"<<endl;

}
```
这个和firstArg版本可以并存


#  SpacesTemplate、nullptr、auto
模板参数中不用特意加空格了
空指针 nullptr 代替 0 他的类型是 void*
auto 现在可以自动判断变量的类型

# Unifrom Initialization
一致性初始化
全部使用大括号初始化
编译器看到{}就会做出一个  initializer_list<\T>,关联至一个array<\T,n>
如果一个函数参数是一个initializer_list，就不能被自动
# Initializer_list
设初值时，会有默认的初值
注意{}不能自动转换。会警告
和variaidc不一样的是，initializer只能收取同一类型的多个元素
如果要使用，需要用{}进行赋值
但是不一定必须有定义因为会自动分解，然后传入
源代码
```
#include <iostream>
#include <initializer_list>

template<class E>
class initializer_list {
public:
    typedef E value_type;
    typedef const E& reference;
    typedef const E& const_reference;
    typedef size_t size_type;
    typedef const E* iterator;
    typedef const E* const_iterator;

private:
    iterator _Marray;
    size_type _Mlen;

    constexpr initializer_list(const_iterator a, size_type l)
        : _Marray(a), _Mlen(l) {}

public:
    constexpr initializer_list() noexcept
        : _Marray(0), _Mlen(0) {}

    constexpr size_type size() const noexcept { return _Mlen; }

    constexpr const_iterator begin() const noexcept { return _Marray; }

    constexpr const_iterator end() const noexcept { return begin() + _Mlen; }
};

int main() {
    initializer_list<int> ilist = {1, 2, 3, 4, 5};

    for (auto it = ilist.begin(); it != ilist.end(); ++it) {
        std::cout << *it << " ";
    }
    std::cout << std::endl;

    return 0;
}

```
private里有一个函数，编译器起可以调用

# Explicit for ctors taking more than one argument
explicit对一个以上的实参的影响
他主要用在构造函数上
2.0之前，explicit就是让构造函数不要自动的隐式转换
且只有一个实参的时候才有用
但在2.0之后可以在有多个实参的函数前面加

# Range－based for statement
for循环的一种特殊写法
for(decl:coll)
{
statement
}

# =default,=delete
会有默认函数，如果不要可以直接加=delete
=default是在即使定义了也用默认函数的意思
构造函数有了任意一个，那么就不会生成默认函数，所以如果要直接用默认函数，但是如果已经定义了，那么完全一样的同一个函数不能default也不能delete
默认的版本都是public且是inline

只要有point member那就一定要写big-three
# alias template模板化名
化名
using ... =...
例如：
```
template<typename T>
using vec=std::vector<T,MyAlloc<T>>;

vec<int>coll;
std::vector<int,MyAlloc<int>>coll;
```
化名不完全可以代替本名，比如没有办法直接用化名去偏特化
一般而言：
不能如下
```
template<typename Container,typename T>
void test_moveable(Container cntr,T elem)
{
Container<T> c;
}
```

```
#include <iostream>
#include <iterator>

template<typename Container>
void test_moveable(Container c) {
    typedef typename std::iterator_traits<typename Container::iterator>::value_type Valtype;
    std::cout << typeid(Valtype).name() << std::endl;
}

int main() {
    std::vector<int> vec;
    test_moveable(vec);
    return 0;
}
 
```
但这样也必须使用到traits
在下一章中有解决办法

# template template parameter
```
template<typename T,
        template<class T>
            class Container
            >
class XCls
{
private:
    Container<T> c;
public:
    XCls(){
        for (long i=0;i<SIZE;++i)
            c.insert(c.end(),T());
        output_static_data(T());
        Container<T>c1(c);
        Container<T>c2(std::move(c));
        c1.swap(c2);
         
 
    }
}
```
这里的意思是第一个参数作为第二个模板的参数，注意这里的第二个参数是模板那么就要传入类似std::deque这种模板才行
但是在使用时直接传入一个模板，如果里面有默认参数的话，那么是通不过的
如何解决：
```
template<typename T>
using Vec=vector<T,allocator<T>>;

XCls<Mystring ,Vec>s1;

```
# Type Alias, noexcept, override, final
type alias类似于typedef
类化名，当作typedef用就可以了
还有刚才的alias template就比较特殊
他不能用define或者typedef直接替换
因为define直接是带着template一起替换了，而typedef则是直接不能带着template显然不是去我们要的
除此之外还有一些早就有的设定：
using namespace std;
using t::count;
***
noexcept表示函数一定不丢异常
当定义了右值引用的构造函数的时候，必须要用noexcept
如果没有的话，就没有容器模板敢使用它
一般只有vector是成长类型，会需要move
***
override会强制要求函数重载的参数全部相同
防止你重载函数时定义为一个新函数
***
final则表示这个函数不能被子类继续重载
也可以用来让这个类不能被继承

# decltype
```
map<string,float>coll;
decltype(coll)::value_type elem;
```
在以前必须要表示出它的原型才行
把它当成typeof就可以了
```
template<typename T1,typename T2>
decltype(x+y)add(T1 x,T2 y);
```
这编译不过，因为x，y定义在decltype后面
所以要想下面这么用
```
auto add(...)->decltype(x+y);
```
这个形式和lambdas很像，但它并不是lambdas
它可以直接获得它的类型然后使用::iterator直接获得他的迭代器类型
面对lambda，我们经常只有object没有type，要获得type，常常要用到decltype


# lambdas
只要是[]开头，那就是lambdas
再{}函数体后面直接加()就是直接调用
也可以auto l=[]{}
然后l（）；就可以了
` [导入符号](参数)mutable thrpwSpec -> retType返回类型{}; `
后面只要有一个参数就要加()
[]里面可以放=或者&，表示传值或者是引用
注意，按值传入的值只能是前面的，后面的编译器传入不进去
mutable表示是可变的
如果没有mutable却在函数体中改变了内容，那么就会报错
如果要用后面的值，那么可以使用引用
```
int x = 1; int y = 2;
auto plus = [=] (int a, int b) -> int { return x + y + a + b; };
int c = plus(1, 2);
```
编译器的翻译结果为
```
class LambdaClass
{
public:
    LambdaClass(int xx, int yy)
    : x(xx), y(yy) {}

    int operator () (int a, int b) const
    {
        return x + y + a + b;
    }

private:
    int x;
    int y;
}

int x = 1; int y = 2;
LambdaClass plus(x, y);
int c = plus(1, 2);
 
```
捕获列表，对应LambdaClass类的private成员。

参数列表，对应LambdaClass类的成员函数的operator()的形参列表

mutable，对应 LambdaClass类成员函数 operator() 的const属性 ，但是只有在捕获列表捕获的参数不含有引用捕获的情况下才会生效，因为捕获列表只要包含引用捕获，那operator()函数就一定是非const函数。

返回类型，对应 LambdaClass类成员函数 operator() 的返回类型

函数体，对应 LambdaClass类成员函数 operator() 的函数体。

引用捕获和值捕获不同的一点就是，对应的成员是否为引用类型。

正常的使用：
```
auto cmp = [](const Person& p1, const Person& p2) {
    return p1.lastname < p2.lastname ||
           (p1.lastname == p2.lastname && p1.firstname < p2.firstname);
};

std::set<Person,decltype(cmp)>coll(cmp);
```
这里为什么coll后面还要传一个对象呢？
因为如果不传的话set会调用decltype(cmp)的默认构造函数，但是declype没有默认构造函数，所以必须要穿一个实例进去才行
# 重回variadic templates
变化的可能是个数也可能是类型
利用参数个数注意递减的特性，
实现递归函数调用
```
template<typename T,typename... Types>
void printX(const T& firstArg,const Types&... args)
{
cout<<...;
printX(args ...);
}

printX()
{}

```
要是需要知道args的大小的话
可以直接sizeof...(args)
```
template<typename... Types>
void printX(const Types&...args)
{}
```
最上面比上面这个特别一些，所以算特化版本，所以下面这个永远不会产生出来
可以用可变参数模板重写printf（）
***
如果要处理类型一样的多个数据，那么没有必要使用多参数模板，可以直接使用initializer_list
max 如果用多参数模板实现的话
```
int maximum(in n)
{
return n;
}
template<typename...Args>
int maximum(int n,Args... args)
{
return std::max(n,maximum(args...));
}
```
内部一直循环，max也只是比较两个参数而已

***
以异于一般的方式处理first元素和last元素
```
template <typename... Args>
ostream& operator<<(ostream& os, const tuple<Args...>& t) {
    os << "(";
    PRINT_TUPLE<0, sizeof...(Args), Args...>::print(os, t);
    return os << ")";
}

template <int IDX, int MAX, typename... Args>
struct PRINT_TUPLE {
    static void print(ostream& os, const tuple<Args...>& t) {
        os << get<IDX>(t) << (IDX+1 < MAX ? ", " : "");
        PRINT_TUPLE<IDX+1, MAX, Args...>::print(os, t);
    }
};

template <int MAX, typename... Args>
struct PRINT_TUPLE<MAX, MAX, Args...> {
    static void print(ostream& os, const tuple<Args...>& t) {}
};

get<IDX>(t)是tuple的一个接口
```

关于tuple如何实现，详情看stl标准库内容
主要就是自己private继承自己
tuple如何实现：
```cpp
template <typename... Values>
class tuple;

template <>
class tuple<> {
    // 空元组，没有任何成员
};

template <typename Head, typename... Tail>
class tuple<Head, Tail...> : private tuple<Tail...> {
private:
    using inherited = tuple<Tail...>;

public:
    // 构造函数，初始化成员变量 m_head 和继承类 inherited
    tuple(Head v, Tail... vtail) : m_head(v), inherited(vtail...) {}

    // 返回头部元素
    typename Head::type head() const { return m_head; }

    // 返回尾部元组
    const inherited& tail() const { return *this; }

protected:
    Head m_head; // 头部元素
};

```
自己继承自己
这里的tail是怎么实现return*this但是返回的的尾部呢？
构造是由内到外，所以后面的在前面先构造，这里的数据就是有m_head，然后传回使用*this返回类型是inherited&，这样的话就因为this指向这个类，但是inherited这个类型只有被继承的大小，所以就可以准确去取出需要的数据了。
这里的Head::type对int等基础类型而言没有办法读出，所以可以用aotu head() ->ddecltype(m_head)。但是也可以直接用Head作为类型直接使用

*** 
用于递归复合
就是把tuple的tail也作为类的保护属性，复合起来

#  Rvalue references and Move Semantics
 Rvalue references右值引用
 为了解决非必要拷贝
 左值，可以出现在左边
 右值，只能出现在右边
 string的临时对象是右值竟然也可以进行赋值
 &&就是右值引用
 以前只有左值可以只用指针，引用现在也可以实现类似指针的作用
 虽然以前的引用类似指针，但一般也是深拷贝，所以会比较慢，现在是直接指向那一块内存，但是这会比较危险，所以右值拷贝之后move，原来的东西就不能用了，直接摧毁原来的指针
 move函数就是把左值当作右值来用

# Perfect Forwarding
copy和move再构造和赋值时都要有两个版本
不完美的快递：
rvalue经由forward（）传给另一函数却变成了Lvalue
因为再传递过程中他变成了一个named object
如果要完美，需要使用forward

不会抛出异常的移动构造函数
拷贝构造函数通常伴随着内存分配操作，因此很可能会抛出异常；移动构造函数一般是移动内存的所有权，所以一般不会抛出异常。
C++11中新引入了一个noexcept关键字，用来向程序员，编译器来表明这种情况。
重要的设计
 MyString& operator=(MyString& str) noexcept {
        ++MAsqn; 
        if (this != &str) {
            if (_data) delete  _data;
            _len = str._len;
            _data = str._data;
            str._len = 0;
            str._data = nullptr;
        }
        return *this;
    }
    
    virtual ~MyString() noexcept {
        ++Dtor; 
        if (_data) delete  _data;
    }
# std::forward与完美转发
## 引用折叠

如果两个引用中至少其中一个引用是左值引用，那么折叠结果就是左值引用；否则折叠结果就是右值引用。为了加深理解，示例如下： using T = int &;
 T& r1;  // int& & r1 -> int& r1
 T&& r2; // int& && r2 -> int& r2
  
 using U = int &&;
 U& r3;  // int&& & r3 -> int& r3
 U&& r4; // int&& && r4 -> int&& r4

## std::forward函数
实现完美转发的关键是使用std::forward函数。std::forward是一个条件转发函数模板，根据参数的左值或右值属性进行转发。
forward的实现就是返回&&值，详情看引用折叠

## 利用std::forward实现完美转发
C++完美转发是指一种能够传递函数参数或对象的同样类型（例如左值或右值属性）和cv限定符（const或volatile）的方式，同时保留原参数的准确数值类别和cv限定符的转发机制。完美转发通过使用引用折叠机制和std::forward函数来实现。

完美转发应用实例
首先定义一个对象CData，具体说明看注释：
```
#include <stdio.h>
#include <unistd.h>
#include <iostream>

class CData
{
public:
	CData() = delete;
	CData(const char* ch) : data(ch)    // 构造函数，涉及资源的复制
	{
		std::cout << "CData(const char* ch)" << std::endl;
	}
	CData(const std::string& str) : data(str)  // 拷贝构造函数，涉及资源的复制
	{
		std::cout << "CData(const std::string& str)" << std::endl;
	}
	CData(std::string&& str) : data(str)    // 移动构造函数，不涉及资源的复制！！！
	{
		std::cout << "CData(std::string&& str)" << std::endl;
	}
	~CData()   // 析构函数
	{
		std::cout << "~CData()" << std::endl;
	}
private:
	std::string data;   // 表示类内部管理的资源
};
```
假如我们封装了一个操作，主要是用来创建对象使用（类似设计模式中的工厂模式），这个操作要求如下：

1. 可以接受不同类型的参数，然后构造一个对象的指针。

2. 性能尽可能高。（这里需要高效率，故对于右值的调用应该使用CData(std::string&& str)移动函数操作）

1）不使用std::forward实现

假设我们不使用std::forward，那么要提高函数参数转发效率，我们使用右值引用（万能引用）作为模板函数参数：
```
template<typename T>
CData* Creator(T&& t) { // 利用&&万能引用，引用折叠： T&& && -> T&&; T&& & -> T&
	return new CData(t);
}
int main(void) {
    std::string str1 = "hello";  
    std::string str2 = " world";
    CData* p1 = Creator(str1);       // 参数折叠为左值引用，调用CData构造函数
    CData* p2 = Creator(str1 + str2);// 参数折叠为右值引用，但在Creator函数中t仍为左值，调用CData构造函数！！！
    delete p2;
    delete p1;

    return 0;
}
```
g++编译上述程序，可得如下结果，印证了注释中的说明：


可以看出，在不使用std::forward的情况下，即使传入了右值引用，也无法在Creator函数中触发CData的移动构造函数，从而造成了额外的资源复制损耗。

2）使用std::forward实现

使用std::forward即可完美解决上述问题：
```
template<typename T>
CData* Creator(T&& t) {
    return new CData(std::forward<T>(t));
}
int main(void) {
    std::string str1 = "hello";
    std::string str2 = " world";
    CData* p1 = Creator(str1);        // 参数折叠为左值引用，调用CData构造函数
    CData* p2 = Creator(str1 + str2); // 参数折叠为右值引用，通过std::forward转发给CData，调用移动构造函数
    delete p2;
    delete p1;

    return 0;
}
```
g++编译上述程序，可得如下结果，印证了注释中的说明：


可以看出，使用了std::forward之后，可以将传入的函数参数按照其原类型进一步传入参数中，从而使右值引用的参数类型可以触发类的移动构造函数，从而避免不必要的资源复制操作，提高参数转移效率。
# array
内部就是一个数组
2.9没有构造函数，也没有析构函数
