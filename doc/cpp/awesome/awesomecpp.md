# Frameworks  框架
Boost ⚡ - 大量通用 C++ 库. [Boost]
C++ Workflow ⚡ - C++ 并行计算和异步网络引擎. [Apache2]
Dlib ⚡ - 一个用 C++ 编写真实世界机器学习和数据分析应用程序的工具包。[Boost] 网站
Folly - Facebook 开发并使用的开源 C++ 库. [Apache2]
Seastar - 一个先进的开源 C++ 框架，适用于现代硬件上的高性能服务器应用程序。[Apache-2.0 许可证] seastar.io

# cli命令行界面
FTXUI - C ++ 功能终端用户界面. [MIT]
CLI11 - 仅包含标头的单文件或多文件 C++11 库，用于简单和高级 CLI 解析. [BSD]
# Compression  压缩
zstd - Zstandard - 快速实时压缩算法。由 Facebook 开发。[BSD]

# Concurrency  并发
Intel TBB - 英特尔® 线程构建模块. [Apache2]
Taskflow - 通用并行和异构任务编程系统. （从 Cpp-Taskflow 重命名）[MIT]

# Cryptography  密码学
Bcrypt - 跨平台文件加密实用程序。加密文件可在所有支持的操作系统和处理器之间移植。[BSD]
OpenSSL - 一个强大、商业级、功能齐全的开源加密库。[Apache] 网站

# Database  数据库
ODB - 一个开源、跨平台、跨数据库的 C++ 对象关系映射 (ORM) 系统. [GPLv2]
MySQL++ - MySQL C API 的 C++ 包装器. [LGPL]
TinyORM - 现代 C++ ORM 库。 [麻省理工学院] 网站

# test
Google Test - Google C++ 测试框架. [BSD]

# Inter-process communication 进程间通信
bRPC - bRPC 是一个使用 C++ 语言编写的工业级 RPC 框架，常用于搜索、存储、机器学习、广告、推荐等高性能系统。[Apache2] 网站

# Logging  日志记录
fmtlog - 高性能的 fmtlib 风格日志库，延迟以纳秒为单位. [MIT]

# Web Application Framework Web 应用程序框架
Drogon - 基于 C++17/20 的高性能 HTTP 应用程序框架. [麻省理工学院]

CPP-JWT - 用于 C++ 的 JSON Web Token 库. [MIT]

# Coding Style Tools  编码风格工具
ClangFormat - 格式化 C/C++/Obj-C 代码的工具。
