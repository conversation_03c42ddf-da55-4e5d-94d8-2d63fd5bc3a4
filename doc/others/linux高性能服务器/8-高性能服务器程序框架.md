# 服务器模型
### c/s模型
使用select函数同时监听多个客户端
当访问量过大时，可能所有客户端都将得到很慢的响应

### p2p模型
提供一个发现服务器，用来提供查找服务，甚至可以提供内容服务
# 服务器编程框架
服务器基本模块
1. i/o处理单元
2. 逻辑单元
3. 网络存储单元
4. 请求队列

i/o处理单元是服务器管理客户连接的模块
逻辑单元通常是一个进程或线程，用来分析并处理客户数据，然后将结果传递给i/o处理单元或者直接发送给客户端
网络存储单元可以是数据库、缓存、文件，不是必须的
请求队列是各单元之间的通信方式的抽象
# i/o模型
socket默认是阻塞的，可以使用：
1. SOCK_NONBLOCK
2. fcntl系统调用F_SETFL
设置为非阻塞的

可能被阻塞的系统调用包括accept,send,recv,connect
非阻塞io执行的系统调用总是立即返回，如果没有已经发生，则返回-1
所以只有已经发生的情况下操作非阻塞io才能提高效率，所以经常会和io复用以及sigio信号一起使用（io通知机制）
<br>
常用的io复用函数：
1. select
2. poll
3. epoll_wait
sigio信号也可以用来报告io事件

i/o模型对比
阻塞io  程序阻塞于读写函数
io复用  阻塞于io服用系统调用，可以同时监听多个io事件，对io本身的读写操作是非阻塞的
sigio信号   信号触发读写就绪事件，用户程序执行读写操作，程序没有阻塞阶段
异步io  内核执行读写操作并触发读写完成事件。程序没有阻塞阶段
# 两种高效的事件处理模式
服务器通常需要处理三类事件：
1. io事件
2. 信号
3. 定时事件

两种高效方式
1. reactor和proactor
2. 通常分别是同步和异步

### Reactor
要求主线程只负责监听文件描述上是否有事件发生，有的话立即通知工作线程
使用io模型（epoll_wait为例）
1. 主线程往epoll内核时间表中注册socket上的读就绪事件
2. 主线程调用epoll_wait等待socket上有数据可读
3. 有数据可读时epoll_wait通知主线程
4. 睡眠在请求队列上的某个工作线程被唤醒，他从socket读取数据，并处理，然后往epoll内核时间表中注册该socket上的写就绪是啊今
5. 主线程调用epoll_wait等待socket可写
6. 当socket可写时，epoll_wait通知主线程
7. 睡眠的线程被唤醒，他往socket上写入服务器处理客户请求的结果

### Proactor
将所有io操作都交给主线程和内核来处理，工作线程仅仅负责业务逻辑
以（`aio_read`和`aio_write`为例），工作流程：
1. 主线程调用aio_read函数向内核注册socket上的读完成事件，并告诉内核用户读缓冲区的位置，以及完成时如何通知应用程序
2. 主线程继续处理其他逻辑
3. 当socket上的数据都读入用户缓冲区后，内核将向应用程序发送一个信号，通知 应用程序数据已经可用
4. 预先定义好的信号处理函数选择一个工作线程来处理客户请求，处理完后，aio_write函数向内核注册socket上的写完成事件
5. 主线程继续处理其他逻辑
6. 用户缓冲区的数据被写入socket后，内核将发送一个信号，已通知应用程序数据已经发送完毕
7. 应用程序预先定义好的信号处理函数选择一个工作线程来做善后处理，不如决定是否关闭socket

### 使用同步io方式模拟Ptoactor模式
epoll_wait
1. 主线程往epoll内核事件表中注册socket上的读就绪事件
2. 主线程调用epoll_wait等待socket上有数据可读
3. 当socket上有数据可读时，epoll_wait通知主线程，主线程循环读取数据，直到没有数据
4. 唤醒工作线程，然后往epoll内核中注册socket上的写就绪时间
5. 主线程调用epoll_wait等待socket可写
6. 当socket可写时，epoll_wait通知主线程，主线程往socket中写入服务器处理客户请求的结果
# 两种高效的并发模式
由于io操作的速度远没有cpu的计算速度快，所以同时执行多个任务会显著提升cpu的利用率

服务器主要有两种并发编程模式：
1. 半同步/半异步
2. 领导者/追随者

### 半同步/半异步模式
同步就是按顺序直接进行
异步就是做一半使用信号让内核运行程序
半同步/半异步模式：
    异步线程只有一个，由主线程来充当。他负责监听所有socket上的事件，当有socket
    接时，会进入请求队列，和其他请求竞争空余工作线程
缺点：
    主线程和工作线程共享请求队列，请求队列工作时会拜拜浪费cpu时间
    每个工作线程同一时间只能处理一个客户请求
    
如何高效：
    主线程和工作线程都使用epoll_wait()
    
### 领导者/追随者模式
程序都仅有一个领导者线程，如果检测到io事件，那么就推选出新的领导者线程，然后处理io事件，此时新的领导者等待新的io事件
组件：
 1. 句柄集
    句柄表示io资源，句柄集使用`wait_for_event`监听句柄上的io事件
    领导者调用绑定到handle上的事件处理器来处理事件
    领导者使用句柄集中的register_handle来将句柄和事件处理器绑定
 2. 线程集
    线程集时所有工作线程的管理者，他负责各线程的同步，线程集中的线程在任意时间必须处于下列状态：
    * leader 领导者身份
    * processing 正在处理事件，进入此状态后，可以使用`promote_new_leader`方法推选新的领导者，处理完事件后，如果当前线程集中没有领导者，则他将成为新的领导者，否则直接变成追随者
    * follower 追随者，通过线程集的join方法等待称为新的领导者，也肯能被当前的领导者指定处理新的任务
    注意：使用线程集时会有多个操作修改他，所以需要一个成员synchronizer来同步这两个操作
3. 事件处理器和具体的事件处理器
    通常包括一个或多个回调函数handle_event。当句柄上有事件发生时，领导者就执行与之绑定的事件处理器中的回调函数。
    这种模式不需要在线程之间传递任何额外的数据，也不需要上面那种线程之间同步，但是仅支持一个事件源集合，因此不能让每个工作线程独立地管理多个客户连接
# 有限状态机
逻辑单元内部的一种高效编程手法
### 状态独立的有限状态机
有的应用层协议头部包含数据包类型字段，每种类型可以映射为逻辑单元的一种执行状态
### 带状态转移的有限状态机
处理完一个状态的事件后，改变他的状态，下次检测的时候就会发生不同的事件
从状态机，用于解析出一行内容
主状态机，用于从buffer中取出所有完整的行




```
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <assert.h>
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <errno.h>
#include <string.h>
#include <fcntl.h>
 
#define BUFFER_SIZE 4096/*读缓冲区大小*/
/*主状态机的两种可能状态，分别表示：当前正在分析请求行，当前正在分析头部字段*/
enum CHECK_STATE { CHECK_STATE_REQUESTLINE = 0, CHECK_STATE_HEADER, CHECK_STATE_CONTENT };
/*从状态机的三种可能状态，即行的读取状态，分别表示：读取到一个完整的行、行出错和行数据尚且不完整*/
enum LINE_STATUS { LINE_OK = 0, LINE_BAD, LINE_OPEN };
/*服务器处理HTTP请求的结果：NO_REQUEST表示请求不完整，需要继续读取客户数据；GET_REQUEST表示获得了一个完整的客户请求；BAD_REQUEST表示客户请求有语法错误；FORBIDDEN_REQUEST表示客户对资源没有足够的访问权限；INTERNAL_ERROR表示服务器内部错误；CLOSED_CONNECTION表示客户端已经关闭连接了*/
enum HTTP_CODE { NO_REQUEST, GET_REQUEST, BAD_REQUEST, FORBIDDEN_REQUEST, INTERNAL_ERROR, CLOSED_CONNECTION };
/*为了简化问题，我们没有给客户端发送一个完整的HTTP应答报文，而只是根据服务器的处理结果发送如下成功或失败信息*/
static const char* szret[] = { "I get a correct result\n", "Something wrong\n" };
 
/*从状态机，用于解析出一行内容*/
LINE_STATUS parse_line( char* buffer, int& checked_index, int& read_index )
{
    char temp;
/*checked_index指向buffer（应用程序的读缓冲区）中当前正在分析的字节，read_index指向buffer中客户数据的尾部的下一字节。buffer中第0～checked_index字节都已分析完毕，第checked_index~(read_index-1)字节由下面的循环挨个分析*/
    for ( ; checked_index < read_index; ++checked_index )
    {
        /*获得当前要分析的字节*/
        temp = buffer[ checked_index ];
        /*如果当前的字节是“\r”，即回车符，则说明可能读取到一个完整的行*/
        if ( temp == '\r' )
        {
            /*如果“\r”字符碰巧是目前buffer中的最后一个已经被读入的客户数据，那么这次分析没有读        取到一个完整的行，返回LINE_OPEN以表示还需要继续读取客户数据才能进一步分析*/
            if ( ( checked_index + 1 ) == read_index )
            {
                return LINE_OPEN;
            }
            /*如果下一个字符是“\n”，则说明我们成功读取到一个完整的行*/
            else if ( buffer[ checked_index + 1 ] == '\n' )
            {
                buffer[ checked_index++ ] = '\0';
                buffer[ checked_index++ ] = '\0';
                return LINE_OK;
            }
            /*否则的话，说明客户发送的HTTP请求存在语法问题*/
            return LINE_BAD;
        }
 
        /*如果当前的字节是“\n”，即换行符，则也说明可能读取到一个完整的行*/
        else if( temp == '\n' )
        {
            if( ( checked_index > 1 ) &&  buffer[ checked_index - 1 ] == '\r' )
            {
                buffer[ checked_index-1 ] = '\0';
                buffer[ checked_index++ ] = '\0';
                return LINE_OK;
            }
            return LINE_BAD;
        }
    }
    /*如果所有内容都分析完毕也没遇到“\r”字符，则返回LINE_OPEN，表示还需要继续读取客户数据才能进一步分析*/
    return LINE_OPEN;
}
 
/*分析请求行*/
HTTP_CODE parse_requestline( char* szTemp, CHECK_STATE& checkstate )
{
    char* szURL = strpbrk( szTemp, " \t" );
    /*如果请求行中没有空白字符或“\t”字符，则HTTP请求必有问题*/
    if ( ! szURL )
    {
        return BAD_REQUEST;
    }
    *szURL++ = '\0';
 
    char* szMethod = szTemp;
    if ( strcasecmp( szMethod, "GET" ) == 0 )/*仅支持GET方法*/
    {
        printf( "The request method is GET\n" );
    }
    else
    {
        return BAD_REQUEST;
    }
 
    szURL += strspn( szURL, " \t" );
    char* szVersion = strpbrk( szURL, " \t" );
    if ( ! szVersion )
    {
        return BAD_REQUEST;
    }
    *szVersion++ = '\0';
    szVersion += strspn( szVersion, " \t" );
    
    /*仅支持HTTP/1.1*/
    if ( strcasecmp( szVersion, "HTTP/1.1" ) != 0 )
    {
        return BAD_REQUEST;
    }
    /*检查URL是否合法*/
    if ( strncasecmp( szURL, "http://", 7 ) == 0 )
    {
        szURL += 7;
        szURL = strchr( szURL, '/' );
    }
 
    if ( ! szURL || szURL[ 0 ] != '/' )
    {
        return BAD_REQUEST;
    }
 
    //URLDecode( szURL );
    printf( "The request URL is: %s\n", szURL );
 
    /*HTTP请求行处理完毕，状态转移到头部字段的分析*/
    checkstate = CHECK_STATE_HEADER;
    return NO_REQUEST;
}
 
/*分析头部字段*/
HTTP_CODE parse_headers( char* szTemp )
{
    /*遇到一个空行，说明我们得到了一个正确的HTTP请求*/
    if ( szTemp[ 0 ] == '\0' )
    {
        return GET_REQUEST;
    }
    else if ( strncasecmp( szTemp, "Host:", 5 ) == 0 )/*处理“HOST”头部字段*/
    {
        szTemp += 5;
        szTemp += strspn( szTemp, " \t" );
        printf( "the request host is: %s\n", szTemp );
    }
    else/*其他头部字段都不处理*/
    {
        printf( "I can not handle this header\n" );
    }
 
    return NO_REQUEST;
}
/*分析HTTP请求的入口函数*/
HTTP_CODE parse_content( char* buffer, int& checked_index, CHECK_STATE& checkstate, int& read_index, int& start_line )
{
    LINE_STATUS linestatus = LINE_OK;/*记录当前行的读取状态*/
    HTTP_CODE retcode = NO_REQUEST;/*记录HTTP请求的处理结果*/
    /*主状态机，用于从buffer中取出所有完整的行*/
    while( ( linestatus = parse_line( buffer, checked_index, read_index ) ) == LINE_OK )
    {
        char* szTemp = buffer + start_line;/*start_line是行在buffer中的起始位置*/
        start_line = checked_index;/*记录下一行的起始位置*/
        
        /*checkstate记录主状态机当前的状态*/
        switch ( checkstate )
        {
            case CHECK_STATE_REQUESTLINE:/*第一个状态，分析请求行*/
            {
                retcode = parse_requestline( szTemp, checkstate );
                if ( retcode == BAD_REQUEST )
                {
                    return BAD_REQUEST;
                }
                break;
            }
            case CHECK_STATE_HEADER:/*第二个状态，分析头部字段*/
            {
                retcode = parse_headers( szTemp );
                if ( retcode == BAD_REQUEST )
                {
                    return BAD_REQUEST;
                }
                else if ( retcode == GET_REQUEST )
                {
                    return GET_REQUEST;
                }
                break;
            }
            default:
            {
                return INTERNAL_ERROR;
            }
        }
    }
 
    /*若没有读取到一个完整的行，则表示还需要继续读取客户数据才能进一步分析*/
    if( linestatus == LINE_OPEN )
    {
        return NO_REQUEST;
    }
    else
    {
        return BAD_REQUEST;
    }
}
 
int main( int argc, char* argv[] )
{
    if( argc <= 2 )
    {
        printf( "usage: %s ip_address port_number\n", basename( argv[0] ) );
        return 1;
    }
    const char* ip = argv[1];
    int port = atoi( argv[2] );
    
    struct sockaddr_in address;
    bzero( &address, sizeof( address ) );
    address.sin_family = AF_INET;
    inet_pton( AF_INET, ip, &address.sin_addr );
    address.sin_port = htons( port );
    
    int listenfd = socket( PF_INET, SOCK_STREAM, 0 );
    assert( listenfd >= 0 );
    
    int ret = bind( listenfd, ( struct sockaddr* )&address, sizeof( address ) );
    assert( ret != -1 );
    
    ret = listen( listenfd, 5 );
    assert( ret != -1 );
    
    struct sockaddr_in client_address;
    socklen_t client_addrlength = sizeof( client_address );
    int fd = accept( listenfd, ( struct sockaddr* )&client_address, &client_addrlength );
    if( fd < 0 )
    {
        printf( "errno is: %d\n", errno );
    }
    else
    {
        char buffer[ BUFFER_SIZE ];/*读缓冲区*/
        memset( buffer, '\0', BUFFER_SIZE );
        int data_read = 0;
        int read_index = 0;/*当前已经读取了多少字节的客户数据*/
        int checked_index = 0;/*当前已经分析完了多少字节的客户数据*/
        int start_line = 0;/*行在buffer中的起始位置*/
        /*设置主状态机的初始状态*/
        CHECK_STATE checkstate = CHECK_STATE_REQUESTLINE;
        while( 1 )/*循环读取客户数据并分析之*/
        {
            data_read = recv( fd, buffer + read_index, BUFFER_SIZE - read_index, 0 );
            if ( data_read == -1 )
            {
                printf( "reading failed\n" );
                break;
            }
            else if ( data_read == 0 )
            {
                printf( "remote client has closed the connection\n" );
                break;
            }
    
            read_index += data_read;
            /*分析目前已经获得的所有客户数据*/
            HTTP_CODE result = parse_content( buffer, checked_index, checkstate,read_index, start_line );
            if( result == NO_REQUEST )/*尚未得到一个完整的HTTP请求*/
            {
                continue;
            }
            else if( result == GET_REQUEST )/*得到一个完整的、正确的HTTP请求*/
            {
                send( fd, szret[0], strlen( szret[0] ), 0 );
                break;
            }
            else/*其他情况表示发生错误*/
            {
                send( fd, szret[1], strlen( szret[1] ), 0 );
                break;
            }
        }
        close( fd );
    }
    
    close( listenfd );
    return 0;
}
```
# 提高服务器性能的其他建议
### 池
空间换时间，开始处理客户请求的时候直接从池中获取资源，无需动态分配
池可以分成很多种，大多含义明确

### 数据复制
应用程序应该避免不必要的数据复制，比如ftp服务器，就不需要知道传递的内容是什么

### 上下文切换和锁
进程和线程不宜过多，当线程的数量不大于cpu的数目时，上下文切换就不是问题了
应该尽量使用读写锁，减小锁的粒度
