# fork系统调用
```
#include<sys/types.h>
#include<unistd.h>
pid_t fork(void);
```
每次调用返回两次，父为子进程的pid，子进程则返回0
# exec系列系统调用
```
#include<unistd.h>
extern char** environ;

int execl(const char * path,const char* arg,...);
int execlp(const char * file,const char* arg,...);
int execle(const char * path,const char* arg,...,char * const envp[]);
int execv(const char * path,char * const argv[]);
int execvp(const char * file,char * const argv[]);
int execve(const char * path,char* const argv [],char* const envp[]);

```
path参数指定可执行文件的完整路径
file接收文件名
arg接收可变参数
argv接收参数数组，他们会被传递给新程序的main函数
envp参数用于设置新程序的环境变量
# 处理僵尸进程
什么时候算僵尸进程
1. 子进程结束之后，父进程读取其退出状态之前
2. 父进程结束或者异常终止，而子进程继续运行
下面函数在父进程中调用，以等待子进程的结束，并获取子进程的返回信息，从而避免了僵尸进程的产生
```
#include<sys/types.h>
#include<sys/wait.h>
pid_t wait(int * stat_loc);
pid_t waitpid(pid_t pid,int * stat_loc,int options);
```
wait函数将阻塞进程，直到某个子进程结束运行为止，返回结束运行的子进程的pid，并将该进程的退出状态存诸于stat_loc参数指向的内存中
下面几个宏用来帮助解释子进程的突出状态信息

| 宏                   | 含义                                         |
|----------------------|----------------------------------------------|
| WIFEXITED(stat_val) | 如果子进程正常结束，它就返回一个非0值。     |
| WEXITSTATUS(stat_val)| 如果 WIFEXITED 非0，它返回子进程的退出码。  |
| WIFSIGNALED(stat_val)| 如果子进程是因为一个未捕获的信号而终止，它就返回一个非0值。|
| WTERMSIG(stat_val)   | 如果 WIFSIGNALED 非0，它返回一个信号值。   |
| WIFSTOPPED(stat_val) | 如果子进程意外终止，它就返回一个非0值。   |
| WSTOPSIG(stat_val)   | 如果 WIFSTOPPED 非0，它返回一个信号值。     |

wait的阻塞特性当然不是希望得到的，所以waitpid更为出色
若pid为-1，那么就是等待任意一个子进程结束
options，取WNOHANG，调用将是非阻塞的
若没有结束，那么立即返回0，若退出了，那么就返回-1


当进程退出时，将会给父进程发送SIGCHLD信号，那么就可以监听这个信号
# 信号量
信号量是一种特殊的变量
他只能取自然数值而且只支持两种操作：等待和信号
因为linux已经对这两种概念都有特殊定义，所以称呼他们为P/V
P时，若他的值大于0，就减一，如果为0那么就挂起进程
V时，若有其他进程因为等待而挂起，那么就唤醒，如果没有，就将其值加1
最常用，最简单的信号量就是二进制信号量，只能取0、1两个值
# semget系统调用--创建
创建一个新的信号量集，或者获取一个已经存在信号量集，定义如下：
```
#include <sys/sem.h>
int semget(key_t key,int num_sems,int sem_flags);
```
key参数是一个键值，用来标识一个全局唯一的信号量集
num_sems参数指定要创建/获取的信号量集中信号量的数目，若是已经存在的，按么可以设置成0
sem_flags参数指定一组标志
```
该信号量的权限，其格式和含义与系统调用 open 的 mode 参数相同。
它的低端的 9 个比特是该信号量的权限。
它还可以与 IPC_CREAT 标志按位“或”运算以创建新的信号量集。即使信号量已经存在，semget 也不会产生错误。
可以联合使用 IPC_CREAT 和 IPC_EXCL 标志来确保创建一组新的、唯一的信号量集。
在这种情况下，如果信号量集已经存在，则 semget 返回错误并设置 errno 为 EEXIST。
这种创建信号量的行为类似于使用 O_CREAT 和 O_EXCL 标志调用 open 来排他地打开一个文件。
如果semget用于创建信号量集，则与之关联的内核数据结构体semid_ds将被创建并初始化
```
```
#include<sys/sem.h>
struct ipc_perm
{
    key_t key;键值
    uid_t uid;所有者的有效用户id
    gid_t gid;所有者的有效阻id
    uid_t cuid;创建者的有效用户id
    gid_t cgid;创建者的有效组id
    mode_t mode;访问权限
    省略其他
};

struct semid_ds
{
    struct ipc_perm sem_perm;信号量的操作权限
    unsigned long int sem_nsems;信号量集中的信号量数目
    time_t sem_otime;最后一次调用semop的时间，设置为0
    tiem_t sem_ctime；最后一次调用semctl的时间，设置为当前系统时间
};

```
# semop系统调用--改变
semop相当于执行pv操作
重要的内核变量：
```
unsigned short semval;信号量的值
unsigned short semzcnt;等待信号量变成0的进程数量
unsigned short semncnt;等待信号量增加的进程数量
pid_t sempid;最后一次执行semop
```
semop对信号量的操作实际上就是对这些内核变量的操作
```
#include <sys/sem.h>
int semop(int sem_id,struct sembuf* sem_ops,size_t num_sem_ops);
```
`sem_id`是由semget调用返回的信号量标识符，用以指定被操作的目标信号量集
`sem_ops`指向一个sembuf结构体类型的数组
```
struct sembuf
{
    unsigned short int sem_num;信号量集中信号量的编号，0表示第一个
    short int sem_op;指定操作类型，其可选值为正整数、0、负整数
    short int sem_flg;IPC_NOWAIT，类似非阻塞，SEM_UNDO当进程退出时取消正在进行的semop操作
};
```
`size_t num_sem_ops`表示要执行的操作个数
# semctl系统调用
```
#include<sys/sem.h>
int semctl(int sem_id,int sem_num,int command,...);
```
sem_id semget调用返回的信号量集标识符
sem_num 参数指定被操作的信号量在信号量集中的编号
command 参数指定要执行的命令
有的命令需要调用者传递第四个参数
```
union semun
{
    int val;用于SETVAL
    struct semid_ds* buf;用于IPC_STAT和IPC_SET
    unsignedshort* array;用于GETALL和SETALL命令
    struct seminfo* _buf;用于IPC_INFO命令
};

struct seminfo
{
    int semmap;
    int semmni;系统最多可以拥有的信号量集数目
    int semmns;系统最多可以拥有的信号量数目
    int semmnu;
    int semmsl;一个信号量集最多允许包含的信号量数目
    int semopm;semop一次最多能执行的sem_op操作数目
    int semume;
    int semusz;sem_undo结构体的大小
    int semvmx;最大允许的信号量值
    int semaem;
};
```


```
IPC_STAT
从关联于 semid 的内核数据结构复制数据到 arg.buf 指向的semid_ds 数据结构。参数 semnum 被忽略。调用进程必须在保量集合里有读权限。
IPC_SET
把 arg.buf 指向的 semid_ds结构的一个成员值写入相关于该信号量集合内核结构，同时更新 sem_ctime成员。结构中下列成员被更新：sem_perm.uid、sem_perm.gid 以及sem_perm.mode (低端 9位)。调用进程的有效用户ID必须匹配信号量集合的所有者(sem_perm.uid)或创建者(sem_perm.cuid)，或者调用者必须有特权。参数semnum 被忽略。
IPC_RMID
立即删除信号量集合，唤醒所有因调用semop（）  阻塞在该信号量集合里的所有进程(相应调用会返回错误且 errno被设置为 EIDRM)。调用进程的有效用户ID必须匹配信号量集合的创建者或所有者，或者调用者必须有特权。参数semnum 被忽略。
IPC_INFO （Linux 定义的）
通过 arg.buf 指向的结构返回系统范围内的信号量限制和参数。这个结构的类型是seminfo，如果宏 _GNU_SOURCE 特性宏被定义，则该结构定义在头文件<sys/sem.h> 。
struct  seminfo { 
     int semmap;  // 信号量映射里的条数，内核未使用 
     int semmni;  // 信号量集合的最大个数 
     int semmns;  // 在所有信号量集合里信号量个数上限  
     int semmnu;  // 系统范围内的 undo 结构最大个数，内核未使用 
     int semmsl;  // 一个信号量集合里信号量个数上限 
     int semopm;  // 执行的最大操作个数  
     int semume;  // 每个进程内 undo 结构最大个数，内核未使用
     int semusz;  // 结构 sem_undo 的尺寸 
     int semvmx;  // 信号量值的上限
     int semaem;  // Max. value that can be recorded for
                    semaphore adjustment (SEM_UNDO) 
};

semmsl、semmns、semopm 和 semmni 设置可以通过/proc/sys/kernel/sem 更改。

返回值
失败时 semctl() 返回 -1 并设置 errno 指明错误。


否则该系统调用返回一个依赖于 cmd 的非负值：
GETNCNT
semncnt 的值。
GETPID
sempid 的值。
GETVAL
semval 的值。
GETZCNT
semzcnt 的值。
IPC_INFO
内核内部关于所有信号量集合的记录数组的最大索引值。(这个信息可以用于重复执行 SEM_STAT来获得系统内所有信号量集合的信息。)
SEM_INFO
如同 IPC_INFO。
SEM_STAT
索引为 semid 的信号量集合的标识。
SETALL
用semun.aray中的数据填充由sem_id标识的信号量樂
SETALL
用semun.array中的数值填充由sem_id标识的信号量集中的所有信号量的semval值，同时内核数据中的semid_ds.sem_ctime被更新
SETVAL
将信号量的semval值设置为semun.val，同时内核数据中的semid_ds.sem_ctime被更新
```
# 特殊键值IPC_PRIVATE
无论该信号量是否已经存在，都将创建一个新的信号量
这个名字有些误导，其实应该叫IPC_NEW
因为在semget函数中赋予这个属性后，父进程和子进程都可以对他进行pv操作
# 共享内存
共享内存是最高效的ipc机制，因为他不涉及进程之间的任何数据传输
但我们必须用其他辅助手段来同步进程对共享内存的访问
linux共享内存的api都定义在sys/shm.h头文件中，包括4个系统调用：shmget，shmat,shmdt和shmctl
# shmget系统调用
```
#include<sys/shm.h>
int shmget(key_t key,size_t size,int shnflg);
```
size在获取已经存在的共享内存时可以设置为0
shmflg的使用和含义与semget系统调用的sem_flags参数相同，不过shmget支持两个额外的标志：
1. `SHM_HUGETLB`，类似于mmap的`MAP_HUGETLB`，系统将使用“大页面”来为共享内存分配空间
2. `SHM_NORESERVE`，类似于mmap的`MAP_HUGETLB`标志，不为共享内存保留交换分区
创建时会被初始化的结构体
```
struct shmid_ds
{
    struct ipc_perm shm_perm;共享内存的操作权限
    size_t shm_segsz;共享内存大小
    __time_t shm_atime;对这段共享内存最后一次调用shmat的时间
    __time_t shm_dtime;对这段共享内存最后一次调用shmdt的时间
    __time_t shm_ctime;对这段共享内存最后一次调用shmctl的时间
    __pid_t shm_cpid;创建者的pid
    __pid_t shm_lpid;最后一次执行shmat或shmdt操作的进程的pid
    shmatt_t shm_nattach;目前关联到此共享内存的进程数量
};
```
# shmat和shmdt系统调用
创建共享内存后，需要先将它关联到进程的地址空间中，使用完共享内存后，也需要将它从进程地址空间中分离，有以下函数实现
```
#include<sys/shm.h>
void* shmat(int shm_id,const void *shm_addr,int shmflg);
int shmdt(const void* shm_addr);
```
shm_id是由shmget调用返回的共享内存标识符
shm_addr指定将共享内存关联到进程的哪块地址空间
shmflg可选参数如下：
1. `SHM_RND`，addr为NULL那么就由操作系统选择，addr非空，此标识符未设置，那么就设为addr的空间，若设置此标识，那么共享内存被关联的地址向下圆整到离`shm_addr`最近的SHMLBA的整数倍地址中
2. `SHM_RDONLY`进程仅能读取共享内存中的内容
3. `SHM_REMAP`如果已经关联到一段共享内存，则重新关联
4. `SHM_EXEC`指定对共享内存的执行权限，对共享内存而言，执行权限实际上和读权限是一样的

# chmctl系统调用
```
#include<sys.shm.h>
int shmctl(int shm_id,int command,struct shmid_ds* buf);
```
command支持的所有命令：
| 命令          | 描述                                                         |
|---------------|--------------------------------------------------------------|
| IPC_STAT      | 获取共享内存的状态信息（shmid_ds 结构）                     |
| IPC_SET       | 设置共享内存的权限和控制参数                                 |
| IPC_RMID      | 删除共享内存段                                               |
| SHM_LOCK      | 锁定共享内存段，防止其被交换到磁盘                         |
| SHM_UNLOCK    | 解锁共享内存段，允许其被交换到磁盘                         |
| SHM_STAT      | 获取共享内存的状态信息，与 IPC_STAT 类似，但在一些系统中用于共享内存                   |
| SHM_INFO      | 获取系统共享内存的信息，包括当前已分配的共享内存数量和最大允许的共享内存数量等 |
# 共享内存的posix方法
mmap可以利用MAP_ANONYLOUS标志我们可以实现父子进程之间的匿名内存共享
linux提供了另外一种利用mmap在无关进程之间共享内存的方式
```
#include<sys/mman.h>
#include<sys.stat.h>
#include<fcntl.h>
int shm_open(const char* name,int oflah,mode_t mode);
```
name,应该使用“/somename”的格式
oflag参数指定创建方式，和open调用完全相同
删除过程：
```
int shm_unlink(const char * name);
```
若使用上述函数，那么编译时需要加上链接选项-lrt
# 消息队列
每个数据块都有一个特定的类型，接收方可以根据类型来有选择地接收数据，不用像管道和命名管道那样必须以先进先出的方式接收数据
api都定义在sys/msg.头文件中，包括四个系统调用：
msgget,msgsnd,msgrcv,msgctl
# msgget系统调用
```
#include<sys/msg.h>
int msgget(key_t key,int msgflg);
```
key是一个键值
msgflg与semget系统调用的sem_flags参数相同
会被初始化的结构体
```
struct msqid_ds {
    struct ipc_perm msg_perm;  /* 消息队列的权限信息 */
    time_t          msg_stime; /* 上次发送消息的时间 */
    time_t          msg_rtime; /* 上次接收消息的时间 */
    time_t          msg_ctime; /* 上次变更队列的时间 */
    unsigned long   msg_cbytes; /* 队列中当前的字节数 */
    msgqnum_t       msg_qnum;   /* 队列中当前的消息数量 */
    msglen_t        msg_qbytes; /* 队列的最大字节数 */
    pid_t           msg_lspid;  /* 最后发送消息的进程ID */
    pid_t           msg_lrpid;  /* 最后接收消息的进程ID */
};
```
# msgsnd和msgrcv系统调用
```
#include<sys/msg.h>
int msgsnd(int msqid,const void* msg_ptr,size_t msg_sz,int msgflg);
```
msqid是由msgget调用返回的消息队列标识符
msg_ptr参数指向一个准备发送的消息，必须是以下类型：
```
struct msgbuf
{
    long mtype;消息类型
    char mtext[512];消息数据
};
```
msg_sz是消息的数据部分
msgflg参数控制msgsnd的行为，通常是非阻塞的
```
#include<sys/msg.h>
int msgrcv(int msqid,void* msg_ptr,size_t msg_sz,long int msgtype,int msgflg);
```
msgtype指定接收何种类型的消息
msgflg控制msgrcv的行为
# msgctl系统调用
```
#include<sys/msg.h>
int msgctl(int msgqid,int command,struct msqid_ds* buf);
```
IPC_STAT (0): 这个命令用于获取消息队列的状态信息，并将其存储在 msqid_ds 结构中。这个结构包含了关于消息队列的各种信息，比如消息数量、消息的最大大小等等。
IPC_SET (1): 这个命令用于设置消息队列的属性，比如设置消息队列的权限、最大消息大小等等。这个命令需要提供一个 msqid_ds 结构，指定要设置的属性。
IPC_RMID (2): 这个命令用于删除消息队列。删除消息队列会释放该队列占用的资源，并且该队列上的所有消息都会被丢弃。
# 在进程间传递文件描述符
```
// 功能：在进程间传递文件描述符
// 2023年02月22日 20:11:19
#include <sys/socket.h>
#include <fcntl.h>
#include <stdio.h>
#include <unistd.h>
#include <stdlib.h>
#include <assert.h>
#include <string.h>

// 返回对齐后的长度
static const  int CONTROL_LEN = CMSG_LEN(sizeof(int));
// 发送文件描述符，fd参数是用来传递信息的UNIX域socket
// fd_to_send参数是待发送的文件描述符
void send_fd(int fd, int fd_to_send)
{
    // iovec 定义了一个向量元素，这个结构用作一个多元素数组
    struct iovec iov[1];
    struct msghdr msg;  // 用于接收和发送数据
    char buf[0];

    // iov_base指向一个缓冲区，这个缓冲区是存放的是readv所接收的数据或是writev将要发送的数据。
    iov[0].iov_base = buf;
    iov[0].iov_len = 1; // 接收的最大长度以及实际写入长度
    msg.msg_name = NULL; // 表示消息的目标地址，通常是一个指向 struct sockaddr 结构体的指针
    msg.msg_iov = iov;  // 数据缓冲区
    msg.msg_iovlen = 1; // 数据缓冲区中元素个数

    // 表示控制信息头部的结构体，通常为msghdr中的msg_control字段数据
    // 通常用于发送或接收一些与协议相关的控制信息，例如 IP 的 TTL 值、TCP 的选项等
    cmsghdr cm;
    // 表示辅助数据的长度，包括 struct cmsghdr 结构体的长度和后面跟随的辅助数据的长度。
    cm.cmsg_len = CONTROL_LEN;
    // 表示辅助数据所属协议的层级
    cm.cmsg_level = SOL_SOCKET;
    // 表示辅助数据的类型
    cm.cmsg_type = SCM_RIGHTS; // SCM_RIGHTS表面上传递的是文件描述符，但实际上并不是简单地传递描述符的数字，而是传递描述符背后的 file 文件
    // CMSG_DATA()返回一个指向cmsghdr的数据部分的指针
    *(int *)CMSG_DATA(&cm) = fd_to_send;
    msg.msg_control = &cm; // 设置辅助数据
    msg.msg_controllen = CONTROL_LEN;
    
    sendmsg(fd, &msg, 0);
}

// 接收目标文件描述符
int recv_fd(int fd)
{
    struct iovec iov[1];
    struct msghdr msg;  // 用于接收和发送数据
    char buf[0];

    iov[0].iov_base = buf;
    iov[0].iov_len = 1; // 接收的最大长度以及实际写入长度
    msg.msg_name = NULL; // 表示消息的目标地址，通常是一个指向 struct sockaddr 结构体的指针
    msg.msg_iov = iov;  // 数据缓冲区
    msg.msg_iovlen = 1; // 数据缓冲区中元素个数

    // 表示控制信息头部的结构体，通常为msghdr中的msg_control字段数据
    // 通常用于发送或接收一些与协议相关的控制信息，例如 IP 的 TTL 值、TCP 的选项等
    cmsghdr cm;
    msg.msg_control = &cm;
    msg.msg_controllen = CONTROL_LEN;

    recvmsg(fd, &msg, 0);

    int fd_to_read = *(int *)CMSG_DATA(&cm);
    return fd_to_read;
}

int main()
{
    int pipefd[2];
    int fd_to_pass = 0;
    // 创建父子进程间的管道，文件描述符pipefd[0]和pipefd[1]都是UNIX域socket
    int ret = socketpair(PF_UNIX, SOCK_DGRAM, 0, pipefd);
    assert(ret != -1);

    pid_t pid = fork();
    assert(pid >= 0);

    if(pid == 0) // child
    {
        close(pipefd[0]);
        fd_to_pass = open("test.txt", O_RDWR, 0666);
        // 子进程通过管道将文件描述符发送到父进程
        // 若test.txt 打开失败，则子进程将标准输入文件描述符发送到父进程
        send_fd(pipefd[1], (fd_to_pass) > 0 ? fd_to_pass : 0); //!!!
        close(fd_to_pass);
        exit(0);
    }
    // parent
    close(pipefd[1]);
    fd_to_pass = recv_fd(pipefd[0]); // 父进程从管道接收目标文件描述符
    char buf[1024];
    memset(buf, '\0', 1024);
    read(fd_to_pass, buf, 1024);
    printf("I got fd %d and data %s\n", fd_to_pass, buf);
    close(fd_to_pass);
}
```