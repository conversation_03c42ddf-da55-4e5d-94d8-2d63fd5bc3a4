# linux信号概述
### 发送信号
一个进程给其他进程发送信号的api是kill函数
```
#include<sys/types.h>
#include<signal.h>
int kill (pid_t pid,int sig);
```
`pid>0`信号发送给PID为pid的进程
`pid=0`信号发送给本进程组内的其他进程
`pid=-1`发送给出init进程外的所有进程，但需要有相应权限
`pid<-1`信号发送给组id为-pid的进程组的所有成员

成功返回0，失败返回-1

### 信号处理方式
接收函数原型如下
```
#include<signal.h>
typedef void(*__sighandler_t) (int );
```
唯一的一个整型参数用来指示信号类型，除了用户自定义信号处理函数外，bits/signum.h头文件中还定义了信号的两种其他处理方式：
```
#include<bits/signum.h>
#define SIG_DFL((__sighandler_t ) 0)
#define SIG_IGN((__sighandler_t ) 1)
```
分别表示忽略目标信号和使用目标信号的默认处理方式
默认处理方式：
1. 结束进程 Term
2. 忽略信号 IGn
3. 结束进程并生成核心转储文件   Core
4. 暂停进程 Stop
5. 继续进程 Cont

### linux信号
比较重要的是SIGHUP、SIGPIPE和SIGURG以及SIGALEM、SIGCHLD等

### 终端系统调用
如果处于阻塞状态下接收到信号，并且设置了信号处理函数，则默认情况下系统调用将被中断，可以讲sigaction函数为信号设置SA_RESTART标志以自动重启该被中断的系统调用



4) SIGILL: CPU检测到某进程执行了非法指令。默认动作为终止进程并产生core文件。

5) SIGTRAP:该信号由断点指令或其他trap 指令产生。默认动作为终止进程并产生core文件。

6) SIGABRT:调用abort函数时产生该信号。默认动作为终止进程并产生core文件。

7) SIGBUS:非法访问内存地址，包括内存对齐出错，默认动作为终止进程并产生core文件。

8)SIGFPE:在发生致命的运算错误时发出。不仅包括浮点运算错误，还包括溢出及除数为0等所有的算法错误。默认动作为终止进程并产生core文件。

9) SIGKILL:无条件终止进程。本信号不能被忽略，处理和阻塞。默认动作为终止进程。它向系统管理员提供了可以杀死任何进程的方法。

10) SIGUSR1:用户定义的信号。即程序员可以在程序中定义并使用该信号。默认动作为终止进程。

11) SIGSEGV:指示进程进行了无效内存访问。默认动作为终止进程并产生core文件。

12）SIGUSR2:另外一个用户自定义信号，程序员可以在程序中定义并使用该信号。默认动作为终止进程。

13) SIGPIPE: Broken pipe向一个没有读端的管道写数据。默认动作为终止进程。

14) SIGALRM:定时器超时，超时的时间由系统调用alarm设置。默认动作为终止进程。

15) SIGTERM:程序结束信号，与SIGKILL不同的是，该信号可以被阻塞和终止。通常用来要示程序正常退出。执行shell命令Kill时，缺省产生这个信号。默认动作为终止进程。

16) SIGSTKFLT: Linux 早期版本出现的信号，现仍保留向后兼容。默认动作为终止进程。

17) SIGCHLD:子进程状态发生变化时，父进程会收到这个信号。默认动作为忽略这个信号。

18) SIGCONT:如果进程己停止，则使其继续运行。默认动作为继续/忽略。

19) SIGSTOP:停止进程的执行。信号不能被忽略，处理和阻塞。默认动作为暂停进程。

20）SIGTSTP:停止终端交互进程的运行。按下<ctrl+z>组合键时发出这个信号。默认动作为暂停进程。

21) SIGTTIN:后台进程读终端控制台。默认动作为暂停进程。

22) SIGTTOU:该信号类似于SIGTTIN，在后台进程要向终端输出数据时发生。默认动作为暂停进程。

23) SIGURG:套接字上有紧急数据时，向当前正在运行的进程发出些信号，报告有紧急数据到达。如网络带外数据到达，默认动作为忽略该信号。

24) SIGXCPU:进程执行时间超过了分配给该进程的CPU时间，系统产生该信号并发送给该进程。默认动作为终止进程。

25) SIGXFSZ:超过文件的最大长度设置。默认动作为终止进程。

26) SIGVTALRM:虚拟时钟超时时产生该信号。类似于SIGALRM，但是该信号只计算该进程占用CPU的使用时间。默认动作为终止进程。

27) SGIPROF:类似于SIGVTALRM，它不公包括该进程占用CPU时间还包括执行系统调用时间。默认动作为终止进程。


28)SIGWINCH:窗口变化大小时发出。默认动作为忽略该信号。

29)SIGIO:此信号向进程指示发出了一个异步I0事件。默认动作为忽略。

30)SIGPWR:关机。默认动作为终止进程。

31)SIGSYS:无效的系统调用。默认动作为终止进程并产生core文件。


34)SIGRTMIN-(64)SIGRTMAX: LINUX的实时信号，它们没有固定的含义(可以由用户自定义)。所有的实时信号的默认动作都为终止进程。

# 信号函数
### signal系统调用
```
#include <signal.h>
_sighandler_t signal (int sig ,_sighandler_t _handler);
```
sig指出要捕获的信号类型，`_handler`参数是`_sighandler_t`，用于指定信号sig的处理函数
成功时返回一个函数指针。这个返回值是前一次调用signal函数时传入的函数指针，或者是信号sig对应的默认处理函数指针SIG_DEF（如果是第一次调用signal的话）

### sigaction系统调用
```
#include<signal.h>
int sigaction(int sig,const struct sigaction* act,struct sigaction* oact);
```
sig 指出要捕获的信号类型，act指定新的信号处理方式，oact输出信号先前的处理方式
sigaction结构体中的`sa_hander`成员指定信号处理函数。`sa_mask`成员设置进程的信号掩码，以指定哪些信号不能发送给本进程
```
struct sigaction {
    #ifdef _USE_POSIX199309
    union {
        _sighandler_t sa_handler;
        void (*sa_sigaction)(int, siginfo_t*, void*);
    } _handler;
    #define sa_handler _handler.sa_handler
    #define sa_sigaction _handler.sa_sigaction
    #else
    _sighandler_t sa_handler;
    #endif
    _sigset_t sa_mask;
    int sa_flags;
    void (*sa_restorer)(void);
};

```
sa_falgs选项：
```
SA_NOCLDSTOP	如果 sigaction 的 sig 参数是 SIGCHLD，则设置该标志表示子进程暂停时不生成 SIGCHLD 信号。
SA_NOCLDWAIT	如果 sigaction 的 sig 参数是 SIGCHLD，则设置该标志表示子进程结束时不产生优尸进程。
SA_SIGINFO	使用 sa_sigaction 作为信号处理函数（而不是默认的 sa_handler），它给进程提供更多相关的信息。
SA_ONSTACK	调用由 sigaltstack 函数设置的可选信号栈上的信号处理函数。
SA_RESTART	重新调用被该信号终止的系统调用。
SA_NODEFER	当接收到信号并进入其信号处理函数时，不藏该信号，默认情况下，我们期望进程在处理一个信号时不再接收到同种信号，香则将引起一些竞态条件。
SA_RESETHAND	信号处理函数执行完以后，恢复信号的默认处理方式。
SA_INTERRUPT	中断系统调用。
SA_NOMASK	同 SA_NODEFER。
SA_ONESHOT	同 SA_RESETHAND。
SA_STACK	同 SA_ONSTACK。
```
# 信号集
### 信号集函数
linux使用sigset_t来表示一组信号
```
#include<bits/sigset.h>
#define _SIGSIT_NWORDS (1024/(8*sizeof(unsigned long int)))
typedef struct
{
    unsigned long int __val[_SIGSET_NWORDS];
} __sigset_t;
```
sigset实际上是一个长整形数组，每个元素的每个位表示一个信号，和fd_set类似，linux提供了下一组函数来设置、修改、查询信号集
```
#include<signal.h>
int sigemptyset(sigset_t * _set)                    清空
int sigfillset(sigset_t * _set)                     设置所有信号
int sigaddset(sigset_t * _set,int _signo)           添加
int sigdelset(sigset_t * _set,int _signo)           删除
int sigismember(_const sigset_t * _set,int _signo)  测试
```

### 进程信号掩码
可以使用sigaction结构体的sa_mask成员来设置进程的信号掩码
```
#include<signal.h>
int sigprocmask(int _how,_const sigset_t* _set,sigset_t _ost);
```
`_set`指定新的信号掩码，`_oset`输出原来的信号掩码，`_how`指定设置进城信号掩码的方式
_how参数如下：
```
1. `SIG_BLOCK`  新的进程掩码是当前值和_set指定信号集的并集
2. `SIG_UNBLOCK` 新的进程信号掩码是当前值和~_set信号值的交集，因此_set指定的信号将不被屏蔽
3. `SIG_SETMASK`直接将进程信号掩码设置为_set
```
### 被挂起的信号
设置进程信号掩码后，被屏蔽的信号不能被进程接受，但是如果取消屏蔽，则它立即被进程接收到，如下函数可以获得进程当前被挂起的信号集
```
#include<signal.h>
int sigpending(sigset_t set);
```
set参数用来保存被挂起的信号集
多次接收到同一信号，只能反映一次，所以取消屏蔽后也只能反映一次
# 统一信号源
信号处理函数和程序的主循环是两条不同的执行路线
所以信号处理函数需要尽可能快地执行完毕，以确保该信号不被屏蔽太久
可以把信号的主要处理逻辑放到程序的主循环中，当信号处理函数被触发时通过管道传递给主循环
使用io复用系统调用来监听管道的终端文件描述符上的可读事件，这样，信号事件就能和其他io事件一样被处理，即同一信号源

```
//统一事件源
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <errno.h>
#include <assert.h>
#include <fcntl.h>
#include <pthread.h>
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/epoll.h>
#include <signal.h>
 
#define MAX_EVENT_NUMBER 1024
 
static int pipefd[2];
 
 
int setnonblocking(int fd);			//设置非阻塞
void addfd(int epollfd, int fd);		//添加描述符的事件
void sig_handler(int sig);			//信号处理函数
void addsig(int sig);					//添加信号处理
 
 
int main(int argc, char **argv)
{
	if (argc != 2) {
		fprintf(stderr, "Usage: %s port\n", basename(argv[0]));
		return 1;
	}
	
	int port = atoi(argv[1]);
	int ret = 0;
	int error;
	
	struct sockaddr_in address;
	bzero(&address, sizeof(address));
	address.sin_family = AF_INET;
	address.sin_port = htons(port);
	address.sin_addr.s_addr = htonl(INADDR_ANY);
	
	int sockfd = socket(PF_INET, SOCK_STREAM, 0);	
	if (sockfd == -1)
		return 1;
		
	printf("server start...\n");
	
	//设置地址可重用
	int reuse = 1;
	ret = setsockopt(sockfd, SOL_SOCKET, SO_REUSEADDR, &reuse, sizeof(reuse));
	if (ret == -1) {
		error = errno;
		while ((close(sockfd) == -1) && (errno == EINTR));
		errno = error;
		return 1;
	}
	
	printf("server reuseaddr success\n");
	
	if ((bind(sockfd, (struct sockaddr*)&address, sizeof(address)) == -1) ||
		(listen(sockfd, 5) == -1)) {
		error = errno;
		while ((close(sockfd) == -1) && (errno == EINTR));
		errno = error;
		return 1;
	}
	
	printf("server bind and listen success\n");
	
	epoll_event events[MAX_EVENT_NUMBER];
	
	int epollfd = epoll_create(5);
	if (epollfd == -1) {
		error = errno;
		while ((close(sockfd) == -1) && (errno == EINTR));
		errno = error;
		return 1;
	}
	
	addfd(epollfd, sockfd);
	
	//使用socketpair创建管道，注册pipefd[0]上的可读事件
	ret = socketpair(PF_UNIX, SOCK_STREAM, 0, pipefd);
	if (ret == -1) {
		error = errno;
		while ((close(sockfd) == -1) && (errno == EINTR));
		errno = error;
		return 1;
	}
	
	setnonblocking(pipefd[1]);
	addfd(epollfd, pipefd[0]);
	
	//设置信号处理
	addsig(SIGHUP);
	addsig(SIGCHLD);
	addsig(SIGTERM);
	addsig(SIGINT);
	
	bool stop_server = false;
	
	while (!stop_server) {
		int number = epoll_wait(epollfd, events, MAX_EVENT_NUMBER, -1);
		if (number < 0 && errno != EINTR) {
			fprintf(stderr, "epoll failed\n");
			break;
		}
		
		for (int i = 0; i < number; i++) {
			int listenfd = events[i].data.fd;
			if (listenfd == sockfd) {			//处理新连接
				struct sockaddr_in client_address;
				socklen_t client_addrlength = sizeof(client_address);
				
				int connfd = -1;
				while ( ((connfd = accept(listenfd, (struct sockaddr*)&client_address, &client_addrlength)) == -1) &&
						(connfd == EINTR) );
				
				addfd(epollfd, connfd);
			}
			else if (listenfd == pipefd[0] && events[i].events & EPOLLIN) {	//处理信号
				char signals[1024];
				ret = recv(pipefd[0], signals, sizeof(signals), 0);
				if (ret == -1)
					continue;
				else if (ret == 0)
					continue;
				else {
					//每个信号值占1字节，所以按字节来逐个接收信号
					for (int i = 0; i < ret; i++) {
						switch(signals[i]) {
							case SIGCHLD:
							{
								fprintf(stderr, "recv SIGCHLD\n");
								continue;
								break;
							}
							case SIGHUP:
							{
								fprintf(stderr, "recv SIGHUP\n");
								continue;
								break;
							}
							case SIGTERM:
							{
								fprintf(stderr, "recv SIGTERM, close server\n");
								stop_server = true;
								break;
							}							
							case SIGINT:
							{
								fprintf(stderr, "recv SIGINT, close server\n");
								stop_server = true;
								break;
							}
							default:
								break;
							
						}
					}
				}
			}
			else {
				
			}
		}
				
	}
	
	
	printf("close fds\n");
	close(sockfd);
	close(pipefd[1]);
	close(pipefd[0]);
	
	
	return 0;
}
 
int setnonblocking(int fd)
{
	int old_option = fcntl(fd, F_GETFL);
	int new_option = old_option | O_NONBLOCK;
	fcntl(fd, F_SETFL, new_option);
	return old_option;
}
 
void addfd(int epollfd, int fd)
{
	epoll_event event;
	event.data.fd = fd;
	event.events = EPOLLIN | EPOLLET;
	epoll_ctl(epollfd, EPOLL_CTL_ADD, fd, &event);
	setnonblocking(fd);
}
 
void sig_handler(int sig)
{
	int save_errno = errno;
	int msg = sig;
	send(pipefd[1], (char*)&msg, 1, 0);		//将信号写入管道，以通知主循环
	errno = save_errno;
}
 
void addsig(int sig)
{
	struct sigaction sa;
	memset(&sa, '\0', sizeof(sa));
	sa.sa_handler = sig_handler;
	sa.sa_flags |= SA_RESTART;
	
	sigfillset(&sa.sa_mask);
	assert(sigaction(sig, &sa, NULL) != -1);
}
```
# 网络编程相关信号
### SIGNUP
挂起进程的控制终端，signup信号将被触发
对于没有控制终端的网络后台程序而言，通常利用signup信号来强制服务器后重读配置文件
### SIGPIPE
默认情况下，往一个度端关闭的管道或socket连接中写数据将引发SIGPIPE信号
程序接收到这个信号默认行为是结束进程
可以使用send函数的MSG_NOSIGNAL标志来禁止写操作触发SIGPIPE信号
### SIGURG
带外数据，除了这个信号之外
前面也介绍过使用io复用技术，select等系统调用在接收到带外数据时将返回并向程序报告
# tcp带外数据整理
可以使用MSG_OOB标志的send/recv系统调用来发送/接收带外数据
检测带外数据是否到达的两种方法：
1. io复用系统调用报告的异常
2. SIGURG信号
判断带外数据报在数据流中的具体位置：
使用sockadmark系统调用
