# 半同步\半异步进程池
```
#ifndef PROCESSPOOL_H
#define PROCESSPOOL_H

#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <assert.h>
#include <stdio.h>
#include <unistd.h>
#include <errno.h>
#include <string.h>
#include <fcntl.h>
#include <stdlib.h>
#include <sys/epoll.h>
#include <signal.h>
#include <sys/wait.h>
#include <sys/stat.h>

//封装的进程 类
class process
{
public:
    process() : m_pid( -1 ){}

public:
    pid_t m_pid;
    int m_pipefd[2];
};

//进程池的模版代码
template< typename T >
class processpool
{
private:
    processpool( int listenfd, int process_number = 8 );
public:
    static processpool< T >* create( int listenfd, int process_number = 8 )
    {
        if( !m_instance )
        {
            m_instance = new processpool< T >( listenfd, process_number );
        }
        return m_instance;
    }
    ~processpool()
    {
        delete [] m_sub_process;
    }
    void run();

private:
    void setup_sig_pipe();
    void run_parent();
    void run_child();

private:
    static const int MAX_PROCESS_NUMBER = 16;   //最大进程数量
    static const int USER_PER_PROCESS = 65536;  //最大子进程处理客户的数量
    static const int MAX_EVENT_NUMBER = 10000;  //epoll最多能处理的事件数量
    int m_process_number;                       //进程池中的进程总数
    int m_idx;                                  //子进程在池中的序号
    int m_epollfd;                              //每个进程都有一个epoll内核事件表 用m_epollfd标识
    int m_listenfd;                             //监听的socket
    int m_stop;                                 //子进程通过m_stop来决定是否退出
    process* m_sub_process;                     //保存子进程描述信息
    static processpool< T >* m_instance;
};
//模版类 初始化类中单例
template< typename T >
processpool< T >* processpool< T >::m_instance = NULL;
//用于处理信号的管道 已实现统一的事件原 后面称为信号管道
static int sig_pipefd[2];

//设置文件描述符为非阻塞
/*文件描述符非阻塞是指进程在读写文件或网络连接时，如果没有数据可读或可写，进程会立即返回，而不是一直等待。*/
static int setnonblocking( int fd )
{
    int old_option = fcntl( fd, F_GETFL );
    int new_option = old_option | O_NONBLOCK;
    fcntl( fd, F_SETFL, new_option );
    return old_option;
}
//将相关的文件描述符塞入epoll中 监听其可读可写事件
static void addfd( int epollfd, int fd )
{
    epoll_event event;
    event.data.fd = fd;
    event.events = EPOLLIN | EPOLLET;
    epoll_ctl( epollfd, EPOLL_CTL_ADD, fd, &event );
    setnonblocking( fd );
}
//将相关的文件描述符从epoll中移除
static void removefd( int epollfd, int fd )
{
    epoll_ctl( epollfd, EPOLL_CTL_DEL, fd, 0 );
    close( fd );
}

//将接受到的信号发送给子进程
static void sig_handler( int sig )
{
    int save_errno = errno;
    int msg = sig;
    send( sig_pipefd[1], ( char* )&msg, 1, 0 );
    errno = save_errno;
}
//将指定的信号 与信号处理器handler关联起来 
static void addsig( int sig, void( handler )(int), bool restart = true )
{
    struct sigaction sa;
    memset( &sa, '\0', sizeof( sa ) );
    sa.sa_handler = handler;
    // 当设置了 SA_RESTART 标志后，被信号中断的系统调用（如读/写操作）会在信号处理后自动重启。 重启后再进行新的读取查看是否还有信号需要处理
    if( restart )
    {
        sa.sa_flags |= SA_RESTART;
    }
    // 使用 sigfillset 函数来填充 sa_mask 字段，使得在处理器执行时阻塞所有信号。   请注意，sa_mask 中指定的信号集只会在处理器函数执行期间被阻塞。一旦处理器函数返回，被阻塞的信号将恢复其正常行为。
    sigfillset( &sa.sa_mask );
    assert( sigaction( sig, &sa, NULL ) != -1 );
}

template< typename T >
processpool< T >::processpool( int listenfd, int process_number ) 
    : m_listenfd( listenfd ), m_process_number( process_number ), m_idx( -1 ), m_stop( false )
{
    assert( ( process_number > 0 ) && ( process_number <= MAX_PROCESS_NUMBER ) );

    m_sub_process = new process[ process_number ];
    assert( m_sub_process );
    //创建process_number个子进程 并寄哪里和父进程之间的管道
    for( int i = 0; i < process_number; ++i )
    {
        int ret = socketpair( PF_UNIX, SOCK_STREAM, 0, m_sub_process[i].m_pipefd );
        assert( ret == 0 );
        /*代码创建了一个子进程，并将子进程的PID（或0，如果这是在子进程中）存储在m_sub_process[i].m_pid中。然后，它检查fork()是否成功，如果失败，则使用assert终止程序。*/
        m_sub_process[i].m_pid = fork();
        assert( m_sub_process[i].m_pid >= 0 );
        //这种处理通常用于进程间通信（IPC），其中父进程和子进程通过管道交换数据，但每个进程只保留自己需要的管道端。
        if( m_sub_process[i].m_pid > 0 )//
        {
            //关闭管道的文件描述符m_pipefd[1]。这个文件描述符通常用于写入管道，父进程在创建子进程后通常不再需要写入管道，因此关闭这个描述符。
            close( m_sub_process[i].m_pipefd[1] );
            continue;
        }
        else
        {
            //关闭管道的文件描述符m_pipefd[0]。这个文件描述符通常用于从管道读取数据，子进程可能不需要从管道读取数据，或者它可能稍后以不同的方式读取，因此关闭这个描述符。
            close( m_sub_process[i].m_pipefd[0] );
            m_idx = i;
            //跳出循环。这表示子进程不需要继续参与创建其他子进程的过程。
            break;
        }
    }
}
//这个函数的主要目的是为进程池设置信号处理和管道通信机制，以便能够正确地处理子进程的状态变化，以及通过管道进行进程间的通信 设置epoll监听管道
template< typename T >
void processpool< T >::setup_sig_pipe()
{
    m_epollfd = epoll_create( 5 );
    assert( m_epollfd != -1 );

    int ret = socketpair( PF_UNIX, SOCK_STREAM, 0, sig_pipefd );
    assert( ret != -1 );

    setnonblocking( sig_pipefd[1] );
    addfd( m_epollfd, sig_pipefd[0] );

    addsig( SIGCHLD, sig_handler );
    addsig( SIGTERM, sig_handler );
    addsig( SIGINT, sig_handler );
    addsig( SIGPIPE, SIG_IGN );
}
//父进程的m_idx值为-1 子进程为对应的id
template< typename T >
void processpool< T >::run()
{
    if( m_idx != -1 )
    {
        run_child();
        return;
    }
    run_parent();
}
//子进程运行的路径
template< typename T >
void processpool< T >::run_child()
{
    //m_epollfd 在其内部初始化
    setup_sig_pipe();

    int pipefd = m_sub_process[m_idx].m_pipefd[ 1 ];//写
    addfd( m_epollfd, pipefd );

    epoll_event events[ MAX_EVENT_NUMBER ];
    T* users = new T [ USER_PER_PROCESS ];
    assert( users );
    int number = 0;
    int ret = -1;

    while( ! m_stop )
    {
        number = epoll_wait( m_epollfd, events, MAX_EVENT_NUMBER, -1 );
        if ( ( number < 0 ) && ( errno != EINTR ) )
        {
            printf( "epoll failure\n" );
            break;
        }

        for ( int i = 0; i < number; i++ )
        {
            int sockfd = events[i].data.fd;
            if( ( sockfd == pipefd ) && ( events[i].events & EPOLLIN ) )
            {
                int client = 0;
                /*从父 子 进程之间的管道读取数据 并将结果保存在变量client中 如果读取成功 表示有新的客户连接到来*/
                ret = recv( sockfd, ( char* )&client, sizeof( client ), 0 );
                if( ( ( ret < 0 ) && ( errno != EAGAIN ) ) || ret == 0 ) 
                {
                    continue;
                }
                else
                {
                    struct sockaddr_in client_address;
                    socklen_t client_addrlength = sizeof( client_address );
                    int connfd = accept( m_listenfd, ( struct sockaddr* )&client_address, &client_addrlength );
                    if ( connfd < 0 )
                    {
                        printf( "errno is: %d\n", errno );
                        continue;
                    }
                    addfd( m_epollfd, connfd );
                    /*模版类 T 必须实现init方法 以初始化一个客户连接 我们直接使用connfd来索引逻辑处理对象 以提高程序效率*/
                    users[connfd].init( m_epollfd, connfd, client_address );
                }
            }
            /*如果接受到信号管道传来的信号*/
            else if( ( sockfd == sig_pipefd[0] ) && ( events[i].events & EPOLLIN ) )
            {
                int sig;
                char signals[1024];
                ret = recv( sig_pipefd[0], signals, sizeof( signals ), 0 );
                if( ret <= 0 )
                {
                    continue;
                }
                else
                {
                    for( int i = 0; i < ret; ++i )
                    {
                        switch( signals[i] )
                        {
                            case SIGCHLD:
                            {
                                pid_t pid;
                                int stat;
                                while ( ( pid = waitpid( -1, &stat, WNOHANG ) ) > 0 )
                                {
                                    continue;
                                }
                                break;
                            }
                            case SIGTERM:
                            case SIGINT:
                            {
                                m_stop = true;
                                break;
                            }
                            default:
                            {
                                break;
                            }
                        }
                    }
                }
            }
            /*如果是其他可读数据 必然是客户请求 到来 调用逻辑处理对象的process方法处理*/
            else if( events[i].events & EPOLLIN )
            {
                 users[sockfd].process();
            }
            else
            {
                continue;
            }
        }
    }

    delete [] users;
    users = NULL;
    close( pipefd );
    /*m_listenfd 创建者来关闭这个文件描述符 谁创建谁销毁*/
    //close( m_listenfd );
    close( m_epollfd );
}
//父进程调用的函数
template< typename T >
void processpool< T >::run_parent()
{
    //初始化信号管道
    setup_sig_pipe();

    addfd( m_epollfd, m_listenfd );

    epoll_event events[ MAX_EVENT_NUMBER ];
    int sub_process_counter = 0;
    int new_conn = 1;
    int number = 0;
    int ret = -1;

    while( ! m_stop )
    {
        number = epoll_wait( m_epollfd, events, MAX_EVENT_NUMBER, -1 );
        if ( ( number < 0 ) && ( errno != EINTR ) )
        {
            printf( "epoll failure\n" );
            break;
        }

        for ( int i = 0; i < number; i++ )
        {
            int sockfd = events[i].data.fd;
            if( sockfd == m_listenfd )
            {
                /*如果有新的连接到来 就采取Round Robin方式将其分配给一个子进程处理*/
                int i =  sub_process_counter;
                do
                {
                    if( m_sub_process[i].m_pid != -1 )
                    {
                        break;
                    }
                    i = (i+1)%m_process_number;
                }
                while( i != sub_process_counter );
                
                if( m_sub_process[i].m_pid == -1 )
                {
                    m_stop = true;
                    break;
                }
                sub_process_counter = (i+1)%m_process_number;
                //send( m_sub_process[sub_process_counter++].m_pipefd[0], ( char* )&new_conn, sizeof( new_conn ), 0 );
                send( m_sub_process[i].m_pipefd[0], ( char* )&new_conn, sizeof( new_conn ), 0 );
                printf( "send request to child %d\n", i );
                //sub_process_counter %= m_process_number;
            }
            /*下面处理 父进程接受到的信号*/
            else if( ( sockfd == sig_pipefd[0] ) && ( events[i].events & EPOLLIN ) )
            {
                int sig;
                char signals[1024];
                ret = recv( sig_pipefd[0], signals, sizeof( signals ), 0 );
                if( ret <= 0 )
                {
                    continue;
                }
                else
                {
                    for( int i = 0; i < ret; ++i )
                    {
                        switch( signals[i] )
                        {
                            case SIGCHLD:
                            {
                                pid_t pid;
                                int stat;
                                while ( ( pid = waitpid( -1, &stat, WNOHANG ) ) > 0 )
                                {
                                    for( int i = 0; i < m_process_number; ++i )
                                    {
                                        /*如果进程池中第i个子进程退出 则主进程关闭相应的通道管道 并设置了相应的m_pid为-1 标记该子进程已经退出*/
                                        if( m_sub_process[i].m_pid == pid )
                                        {
                                            printf( "child %d join\n", i );
                                            close( m_sub_process[i].m_pipefd[0] );
                                            m_sub_process[i].m_pid = -1;
                                        }
                                    }
                                }
                                /* 如果所有子进程退出 则父进程也退出*/
                                m_stop = true;
                                for( int i = 0; i < m_process_number; ++i )
                                {
                                    if( m_sub_process[i].m_pid != -1 )
                                    {
                                        m_stop = false;
                                    }
                                }
                                break;
                            }
                            case SIGTERM:
                            case SIGINT:
                            {
                                printf( "kill all the clild now\n" );
                                for( int i = 0; i < m_process_number; ++i )
                                {
                                    int pid = m_sub_process[i].m_pid;
                                    if( pid != -1 )
                                    {
                                        kill( pid, SIGTERM );
                                    }
                                }
                                break;
                            }
                            default:
                            {
                                break;
                            }
                        }
                    }
                }
            }
            else
            {
                continue;
            }
        }
    }

    //close( m_listenfd );
    close( m_epollfd );
}

#endif
```
# 用进程池实现的简单cgi服务器
```
#include <sys/types.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <assert.h>
#include <stdio.h>
#include <unistd.h>
#include <errno.h>
#include <string.h>
#include <fcntl.h>
#include <stdlib.h>
#include <sys/epoll.h>
#include <signal.h>
#include <sys/wait.h>
#include <sys/mman.h>
#include <sys/stat.h>
#include <fcntl.h>

#define BUFFER_SIZE 1024
#define MAX_EVENT_NUMBER 1024
#define PROCESS_COUNT 5
#define USER_PER_PROCESS 65535

struct process_in_pool
{
    pid_t pid;
    int pipefd[2];
};

struct client_data
{
    sockaddr_in address;
    char buf[ BUFFER_SIZE ];
    int read_idx;
};

int sig_pipefd[2];
int epollfd;
int listenfd;
process_in_pool sub_process[ PROCESS_COUNT ];
bool stop_child = false;

int setnonblocking( int fd )
{
    int old_option = fcntl( fd, F_GETFL );
    int new_option = old_option | O_NONBLOCK;
    fcntl( fd, F_SETFL, new_option );
    return old_option;
}

void addfd( int epollfd, int fd )
{
    epoll_event event;
    event.data.fd = fd;
    event.events = EPOLLIN | EPOLLET;
    epoll_ctl( epollfd, EPOLL_CTL_ADD, fd, &event );
    setnonblocking( fd );
}

void sig_handler( int sig )
{
    int save_errno = errno;
    int msg = sig;
    send( sig_pipefd[1], ( char* )&msg, 1, 0 );
    errno = save_errno;
}

void addsig( int sig, void(*handler)(int), bool restart = true )
{
    struct sigaction sa;
    memset( &sa, '\0', sizeof( sa ) );
    sa.sa_handler = handler;
    if( restart )
    {
        sa.sa_flags |= SA_RESTART;
    }
    sigfillset( &sa.sa_mask );
    assert( sigaction( sig, &sa, NULL ) != -1 );
}

void del_resource()
{
    close( sig_pipefd[0] );
    close( sig_pipefd[1] );
    close( listenfd );
    close( epollfd );
}

void child_term_handler( int sig )
{
    stop_child = true;
}

void child_child_handler( int sig )
{
    pid_t pid;
    int stat;
    while ( ( pid = waitpid( -1, &stat, WNOHANG ) ) > 0 )
    {
        continue;
    }
}

int run_child( int idx )
{
    epoll_event events[ MAX_EVENT_NUMBER ];
    int child_epollfd = epoll_create( 5 );
    assert( child_epollfd != -1 );
    int pipefd = sub_process[idx].pipefd[1];
    addfd( child_epollfd, pipefd );
    int ret;
    addsig( SIGTERM, child_term_handler, false );
    addsig( SIGCHLD, child_child_handler );
    client_data* users = new client_data[ USER_PER_PROCESS ];

    while( !stop_child )
    {
        int number = epoll_wait( child_epollfd, events, MAX_EVENT_NUMBER, -1 );
        if ( ( number < 0 ) && ( errno != EINTR ) )
        {
            printf( "epoll failure\n" );
            break;
        }

        for ( int i = 0; i < number; i++ )
        {
            int sockfd = events[i].data.fd;
            if( ( sockfd == pipefd ) && ( events[i].events & EPOLLIN ) )
            {
                int client = 0;
                ret = recv( sockfd, ( char* )&client, sizeof( client ), 0 );
                if( ret < 0 )
                {
                    if( errno != EAGAIN )
                    {
                        stop_child = true;
                    }
                }
                else if( ret == 0 )
                {
                    stop_child = true;
                }
                else
                {
                    struct sockaddr_in client_address;
                    socklen_t client_addrlength = sizeof( client_address );
                    int connfd = accept( listenfd, ( struct sockaddr* )&client_address, &client_addrlength );
                    if ( connfd < 0 )
                    {
                        printf( "errno is: %d\n", errno );
                        continue;
                    }
                    memset( users[connfd].buf, '\0', BUFFER_SIZE );
                    users[connfd].address = client_address;
                    users[connfd].read_idx = 0;
                    addfd( child_epollfd, connfd );
                }
            }
            else if( events[i].events & EPOLLIN )
            {
                int idx = 0;
                while( true )
                {
                    idx = users[sockfd].read_idx;
                    ret = recv( sockfd, users[sockfd].buf + idx, BUFFER_SIZE-1-idx, 0 );
                    if( ret < 0 )
                    {
                        if( errno != EAGAIN )
                        {
                            epoll_ctl( child_epollfd, EPOLL_CTL_DEL, sockfd, 0 );
                            close( sockfd );
                        }
                        break;
                    }
                    else if( ret == 0 )
                    {
                        epoll_ctl( child_epollfd, EPOLL_CTL_DEL, sockfd, 0 );
                        close( sockfd );
                        break;
                    }
                    else
                    {
                        users[sockfd].read_idx += ret;
                        printf( "user content is: %s\n", users[sockfd].buf );
                        idx = users[sockfd].read_idx;
                        if( ( idx < 2 ) || ( users[sockfd].buf[idx-2] != '\r' ) || ( users[sockfd].buf[idx-1] != '\n' ) )
                        {
                            continue;
                        }
                        users[sockfd].buf[users[sockfd].read_idx-2] = '\0';
                        char* file_name = users[sockfd].buf;
                        if( access( file_name, F_OK ) == -1 )
                        {
                            epoll_ctl( child_epollfd, EPOLL_CTL_DEL, sockfd, 0 );
                            close( sockfd );
                            break;
                        }
                        ret = fork();
                        if( ret == -1 )
                        {
                            epoll_ctl( child_epollfd, EPOLL_CTL_DEL, sockfd, 0 );
                            close( sockfd );
                            break;
                        }
                        else if( ret > 0 )
                        {
                            epoll_ctl( child_epollfd, EPOLL_CTL_DEL, sockfd, 0 );
                            close( sockfd );
                            break;
                        }
                        else
                        {
                            close( STDOUT_FILENO );
                            dup( sockfd );
                            execl( users[sockfd].buf, users[sockfd].buf, 0 );
                            exit( 0 );
                        }
                    }
                }
            }
            else
            {
                continue;
            }
        }
    }

    delete [] users;
    close( pipefd );
    close( child_epollfd );
    return 0;
}

int main( int argc, char* argv[] )
{
    if( argc <= 2 )
    {
        printf( "usage: %s ip_address port_number\n", basename( argv[0] ) );
        return 1;
    }
    const char* ip = argv[1];
    int port = atoi( argv[2] );

    int ret = 0;
    struct sockaddr_in address;
    bzero( &address, sizeof( address ) );
    address.sin_family = AF_INET;
    inet_pton( AF_INET, ip, &address.sin_addr );
    address.sin_port = htons( port );

    listenfd = socket( PF_INET, SOCK_STREAM, 0 );
    assert( listenfd >= 0 );

    ret = bind( listenfd, ( struct sockaddr* )&address, sizeof( address ) );
    assert( ret != -1 );

    ret = listen( listenfd, 5 );
    assert( ret != -1 );

    for( int i = 0; i < PROCESS_COUNT; ++i )
    {
        ret = socketpair( PF_UNIX, SOCK_STREAM, 0, sub_process[i].pipefd );
        assert( ret != -1 );
        sub_process[i].pid = fork();
        if( sub_process[i].pid < 0 )
        {
            continue;
        }
        else if( sub_process[i].pid > 0 )
        {
            close( sub_process[i].pipefd[1] );
            setnonblocking( sub_process[i].pipefd[0] );
            continue;
        }
        else
        {
            close( sub_process[i].pipefd[0] );
            setnonblocking( sub_process[i].pipefd[1] );
            run_child( i );
            exit( 0 );
        }
    }

    epoll_event events[ MAX_EVENT_NUMBER ];
    epollfd = epoll_create( 5 );
    assert( epollfd != -1 );
    addfd( epollfd, listenfd );

    ret = socketpair( PF_UNIX, SOCK_STREAM, 0, sig_pipefd );
    assert( ret != -1 );
    setnonblocking( sig_pipefd[1] );
    addfd( epollfd, sig_pipefd[0] );

    addsig( SIGCHLD, sig_handler );
    addsig( SIGTERM, sig_handler );
    addsig( SIGINT, sig_handler );
    addsig( SIGPIPE, SIG_IGN );
    bool stop_server = false;
    int sub_process_counter = 0;

    while( !stop_server )
    {
        int number = epoll_wait( epollfd, events, MAX_EVENT_NUMBER, -1 );
        if ( ( number < 0 ) && ( errno != EINTR ) )
        {
            printf( "epoll failure\n" );
            break;
        }

        for ( int i = 0; i < number; i++ )
        {
            int sockfd = events[i].data.fd;
            if( sockfd == listenfd )
            {
                int new_conn = 1;
                send( sub_process[sub_process_counter++].pipefd[0], ( char* )&new_conn, sizeof( new_conn ), 0 );
                printf( "send request to child %d\n", sub_process_counter-1 );
                sub_process_counter %= PROCESS_COUNT;
            }
            else if( ( sockfd == sig_pipefd[0] ) && ( events[i].events & EPOLLIN ) )
            {
                int sig;
                char signals[1024];
                ret = recv( sig_pipefd[0], signals, sizeof( signals ), 0 );
                if( ret == -1 )
                {
                    continue;
                }
                else if( ret == 0 )
                {
                    continue;
                }
                else
                {
                    for( int i = 0; i < ret; ++i )
                    {
                        switch( signals[i] )
                        {
                            case SIGCHLD:
                            {
	                        pid_t pid;
	                        int stat;
	                        while ( ( pid = waitpid( -1, &stat, WNOHANG ) ) > 0 )
                                {
                                    for( int i = 0; i < PROCESS_COUNT; ++i )
                                    {
                                        if( sub_process[i].pid == pid )
                                        {
                                            close( sub_process[i].pipefd[0] );
                                            sub_process[i].pid = -1;
                                        }
                                    }
                                }
                                stop_server = true;
                                for( int i = 0; i < PROCESS_COUNT; ++i )
                                {
                                    if( sub_process[i].pid != -1 )
                                    {
                                        stop_server = false;
                                    }
                                }
                                break;
                            }
                            case SIGTERM:
                            case SIGINT:
                            {
                                printf( "kill all the clild now\n" );
                                for( int i = 0; i < PROCESS_COUNT; ++i )
                                {
                                    int pid = sub_process[i].pid;
                                    kill( pid, SIGTERM );
                                }
                                break;
                            }
                            default:
                            {
                                break;
                            }
                        }
                    }
                }
            }
            else 
            {
                continue;
            }
        }
    }

    del_resource();
    return 0;
}
```