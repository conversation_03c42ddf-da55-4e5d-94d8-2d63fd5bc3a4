# 创建线程和结束线程
linux系统上他们都定义在pthread.h头文件中
1. 创建
```
#include<pthread.h>
int pthread_creat(pthread_t * thread,const pthread_attr_t * attr,void*(*start_routinr)(void*),void*arg);

#include<bits/pthreadtypes.h>
typedef unsigned long int pthread_t;
```
attr参数用于设置新线程的属性，NULL表示默认
start_routine参数指定新线程将运行的函数
arg是运行函数的参数
2. 退出
```
#include<pthread.h>
void pthread_exit(void* retval);
```
retval想线程的回收者传递其退出信息，执行完之后不会返回到调用者，而且永远不会失败
3. 回收
一个进程中的所有线程都可以回收其他线程（前提是可以回收）
类似于进程的wait函数
```
#include<pthread.h>
int pthread_join(pthread_t thread,void**  retval);
```
thread是目标线程的标识符
retval则是目标线程返回的退出信息
4. 终止
```
#include<pthread.h>
int pthread_cancel(pthread_t thread);
```
5.分离
```
#include <pthread.h>
int pthread_detach(pthread_t tid);
```
服务器上每个链接都是有一个单独的线程独立处理，所以很没有必要显式的等待每个对等线程终止。
6.初始化线程
```
#include <pthread.h>
pthread_once_t once_control = PTHREAD_ONCE_INIT;
int pthread_once(pthread_once_t *once_control,
void (*init_routine)(void));
```

once_control 变量是一个全局或者静态变量，总是被初始化为 PTHREAD_ ONCE_INIT 。当你第一次用参数 once_ control 调用
phread_once 时，它调用init_routine,这是一个没有输入参数、也不返回什么的函数。无论何时，当你需要动态初始化多个线程共享的全局变最时，pthread_once 函数是很有用的

接收到取消请求的目标线程可以决定是否允许被取消以及如何取消
```
#include<pthread.h>
int pthread_setcancelstate(int state,int *oldstate);
int pthread_setcanceltype(int type,int * oldtype);
```
第一个参数设置线程的取消状态和取消类型
第二个参数分别记录原来的取下状态和取消类型
state：
PTHREAD_CANCEL_ENABLE   允许线程被取消
PTHREAD_CANCEL_ENABLE   禁止线程被取消
type：
PTHREAD_CANCEL_ASYNCHRONOUS 线程随时都可以被取消
PTHREAD_CANCEL_DEFERRED     允许目标线程推迟行动
# 线程属性
```#include<bits/pthreadtypes.h>
#define __SIZEOF_PTHREAD_ATTR_T 36
typedef union
{
    char __size[__SIZEOF_PTHREAD_ATTR_T];
    long int __align;
}pthread_attr_t;
```
线程库定义了一系列函数来操作pthread_attr_t类型的变量

1. `pthread_attr_init()`: 初始化线程属性对象。
2. `pthread_attr_destroy()`: 销毁线程属性对象。
3. `pthread_attr_getdetachstate()` 和 `pthread_attr_setdetachstate()`: 获取和设置线程的分离状态，即线程的结束是否会影响到其他线程。
4. `pthread_attr_getstack()` 和 `pthread_attr_setstack()`: 获取和设置线程栈的大小和位置。
5. `pthread_attr_getstacksize()` 和 `pthread_attr_setstacksize()`: 获取和设置线程栈的大小。
6. `pthread_attr_getschedparam()` 和 `pthread_attr_setschedparam()`: 获取和设置线程调度参数，如优先级。
7. `pthread_attr_getschedpolicy()` 和 `pthread_attr_setschedpolicy()`: 获取和设置线程的调度策略。
8. `pthread_attr_getinheritsched()` 和 `pthread_attr_setinheritsched()`: 获取和设置线程调度属性的继承性。
9. `pthread_attr_getscope()` 和 `pthread_attr_setscope()`: 获取和设置线程范围，即线程的可见性。
10. `pthread_attr_getguardsize()` 和 `pthread_attr_setguardsize()`: 获取和设置线程栈的守护区大小。

1. **分离状态（Detached State）**：
   - 分离状态决定了线程的结束是否会影响到其他线程。如果线程处于分离状态，那么线程结束时它的资源会被自动释放，不需要其他线程调用 `pthread_join()` 来等待它的结束。相反，如果线程不是分离状态，那么需要调用 `pthread_join()` 来等待线程的结束并释放资源。

2. **线程栈（Stack）**：
   - 线程栈用于存储线程执行时所需的局部变量和函数调用信息。可以设置线程栈的大小和位置，以满足不同线程的需求。

3. **线程调度参数（Scheduling Parameters）**：
   - 包括线程的优先级和调度策略。优先级决定了线程在竞争 CPU 资源时的执行优先级，而调度策略则决定了线程在可运行状态时如何被调度执行。

4. **线程调度属性（Scheduling Attributes）**：
   - 包括线程调度参数的继承性和范围。继承性指定了子线程是否继承父线程的调度参数，而范围则指定了线程的可见性，即线程能否被其他线程看到和操作。

5. **线程栈的守护区大小（Guard Size）**：
   - 守护区是线程栈中用于检测栈溢出的一部分。设置守护区大小可以提高线程栈的安全性，避免栈溢出导致的问题。
# posix信号量
在linux上信号量api有两组，一组是13章的system v ipc信号量
另外一组是posix信号量
常用的信号量函数如下
```
#include<semaphore>
1. `sem_init(sem_t* sem, int pshared, unsigned int value)`:
   - 初始化一个信号量。`sem` 是指向要初始化的信号量的指针，`pshared` 指定信号量是在进程间共享还是线程间共享（通常使用0表示线程间共享），`value` 是信号量的初始值。

2. `sem_destroy(sem_t* sem)`:
   - 销毁一个信号量。释放与信号量相关的资源。

3. `sem_wait(sem_t* sem)`:
   - 等待信号量。如果信号量的值大于零，就将其减一并立即返回；否则，线程会阻塞直到信号量的值大于零。

4. `sem_trywait(sem_t* sem)`:
   - 尝试等待信号量。与 `sem_wait()` 类似，但是如果信号量的值为零，它会立即返回而不是阻塞线程。

5. `sem_post(sem_t* sem)`:
   - 发布（释放）信号量。将信号量的值加一，唤醒等待信号量的线程。
```

# 互斥锁
posix互斥锁相关函数如下：
```
#include<pthread.h>
1. `pthread_mutex_init(pthread_mutex_t* mutex, const pthread_mutexattr_t* mutexattr)`:
   - 初始化互斥锁。`mutex` 是指向要初始化的互斥锁的指针，`mutexattr` 是用于设置互斥锁属性的指针。如果 `mutexattr` 为 NULL，则使用默认属性初始化互斥锁。

2. `pthread_mutex_destroy(pthread_mutex_t* mutex)`:
   - 销毁互斥锁。释放与互斥锁相关的资源。

3. `pthread_mutex_lock(pthread_mutex_t* mutex)`:
   - 加锁互斥锁。如果互斥锁已经被其他线程锁定，则当前线程会阻塞直到该互斥锁可用。

4. `pthread_mutex_trylock(pthread_mutex_t* mutex)`:
   - 尝试加锁互斥锁。与 `pthread_mutex_lock()` 类似，但是如果互斥锁已经被其他线程锁定，则立即返回而不是阻塞线程。

5. `pthread_mutex_unlock(pthread_mutex_t* mutex)`:
   - 解锁互斥锁。释放互斥锁以允许其他线程获取锁。
```
#属性
```
1. `pthread_mutexattr_init(pthread_mutexattr_t* attr)`:
   - 初始化互斥锁属性对象。`attr` 是指向要初始化的互斥锁属性对象的指针。

2. `pthread_mutexattr_destroy(pthread_mutexattr_t* attr)`:
   - 销毁互斥锁属性对象。释放与互斥锁属性对象相关的资源。

3. `pthread_mutexattr_getpshared(const pthread_mutexattr_t* attr, int* pshared)`:
   - 获取互斥锁的 `pshared` 属性。`pshared` 会被设置为互斥锁的共享属性（进程间共享或线程间共享）。

4. `pthread_mutexattr_setpshared(pthread_mutexattr_t* attr, int pshared)`:
   - 设置互斥锁的 `pshared` 属性。通过 `pshared` 参数指定互斥锁是进程间共享还是线程间共享。

5. `pthread_mutexattr_gettype(const pthread_mutexattr_t* attr, int* type)`:
   - 获取互斥锁的 `type` 属性。`type` 会被设置为互斥锁的类型。

6. `pthread_mutexattr_settype(pthread_mutexattr_t* attr, int type)`:
   - 设置互斥锁的 `type` 属性。通过 `type` 参数指定互斥锁的类型。
```
```
pshared：
pshared 是互斥锁的共享属性，用于指定互斥锁是进程间共享还是线程间共享。
如果 pshared 被设置为 0（默认值），则表示互斥锁是线程间共享的，即互斥锁只能在创建它的进程内的不同线程之间共享。
如果 pshared 被设置为非零值（通常使用 PTHREAD_PROCESS_SHARED 宏），则表示互斥锁是进程间共享的，即它可以在不同进程之间共享。
进程间共享的互斥锁通常需要在共享内存区域中进行，以确保多个进程可以访问同一把锁。线程间共享的互斥锁则可以直接在多个线程之间共享，通常是在同一进程内的不同线程之间使用。
type：
type 是互斥锁的类型，用于指定互斥锁的行为方式。
POSIX 线程库定义了三种互斥锁的类型：
PTHREAD_MUTEX_NORMAL：普通互斥锁，不具备死锁检测和错误检测功能，可能会导致死锁。
PTHREAD_MUTEX_ERRORCHECK：带有错误检测的互斥锁，如果同一线程尝试对已加锁的互斥锁再次加锁，则会返回错误。
PTHREAD_MUTEX_RECURSIVE：递归互斥锁，允许同一线程对已加锁的互斥锁再次加锁，需要相应地释放相同次数的锁。
选择合适的互斥锁类型取决于应用程序的需求。例如，如果确定不会出现死锁情况，可以使用普通互斥锁以获得更好的性能。如果需要对互斥锁的使用进行严格控制以避免错误，可以选择带有错误检测功能的互斥锁。如果需要在递归函数中对同一互斥锁进行多次加锁和解锁操作，可以选择递归互斥锁。
```
# 条件变量
1. **pthread_cond_init**: 初始化条件变量
   - 函数原型：`int pthread_cond_init(pthread_cond_t *cond, const pthread_condattr_t *cond_attr);`
   - 功能：初始化一个条件变量。
   - 参数：
     - `cond`：指向要初始化的条件变量的指针。
     - `cond_attr`：指向条件变量属性的指针，通常为NULL，表示使用默认属性。

2. **pthread_cond_destroy**: 销毁条件变量
   - 函数原型：`int pthread_cond_destroy(pthread_cond_t *cond);`
   - 功能：销毁一个已经初始化的条件变量。
   - 参数：
     - `cond`：指向要销毁的条件变量的指针。

3. **pthread_cond_signal**: 唤醒等待在条件变量上的一个线程
   - 函数原型：`int pthread_cond_signal(pthread_cond_t *cond);`
   - 功能：向等待在条件变量上的线程发送信号，唤醒其中的一个线程。
   - 参数：
     - `cond`：指向条件变量的指针。

4. **pthread_cond_broadcast**: 唤醒等待在条件变量上的所有线程
   - 函数原型：`int pthread_cond_broadcast(pthread_cond_t *cond);`
   - 功能：向等待在条件变量上的所有线程发送信号，唤醒所有线程。
   - 参数：
     - `cond`：指向条件变量的指针。

5. **pthread_cond_wait**: 等待条件变量
   - 函数原型：`int pthread_cond_wait(pthread_cond_t *cond, pthread_mutex_t *mutex);`
   - 功能：等待条件变量的信号，如果收到信号则继续执行，否则一直阻塞。
   - 参数：
     - `cond`：指向条件变量的指针。
     - `mutex`：指向互斥锁的指针，函数在等待条件变量时会解锁该互斥锁，在收到信号后会重新上锁。
# 多线程环境
对于可重入函数，成为线程安全的
大多数不可重入函数，主要是因为使用了静态变量
不过linux提供了很多不可重入函数的可重入版本

### 在多线程程序中调用fork函数
因为子进城可能不清楚从父进城继承而来的互斥锁的具体状态
若互斥所是被加锁状态，但并不是由调用fork函数的那个线程锁住的，而是由其他线程锁住的，那么子进城若在此执行加锁操作，那么就会导致死锁
例子：
在使用fork函数的那个线程里先调用新的线程加锁，然后调用fork，又在子进程中加锁，这就会导致死锁
死锁的意思就是，这个锁永远不会被释放，等待的那个线程永远都用不了这个锁
不过pthread提供了专门的fork函数
```
#include<pthread.h>
int pthread_atfork(void(*prepare)(void),void(*parent)(void),void(*child)(void));
```
prepare fork创建出子进城之前被执行，用来锁住所有父进程中的互斥锁
parent  fork子进城创建之后，返回之前，在父进程中执行，它的作用是释放所有在prepare中被锁住的护持锁
child   fork返回之前，在子进城中被执行，child也可以用于释放被锁住的互斥锁
### 使用pthread_adfork函数
```
void prepare()
{
    pthread_mutex_lock(&mutex);
}
void infork()
{
    pthread_mutex_unlock(&mutex);
}
pthread_atfork(prepare,infork,infork);
```
### 线程和信号
每个线程都可以独立设置信号掩码
但是多线程环境在应该使用特殊版本的sigprocmask
```
#include <pthread.h>
#include<signal.h>
int pthread_sigmask(int how,const sigset_t * newmask,sigset_t *oldmask);
```
但是每个线程单独设置信号掩码，这会使逻辑十分混乱，所以应该使用一个专门的线程来处理所有的信号，通过以下两个步骤来实现：
1. 在创建子进城之前调用上述掩码函数设置好信号掩码
2. 在某个线程中调用如下函数了来等待信号并处理之
```
#include<signal.h>
int sigwait(const sigset_t* set,int *sig);
```
set指定需要等待的信号的集合
sig指向的整数用于存储函数返回的信号值
使用了sigwait就不应该设置信号处理函数了，因为程序接收到信号时，只会有一个起到作用
当然我们也可以明确的向一个线程发送一个信号
```
#include<signal.h>
int pthread_kill(pthread_t thread,int sig);
```
# 线程同步机制的包装类
```
#ifndef LOCK_H
#define LOCK_H

#include <pthread.h>
#include <semaphore.h>

class sem
{
public:
    sem()
    {
        if(sem_init(&m_sem, 0, 0) != 0)
        {
            throw std::exception();
        }
    }

    ~sem()
    {
        sem_destroy(&m_sem);
    }

    bool wait()
    {
        return sem_wait(&m_sem) == 0;
    }

    bool post()
    {
        return sem_post(&m_sem) == 0;
    }

private:
    sem_t m_sem;
};

class locker
{
public:
    locker()
    {
        if(pthread_mutex_init(&m_mutex, NULL) != 0)
        {
            throw std::exception();
        }
    }

    ~locker()
    {
        pthread_mutex_destroy(&m_mutex);
    }

    bool lock()
    {
        return pthread_mutex_lock(&m_mutex) == 0;
    }

    bool unlock()
    {
        return pthread_mutex_unlock(&m_mutex) == 0;
    }

private:
    pthread_mutex_t m_mutex;
};

class cond
{
public:
    cond()
    {
        if(pthread_mutex_init(&m_mutex, NULL) != 0)
        {
            throw std::exception();
        }

        if(pthread_cond_init(&m_cond, NULL) != 0)
        {
            pthread_mutex_destroy(&m_mutex);
            throw std::exception();
        }
    }

    ~cond()
    {
        pthread_mutex_destroy(&m_mutex);
        pthread_cond_destroy(&m_cond);
    }

    bool wait()
    {
        int ret = 0;
        pthread_mutex_lock(&m_mutex);
        ret = pthread_cond_wait(&m_cond, &m_mutex);
        pthread_mutex_unlock(&m_mutex);
        return ret == 0;
    }

    bool signal()
    {
        return pthread_cond_signal(&m_cond) == 0;
    }

private:
    pthread_mutex_t m_mutex;
    pthread_cond_t m_cond;
};

#endif
```
