# tcp/ip协议族
tcp/ip协议族是一个四层协议系统，自上而下分别是数据链路层，网络层，传输层和应用层
上层使用下层协议提供的服务
### 数据链路层
实现了网卡接口的网络驱动程序
两个常用协议是ARP（地址解析协议）和RARP（逆地址解析协议）
将ip地址转换成物理地址
### 网络层 
通信的两台设备一般不是直连的，通过多个中间节点－路由器，连接
##### ip协议
ip协议根据数据包的目的ip地址来决定如何投递
##### icmp协议
ip协议的重要补充，主要用于检测网络连接
8位类型，8位代码，16位校验和
严格来说，icmp并非网络层协议，因为他是用ip协议
### 传输层
主要协议有三个：tcp协议，udp协议，sctp协议
##### tcp协议
提供可靠的，面向连接的和基于流的服务
使用了超时重传，数据确认等方式
因为基于流，所以他没有边界（长度）的限制
##### udp协议
提供不可靠，无连接，基于数据报的服务
不可靠意味着必须要程序自己处理数据确认、超时重传等逻辑
无连接意味着每次发送数据都要指定接收端的地址
基于数据报意味着必须要将其内容一次性读出，否则将会被截断
### 应用层
ping是应用程序，而不是协议，通过icmp报文检测网络连接
talent协议是一种远程登录协议，他使我们能在本地完成远程任务
ospf协议是一种动态路由更新协议
dns协议提供机器域名到ip地址的转换

# 封装
从应用层->传输层->网络层->数据链路层一层一层的封装，将自己放入下一层的缓冲区中
当发送端程序使用send或者write向一个tcp连接写入数据时，tcp模块会首先把这些数据复制到对应的内核发送缓冲区中，然后调用ip模块提供的服务，传递的参数包括tcp头部和发送缓冲区的数据
udp不同的是她不会保存数据报的副本
<br>
ip封装后的数据成为ip数据报
经过数据链路层封装的数据成为帧，根据传输媒介的不同，分为以太网帧和令牌环帧
以太网帧包括6字节的目的物理地址，6字节的源物理地址，2字节的类型，mtu字节的数据和4字节的crc（提供循环冗余校验）

# 分用
以以太网帧为例
数据链路层通过2字节的类型字段将帧交付给ip模块或者是arp、rarp
ip层通过16位协议字段交给icmp、tcp、udp字段
tcp、udp通过16位（65536）的端口号来区分上层应用程序

# arp协议工作原理
arp协议能实验任意网络地址到任意物理地址的转换
ip地址转换成mac地址的原理：主机向自己所在网络广播一个arp请求，该请求包含目标机器的网络地址，只有被请求的目标机器会回应一个arp应答，其中包含自己的物理地址
### 报文详解
1. 2字节的硬件类型，1表示mac地址
2. 2字节的协议类型，0x800表示ip地址
3. 1字节的硬件地址长度和1字节的协议地址长度，对mac来说长度是6，对ip来说长度是4
4. 2字节的操作字段，arp请求－1，arp应答－2，rarp请求－3，rarp应答－4
5. 最后四个字段分别为发送端以太网地址，发送端ip地址，目的端以太网地址，目的端ip地址，因为是mac和ip，所以长度是6464
发送端会填充目的端以太网地址以外的所有信息，接收端发现目标ip是自己，就会填充完成后交换目的端地址和发送端地址作为应答返回
有些时候传输字段有最短长度要求，arp报文会自动填充，这个时候一个以太网帧为64字节，平常为arp报文的28加上以太网帧头部和尾部的18字节也就是46字节
linux下使用arp -a 查看保存的高速缓存
使用tcpdump观察arp通信过程
# dns工作原理
dns查询和应答报文
1. 16位标识字段，标记一对dns查询和应答
2. 16位标志字段，协商具体的通信方式和反馈通信状态，QR，查询/应答状态，0表示查询1表示应答，opcode，定义查询和应答的类型，0表示标准查询，1表示反向查询，2表示请求服务器状态，AA，授权应答标志，1表示域名服务器是授权服务器，TC，截断标志，仅udp使用，1表示报文被截断，RD，递归查询标志，1表示执行递归查询，向其他dns查询，0表示迭代查询，将其他dns服务器的ip地址返回给客户端，RA，允许递归查询，仅由应答报文使用，zero，未用字段，rcode，返回码，0，无错误，3，域名不存在
3. 接下来的4个子段分别指出dns报文的最后4个字段的资源记录数目，查询报文一半包含1个查询问题，应答资源记录数、授权资源记录数和额外资源记录数则为0，应答报文的应答资源记录数则至少为1
4. 查询问题，查询名以一定的格式封装了要查询的主机域名，16位查询类型，A表示1，获取目标主机的ip地址，CNAME，5，表示获得目标主机的别名，PTR，12，表示反向查询
5. 应答字段，授权字段和额外信息字段都是用资源记录格式，包含32位域名，16位类型，16位类，32位生存时间，16位资源数据长度