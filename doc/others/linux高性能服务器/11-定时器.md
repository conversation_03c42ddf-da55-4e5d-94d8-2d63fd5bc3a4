# socket选项SO_RCVTIMEO和SO_SNDTIMEO
linux提供了三种定时方法
他们是：
1. socket选项，即本章
2. SIGARLRM信号
3. IO复用系统调用的超时参数

| 系统调用     |有效选项| 超时后的行为                               |
|--------------|--------|--------------------------------------------|
| send         |SO_SNDTIMEO|返回-1，设置errno为EAGAIN或EWOULDBLOCK     |
| sendmsg      |SO_SNDTIMEO|返回-1，设置errno为EAGAIN或EWOULDBLOCK     |
| recv         |SO_RCVTIMEO|返回-1，设置errno为EAGAIN或EWOULDBLOCK     |
| recvmsg      |SO_RCVTIMEO|返回-1，设置errno为EAGAIN或EWOULDBLOCK     |
| accept       |SO_RCVTIMEO|返回-1，设置errno为EAGAIN或EWOULDBLOCK     |
| connect      |SO_SNDTIMEO|返回-1，设置errno为EINPROGRESS             |

一般使用connect系统调用，因为errno只有一种可能
# 定时器升序链表
大致原理：
使用绝对时间作为定时依据
第一个如果触发定时信号那么就将测试用指针指向链表中第一个超时时间大于目前时间的位置前方
将触发的函数一次触发
# io复用系统调用的超时参数
```
#define TIMEOUT 5000
int timeout = TIMEOUT;
time_t start = time(NULL);
time_t end = time(NULL);

while (1) {
    printf("the timeout is now %d milliseconds\n", timeout);
    start = time(NULL);
    int number = epoll_wait(epollfd, events, MAX_EVENT_NUMBER, timeout);
    
    if (number < 0) {
        if (errno != EINTR) {
            printf("epoll failure\n");
            break;
        }
    }
    
    /* 如果 epoll_wait 成功返回 0，则说明超时时间到，此时便可处理定时任务，并重置定时时间 */
    if (number == 0) {
        timeout = TIMEOUT;
        continue;
    }
    
    end = time(NULL);
    timeout -= (end - start) * 1000;
    
    /* 重新计算之后的 timeout 仅有可能等于 0，说明本次 epoll_wait 调用返回时，不仅有文件描述符就绪，且其超时时间也刚好到达，此时我们也要处理定时任务，并重置定时时间 */
    if (timeout <= 0) {
        timeout = TIMEOUT;
        // handle connections
    }
}
```
因为io复用系统调用可能在超时时间到期之前就返回，所以如果我们要利用它们来定时，就需要不断更新定时参数以反映生于欧的时间
# 高性能定时器
### 时间轮
上一个链表定时器的插入和删除时间复杂度都较高
可以使用哈希表思想。
指 针 指 向 轮 子 上 的 一个 槽 ( s l o t ) 。 它 以 恒 定 的 速 度 顺时针转动，每转动一步就指向下一个槽(虚线指针指向的槽)，每次转动称为 一个滴答 (tick)。 一个滴答的时间称为时间轮的槽间隔si(slotinterval)，它实际上就是心搏时间。该 时 间 轮 共 有 N 个 槽 ， 因 此 它 运 转 一 周 的 时 间 是 N * s i 。 每 个 槽 指 向 一条 定 时 器 链 表 ， 每 条 链 表上的定时器具有相同的特征:它们的定时时间相差N*si 的整数倍。时间轮正是利用这个关系 将定时器散列到不同的链表中。假如现在指针指向槽cs，我们要添加 一个定时时间为位的定 时器，则该定时器将被插入槽ts (timer slot)对应的链表中:
ts =(cs+ (ti/si))%N
很显然，对时间轮而言，要提高定时精度，就要使si 值足够小:要提高执行效率，则要 求N 值足够大
### 时间堆
使用最小堆思想，时间轮的tick每次都是固定的
那么也可以让每次tick都等于超时时间的最小值
最小堆是一种完全二叉树
所以可以直接用数组来表示这个最小堆


