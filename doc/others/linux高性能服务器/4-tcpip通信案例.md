# http通信
### 常见的http请求方法
1. GET 申请获取资源，只读
2. HEAD 仅要求头部文件
3. POST 客户端向服务器提交数据
4. PUT 上传某个资源
5. DELETE 删除某个资源
6. TRACE 要求目标服务器返回原始http请求的内容
7. OPTIONS 查看服务器对某个特定URL都支持哪些请求方法
8. CONNECT，可把连接转化成为一个安全隧道
9. PATCH 对某个资源做部分修改

### http状态码
##### 1xx信息
100 continue继续
##### 2xx成功
200 ok请求成功
##### 3xx重定向
301 重定向
302 能在其他地方找到，需要使用GET
304 被申请的资源没有更新，和之前获得的相同
307 能在其他地方找到，客户端可以使用和原始请求相同的请求方法来访问
##### 4xx客户端错误
400 通用客户请求错误
401 请求需要认证信息
403 访问被服务器禁止
404 资源没找到
407 客户端需要先获得代理服务器的认证
##### 5xx服务器错误
500 通用服务器错误
503 暂时无法访问服务器

http是无状态的，但是交互程序常常需要承上启下，所以可以使用cookie，这样服务器就可以区分不同的客户。
