# tcp服务的特点
面相连接的，无边界传输
对于变成来说就是没有必要使用相同次数的读写操作
tcp有数据发送接收缓冲区，udp是直接发送的
# tcp头部结构
### tcp固定头部结构
1. 16位源端口号和目的端口号
2. 32位序号，第一个报文段为随机isn值
3. 32位确认号，用作回应，其值为收到的序号值加一，a对b进行通讯，包含自己的序号，也包含b发送来的tcp确认号
4. 4位头部长度，和ip一样，记录有多少个4字节，最长都是60字节
5. 6位标志位urg，紧急指针是否有效，ack，确认号是否有效，psh，接收端立即取走数据，rst，要求重新连接，syn请求建立连接，fin，通知对方关闭链接
6. 16位窗口大小，通知对方接受缓冲区还有多少
7. 16位校验和，发送端填充接收端执行crc检验，和ip不同，不仅校验头部，数据部分也会校验
8. 16位紧急指针，一个报文段内，序号加上一个正的偏移量，得到紧急数据

### tcp头部选项
选项部分最多40字节
一般结构：kind，length，info
kind＝0，选项表结束选项
kind＝1，空操作，将总长度填充为4字节的整数倍
kind＝2，最大报文段长度选项，通常mss设置为mtu－40，因为通常而言，ip头部和tcp头部都不会携带选项
kind＝3，窗口扩大因子选项，tcp一般接受窗口为16位，65535字节，m表示左移几位，取值范围是0－14，可以通过修改/proc/sys/net/ipv4/tcp_window_scaling
kind2,3都是只能出现在同步报文段中，即第一个窗口永远是默认的
kind＝4，选择性确认选项，以前一个字段丢失，就会全部重传，这个选项是用来确认是否支持sack技术，可以通过修改/proc/sys/net/ipv4/tcp_sack
kind=5,sack实际工作的选项，告诉发送端本端已经收到并缓存的不连续的数据块
kind＝8，时间戳选项提供了双方的回路时间的方法，/proc/sys/net/ipv4/tcp_timestamps
# tcp连接的建立和关闭
### 连接的建立和关闭时序
建立：
第一个是同步报文段，包含seq
第二个也是同步报文段，包含seq和ack，同步报文段比较特殊，即使没有携带任何应用程序数据，他也会占用一个序号值
第三个报文是确认第二个同步报文段，包含ack
关闭：
第一个为结束报文段，同样占用一个序号值，包含seq和ack
第二个为确认包含ack
第三个是接收端发送的结束报文段，包含seq和ack
第四个是确认第三个的确认报文段，包含ack
第二个确认报文段其实可以省略
<br>
tcp是全双工的，允许一端独立关闭后另一端继续发送信息
tcp重连时间为1，2，4，8，16，32（五次）
可以通过修改`/proc/sys/net/ipv4/tcp_syn_retries`来定义
# tcp状态转移

服务器调用listen进入listen状态，被动等待客户端连接，被动打开，此时一旦收到链接请求，该链接处于SYN_RCVD，如果成功收到发送回的确认报文段，则转移到established
客户端主动关闭连接时（close，shutdown），服务器返回确认报文段，使连接进入close_wait状态
通常，服务器检测到客户端关闭后会立即发送一个结束报文段，将连接转移到last_ack状态

### 典型状态转移
##### connect
客户端通过connect主动与服务器建立连接，首先给服务器发送一个同步报文段，使连接转移到syn_sent状态
若端口不存在，活仍处于time_wait状态，则服务器发送复位报文段，connect失败
若端口存在，但在超时时间内未收到服务器的确认报文段，则connect调用失败
调用失败后立即返回到初始的closed状态，若成功，则进入established

客户端主动关闭时，他将发送一个结束报文段，同时连接进入`fin_wait_1`状态，若此时收到确认目的的确认报文段（四次挥手的第二个报文段），则连接转移至`fin_wait_2发生半关闭的状态，此时如果`，此时服务器处`close_wait`状态，这一对状态是可能发生半关闭的状态，如果服务器也关闭连接，则客户端将给予确认并进入`time_wait`状态
如果服务器端的四次挥手，2、3一起，那么就不会有`fin_wait_1`而是直接进入2
如果客户端还没有接收到服务器端的连接还在2状态就强行退出，那么就变成了孤儿连接，为了防止长时间残留，定义了孤儿连接数目和生存时间

### time_wait状态
客户端接收到第三次挥手后没有立即进入close状态，需要等待2个报文段最大生存时间才能关闭
这保证了可靠的终止tcp连接
保证让迟来的tcp报文段有足够的时间被识别并丢弃
第一个保证解决了第四次挥手有可能丢失的问题
第二个保证解决了linux系统，迅速使用上一次的端口，造成接收到原来程序的信息
两个msl的time_wait可以保证所有其他报文段全部丢弃，可以安全的建立连接
可以通过socket选项SO_REUSEADDR强制立即使用time_wait状态的端口
# 复位报文段
即发送携带ret标志的报文段
什么时候会发送复位报文段
1. 访问不存在的端口
2. 异常终止链接，可食用SO_LINGER发送复位报文段
3. 处理半打开连接
# tcp数据流和带外数据
tcp报文段携带的应用程序数据按照长度分为两种：交互数据和成块数据
交互数据很短，对实时性要求高，比如telnet、ssh
成块数据对传输效率高，比如ftp

### 带外数据
udp没有实现，tcp可以使用头部的紧急数据来代替
紧急指针指向紧急信息的下一个位置

### 超时重传
一共执行5次重传，每次重传时间都增加一倍，五次重传均失败，底层的ip或者arp模块开始接管
# 拥塞控制
拥塞控制：慢启动、拥塞避免、快速重传和快速恢复
最终受控变量是发送端向网络一次连续写入的数据量，即发送窗口swnd，这限制了连续发送的tcp报文段数量，tcp报文段最大长度成为smss
接收方可通过接收窗口控制发送窗口，但这是不够的，所以使用一个取小程序，比较拥塞窗口和接收端口哪个更小
### 慢启动和拥塞避免
`CWND+=min(N,SMSS)`
N是此次确认中包含的之前未确认的字节数
慢启动是指数型的，所以并不慢
当N大于SMSS时tcp拥塞控制将进入拥塞避免状态
使用的情况：传输超时
### 快速重传和快速恢复
使用的情况：接收到重复的确认报文段
比如连续收到多个报文段的确认，则立即（不等到超时重传时间），而是直接那个确认的报文段的后面一个报文段

