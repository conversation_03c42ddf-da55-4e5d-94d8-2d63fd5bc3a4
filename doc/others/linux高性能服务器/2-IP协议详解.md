# ip服务的特点
ip协议是tcp/ip协议族的动力，他为上层协议提供无状态，无连接，不可靠的服务
无状态，是指是指ip通信双方不同步传输数据的状态信息，除了ip，无状态常见的还有ucp和http
无连接，是指ip通信双方都不长久地维持对方的任何信息
不可靠，表示ip协议不保证ip数据报准确地到达接收端
# ipv4头部结构
头部长度一般为20字节
1. 4位版本号指定ip协议的版本，ipv4为4
2. 4位头部长度，标识有多少个32bit字（4字节），因为4位最大为15，所以ip头部长度最长是60字节
3. 8位服务类型包含：3位优先权字段（已被忽略），4位tos字段和一位保留字段（必须置0），4位tos字段分别表示：最小延时，最大吞吐量，最高可靠性和最小费用，最多有一个能设置为1
4. 16位总长度是指整个ip数据报的长度，因此最大长度为65536字节，因为mtu的限制，大部分都达不到最大值，一般使用分片
5. 16位标识，唯一的标识主机发送的每一个数据报，一个数据报的所有分片都具有相同的表示值
6. 3位标志字段的第一位保留，第二位表示禁止分片，第三位表示更多分片，这里面有mf标志位，表示是否是分片的一部分
7. 13位分片偏移，因为要使用13位表示16位的数据，所以除了最后一个ip分片外，所有ip分片的数据部分的长度都必须是8的整数倍
8. 8位生存时间，数据报到达目的地之前允许经过的路由器跳数，可以放置数据报陷入路由循环
9. 8位协议，用来区分上层协议，icmp是1，tcp是6，udp是17
10. 16位头部校验和，由发送端填充
11. 32位的源端ip地址和目的端ip地址用来标识数据报的发送端和接收端
12. 选项字段最多包含40字节，因为前面的部分已经有20填入 字节：记录路由（中间路由会保存ip到数据报中），时间戳，让每个路由器都将转发的时间都保存，松散源路由选择，指定要经过的路由器ip列表，严格源路由选择，只经过指定的路由器
# ip分片
以太网帧的mtu是1500
第一个分片的偏移量为0，第二个分片的偏移量为第一个分片的大小
# ip路由
### ip模块工作流程
ip模块接收到来自数据链路层的ip数据报时。他首先对该数据包的头部做crc校验，确认无误之后分析具体头部信息。
若数据报头部使用了源站选路选项，那么就调用数据报转发子模块来处理，如果是发给自己的，则根据数据报头部中的协议字段来决定将它派发给哪个上层应用。
数据报转发子模块会检测数据报是否允许转发，如果不允许那么就将数据报丢弃
ip路由过程由计算下一跳路由子模块，实现的核心数据结构是路由表
### 路由机制
路由表每项包含8个子段：
1. destination，目标网络或主机
2. gateway，网关地址，*表示在同一个网络，不需要路由
3. genmask，网络掩码
4. flags，常见的有：U，活动的，H，是一台主机，G是网关，D，重定向生成的，M，被重定向修改过
5. metric，路由距离，即到达指定网络所需的中转数
6. ref，路由项被引用的次数
7. use，该路由被使用的次数
8. iface，该路由项对应的输出网卡接口
### 路由表更新
```
sudo route add ...
sudo route del...
```
这种手动修改路由表是静态的路由更新方式，对于大型的路由器，他们是通过bgp（边际网关协议），rip（路由信息协议），ospf等协议来发现路径，这种方式是动态的，自动的。
### ip转发
默认机器上/proc/sys/net/ipv4/ip_forward内核参数默认被设置为0，可以修改他来使能主机的数据报转发功能
### 重定向
icmp报文头部的3个固定字段：8位类型，8位代码和16位校验和
icmp重定向报文的类型值为5，代码字段有4个可选值，主机重定向代码值为1
icmp重定向报文包含源ip和应使用的路由ip两个消息
/proc/sys/net/ipv4/conf/akk/send_redirects内核参数指定是否允许发送icmp重定向报文
/proc/sys/net/ipv4/conf/akk/accept_redirects内核参数指定是否允许接受icmp重定向报文
一般来说主机只能接收icmp重定向报文，路由器只能发送icmp重定向报文。
icmp重定向的作用是告诉主机有更好的网关选择。
# ipv6头部详解
### ipv6固定头部结构
1. 4位版本号，指定ip协议的版本
2. 8位通信类型指示数据流通信类型或优先级，和ipv4中的tos类似
3. 20位流标签是ipv6新增加的字段，用于某些对连接的服务质量有特殊要求的通信
4. 16位净荷长度指的是ipv6拓展头部和应用程序数据长度之和，不包括固定头部长度
5. 8位下一个包头指出紧跟ipv6固定头部后的包头类型，如扩展头或者某个上层协议头，类似于ipv4头部中的协议字段
6. 8位跳数限制，和ttl含义相同
7. ipv6用128位（16字节）表示ip地址，ipv6地址使用：分割成8组，每组包含2字节，通常可以使用所谓的零压缩法将其简写，不过零压缩法只能使用一次，否则无法计算每个::之间省略了多少个全零组。
### ipv6扩展头部

Hop-by-Hop 逐跳选项头部，它包含每个路由器都必须检查和处理的特殊参数选项
Destination options 目的选项头部，指定由最终目的节点处理的选项
Routing 路由头部，指定数据报要经过哪些中转路由器，功能类似于 IPv4 的松散源路由选择选项和记录路由选项
Fragment 分片头部，处理分片和重组的细节
Authentication 认证头部，提供数据源认证、数据完整性检查和反重播保护
Encapsulating Security Payload 加密头部，提供加密服务
No next header 没有后续扩展头部
