## 1、什么是汇编语言，它在计算机语言中的地位？

```text
汇编语言是程序设计语言的基础语言，是唯一可以直接与计算机硬件打交道的语言
```

## 2、汇编语言与源程序、汇编程序、汇编的关系？

![img](https://pic1.zhimg.com/80/v2-b9c8c1f5adce7fab60ba6010280982cc_720w.webp)

## 3、汇编语言的特点

- \1) 汇编语言与机器指令一一对应，可充分理解计算机的操作过程汇编语言指令是机器指令的符号表示
- \2) 汇编语言是靠近机器的语言编程时要求熟悉机器硬件系统，可充分利用机器硬件中的全部功能，发挥机器的特点在计算机系统中，某些功能由汇编语言程序实现：实时过程控制系统、系统初始化、实际的输入输出设备操作
- \3) 汇编语言程序的效率高于高级语言效率，指的是用汇编语言编写的源程序在汇编后所得的目标程序效率高时间域的高效率：运行速度快；空间域的高效率：目标代码占用存储空间少

## 4、汇编语言与高级语言的比较

![img](https://pic2.zhimg.com/80/v2-e147e0cdc60a6b243dae382353af0ad9_720w.webp)

## 5、进制转换

```text
（略）
```

## 6、数据组织单位

> \1) 位（bit）
> 是计算机中表示信息的最小单位，符号b，是一个二进制位，每一位用0或1表示
> \2) 字节（Byte）
> 8位二进制数为一个字节
> \3) 字（Word）
> 若干个字节为一个字，一般一个字包含两个字节
> 范围0000H~FFFFH
> \4) 双字（Double Word）
> 两个字节为一个字，四个字节为连个字，称为双字
> 范围00000000H~FFFFFFFFH
> \5) 字长
> 机器字的长度为字长，即计算机中每个字所包含的位数，由机器数据总线数决定
> 例如，数据总线数为64位，机器字长为64位，即每个字有8个字节
> \6) 数据字与指令字
> 数据字：在存储单元中存储的是数据
> 指令字：在存储单元中存储的是指令
> 无论是数据字还是指令字，在存储单元中都是以二进制的形式存放的

**7、BCD码**

两种存储方式：组合型（1个字节表示2个BCD码）；非组合型（1个字节表示1个BCD码）

**8、80X86计算机组织结构**

微型计算机的硬件系统主要由3个主要部分组成：

- 1)中央处理器CPU（运算器、控制器、寄存器）
- 2)输入输出设备
- 3)存储器

**9、80X86 CPU的寄存器**

寄存器分为3类：

- 1)通用寄存器
- 2)控制寄存器
- 3)段寄存器

8个8位通用寄存器：AL,AH,BL,BH,CL,CH,DL,DH

8个16位通用寄存器：AX,BX,CX,DX,SI,DI,BP,SP

8个32位通用寄存器：EAX,EBX,ECX,EDX,ESI,EDI,EBP,ESP

![img](https://pic1.zhimg.com/80/v2-0631762f84d53592ee29dcc490ff3994_720w.webp)



> 说明：1）指针寄存器（SP,ESP,BP,EBP）
> SP,ESP为堆栈指针寄存器，存放当前堆栈段栈顶的偏移地址，
> 是根据指令自动移动的，要想随机读取
>
> 堆栈段中的数据，必须通过BP或EBP基址指针寄存器来读取。
> 2）控制寄存器（IP,EIP,FLAGS,EFLAGS）
> IP,EIP为指令指针寄存器，用于存放当前正在执行的指令的
> 下一条指令的偏移地址，该寄存器所指的为代码段的偏移地址。
> FLAGS为标识寄存器，表示程序运行时的状态和一些特殊控制

3）段寄存器

代码和数据是分开存放，代码存放在代码段，数据存放在数据段

**10、内存组织结构**

1）内存的地址
在存储器中内存单元的基本单位是*字节*，每个字节都有一个唯一的地址

![img](https://pic3.zhimg.com/80/v2-5cf385fec47048719360e4f84cb0a67a_720w.webp)

**2）存储单元的内容**

一个存储单元存放的信息为存储单元的内容

1. 分为：字节单元、字节单元、双字单元
2. 双字：需要两个16位寄存器，通常为DX:AX,DX高位，AX低位

![img](https://pic3.zhimg.com/80/v2-422fd8d4b3b9a5a5b914c8c118a428f6_720w.webp)

```cpp
3)堆栈
堆栈是内存中一块特定的区域，其中数据按照*先进后出*原则
作用：暂存数据、子程序调用与返回、调用中断处理程序、从中断处理程序返回
位置：堆栈段地址存放于SS寄存器中，偏移地址存放在堆栈指针寄存器（SP(16位)/ESP(32位)），
他们永远指向栈顶
	初始化：堆栈的初始化时通过设置SS及SP/ESP值来完成的，可以由编译系统自动完成，也可以在程序
中通过伪指令显示地定义
```

**11、实模式**

```cpp
1)介绍
	
	只有8086/8088工作在实模式下；
	80286以上的微处理器工作在实模式和保护模式下；
	在实模式下微处理器只能寻址1MB的存储空间；
	80286以上系统的微处理器在加点或复位时都以实模式方式开始工作

2）内存地址的分段

	*为什么要分段？*
	8086/8088地址总线为20根，可访问的地址为：2^20=1048576=1M
	8086/8088内部寄存器都是16位的，可以直接处理16位长度的存储地址，16位地址的寻址2^16=64K
	为了把寻址范围扩大到1MB，实模式存储器地址均采用存储空间的分段技术来解决寻址1MB的存储空间
	提出了段地址和偏移地址合成20位物理地址的概念
	
	*分段方法？*
	16位段地址+16位段内地址--->20位物理地址
	地址的组合：物理地址=段地址*16D(或10H)+偏移地址，（段地址*16D--二进制段地址左移4位）
	存放段地址：16位段地址寄存器（CS、DS、SS、ES）
	存放偏移地址：16位指针寄存器（IP、SP）
	在1MB存储器中可以有64K个段，每个段最多64KB，最小为16KB
	

	*物理地址、段地址、段内地址、逻辑地址的区别？*
	物理地址：与内存单元一一对应的20位二进制数,1MB=00000H~FFFFFH
		    每个物理地址代表一个唯一的内存单元
	
	段地址：将1MB的内存空间分为长64KB的程序区和数据区称为段
		  每个段用1个16位二进制地址表示
		  段地址存放在段寄存器中
		  代码段：用于存放源程序的二进制程序代码，该段的段地址放在CS中
		  数据段：存放操作数据的，该段的段地址放在DS中
		  堆栈段：堆栈用的存储区，该段的段地址放在SS中
		  附加段：该段的段地址放在ES中
	
	段内地址：16位二进制段内地址为偏移地址
   （偏移地址）不同段内的偏移地址存放在不同的寄存器中，段寄存器与装偏移地址的寄存器按一定要求组合
```

![img](https://pic1.zhimg.com/80/v2-7ea71c556f95e10dde7137a275c01748_720w.webp)

```cpp
逻辑地址：用段地址和偏移地址来表示内存单元的地址为逻辑地址，例如，段地址：偏移地址
	*逻辑地址与物理地址的换算关系？*
	物理地址 = 段地址*16D（10H）+偏移地址
	逻辑地址 = 段地址：偏移地址
	例子：逻辑地址，1111H:2222H
物理地址，1111H*10H+2222H = 13332H
假设1111H为代码段地址，2222H在指针寄存器IP中，示意图如下：
```

![img](https://pic3.zhimg.com/80/v2-65c8fa036b5b5f4ca807f594e426308a_720w.webp)



**内存分配方法？**

代码段、数据段、堆栈段的大小，是以节为最小单位分配内存区域的16字节=2个字=1节，节的边界地址就是能够被16整除的地址偏移地址（段内地址）是从0000H开始的例子：假设程序分配的内存区从6100H开始，程序长度1020字节，操作数510字节，堆栈段250字节则代码段长度为1024D=400H，数据段长度为512D=200H，堆栈段长度为256D=100H

**示意图如下：**

![img](https://pic4.zhimg.com/80/v2-f3c4bf9aa6606ce79db198b30797dc4f_720w.webp)

**段与段之间的关系？**

8088/8086 CPU把1MB的存储空间划分成若干逻辑段每个段的起始地址必须是能够被16整除的数逻辑段的最大长度为64KB 1MB的存储空间最多可以分成64K个逻辑段，当每个逻辑段为16KB时段与段之间可以相邻、分离、重叠、部分重叠

**12、保护模式**

```text
1）保护模式存储器寻址机制
	在保护模式下，逻辑地址=选择符+偏移地址
	与实模式不同，实模式的段寄存器存放段基地址，而保护模式的段寄存器存放选择符
	保护模式下，通过选择描述符表中的描述符，间接地形成段基地址
	保护模式的偏移地址最大可以是32位，最大段长可以从16KB扩展到4GB
2)描述符
	描述符包括，段在寄存器中的位置，段的长度，访问权限
	由基地址、段界限、访问权限、附加字段组成
		基地址：指定段的起始地址
		段界限：存放该段的最大偏移地址
		访问权限：说明该段在系统中的功能和一些控制信息
		附加字段：描述该段的一些属性
	描述符的内容是由系统自动设置的
	由于段寄存器是16位的，描述符是64位的
	故将64位的段描述符放按顺序存放形成一个段描述符表，放在内存中
	而在段寄存器中实际存放的是要选择的段描述符表的序号，类似于数组中的下标
```

**13、存储器管理机制**

```cpp
1）分段管理机制
		①虚拟存储器：在有限的物理存储器上获取更大的使用空间
			*虚拟存储器是如何实现存储的？*
			在程序执行期间的任意时刻，虚拟存储器系统自动吧程序分成许多小块即程序段
			将某个程序段存放到物理存储器中，其他程序段放在磁盘中
			当程序要访问到哪个程序段时，就把哪个程序段引导到物理存储器中
		
		②分段管理：将4GB的存储空间分成若干独立的受保护的存储空间块
			每个应用程序可以使用这些存储空间块
	
	2）分页管理机制
①线性地址空间：每个进程都有相同大小的4GB线性空间
用分段管理机制实现虚拟地址空间到线性地址空间的映射，实现把二维的
虚拟地址转换为一维的线性地址

②分页存储管理：把线性地址空间和物理地址空间分别划分为大小相同的块，每块长为4KB
这样的块称为页，通过分页管理机制实现线性地址空间到物理地址空间的
映射，实现线性地址到物理地址的转换
```

---

版权声明：本文为知乎博主「玩转Linux内核」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
原文链接：https://zhuanlan.zhihu.com/p/449157752