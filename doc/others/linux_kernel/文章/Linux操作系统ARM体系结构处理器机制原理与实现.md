## ARM 的概念

ARM(Advanced RISC Machine)，既可以认为是一个公司的名字，也可以认为是对一类微处理器的通称，还可以认为是一种技术的名字。
ARM 公司并不生产芯片也不销售芯片，它只出售芯片技术授权。其合作公司针对不同需求搭配各类硬件部件，比如 UART、SDI、I2C 等，从而设计出不同的 SoC 芯片。

## ARM 的应用场景

基于 ARM 的处理器具有高速度、低功耗、价格低等优点被广泛应用于以下领域：

- 为通信、消费电子、成像设备等产品，提供可运行复杂操作系统的开放应用平台；
- 在海量存储、汽车电子、工业控制和网络应用等领域，提供实时嵌入式应用；
- 安全系统，比如信用卡、SIM 卡等。

## ARM的技术特征

ARM 架构支持 32 位的 **ARM 指令集**和 16 位的 **Thumb 指令集**（大大减小了代码的存储空间）。

这里先以一个例子解释一下架构、核、处理器和芯片之间的特征：S3C2440，这是一款SoC芯片，注意，它不是cpu。
2440和我们熟知的51单片机有点类似，都属于嵌入式，嵌入式的发展到目前经历了三个阶段，分别是SCM、MCU、SoC。
51属于SCM或MCU，而2440就属于SoC了，先来看看51单片机的内部结构。

![img](https://pic1.zhimg.com/80/v2-3902214bd712c07886cec5c0faafd3a4_720w.webp)

其内部结构可以简单的分成两部分：cpu和外设。

**我们再看一下再来看2440的内部结构：**

![img](https://pic2.zhimg.com/80/v2-3c72a4461c239fb5b1f48c4f47337c9d_720w.webp)

arm920t就是它的处理器，处理器和核在我看来在这里是一个概念，只不过一个是硬概念，一个是软概念。这里的920t就既是处理器又是核。而三星做的就是除了这个cpu外其他的东西。

## RM版本系列

**ARM版本Ⅰ：V1版架构。**

该版架构只在原型机ARM1出现过，只有26位的寻址空间，没有用于商业产品。

**其基本性能有:**

- 基本的数据处理指令（无乘法）；
- 基于字节、半字和字的Load/Store指令;
- 转移指令，包括子程序调用及链接指令；
- 供操作系统使用的软件中断指令SWI； 寻址空间：64MB（226）。

**ARM版本Ⅱ： V2版架构**

该版架构对V1版进行了扩展，例如ARM2和ARM3（V2a）架构。包含了对32位乘法指令和协处理器指令的支持。 版本2a是版本2的变种，ARM3芯片采用了版本2a，是第一片采用片上Cache的ARM处理器。同样为26位寻址空间，现在已经废弃不再使用。

**V2版架构与版本V1相比，增加了以下功能：**

- 乘法和乘加指令；
- 支持协处理器操作指令；
- 快速中断模式；
- SWP/SWPB的最基本存储器与寄存器交换指令;
- 寻址空间：64MB。

**ARM版本Ⅲ ： V3版架构**

ARM作为独立的公司，在1990年设计的第一个微处理器采用的是版本3的ARM6。它作为IP核、独立的处理器、具有片上高速缓存、MMU和写缓冲的集成CPU。 变种版本有3G和3M。版本3G是不与版本2a向前兼容的版本3，版本3M引入了有符号和无符号数乘法和乘加指令，这些指令产生全部64位结果。

**V3版架构（ 目前已废弃 ）对ARM体系结构作了较大的改动：**

- 寻址空间增至32位（4GB）；
- 当前程序状态信息从原来的R15寄存器移到当前程序状态寄存器CPSR（Current Program Status Register）中；
- 增加了程序状态保存寄存器SPSR（Saved Program Status Register）；
- 增加了两种异常模式，使操作系统代码可方便地使用数据访问中止异常、指令预取中止异常和未定义指令异常；
- 增加了MRS/MSR指令，以访问新增的CPSR/SPSR寄存器；
- 增加了从异常处理返回的指令功能。

**ARM版本Ⅳ ： V4版架构**

V4版架构在V3版上作了进一步扩充，V4版架构是目前应用最广的ARM体系结构，ARM7、ARM8、ARM9和StrongARM都采用该架构。 V4不再强制要求与26位地址空间兼容，而且还明确了哪些指令会引起未定义指令异常。

**指令集中增加了以下功能：**

- 符号化和非符号化半字及符号化字节的存/取指令；
- 增加了T变种，处理器可工作在Thumb状态，增加了16位Thumb指令集；
- 完善了软件中断SWI指令的功能；
- 处理器系统模式引进特权方式时使用用户寄存器操作;
- 把一些未使用的指令空间捕获为未定义指令

**ARM版本Ⅴ ： V5版架构**

V5版架构是在V4版基础上增加了一些新的指令，ARM10和Xscale都采用该版架构。

**这些新增命令有：**

- 带有链接和交换的转移BLX指令；
- 计数前导零CLZ指令；
- BRK中断指令；
- 增加了数字信号处理指令（V5TE版）；
- 为协处理器增加更多可选择的指令；
- 改进了ARM/Thumb状态之间的切换效率；
- E—增强型DSP指令集，包括全部算法操作和16位乘法操作；
- J----支持新的JAVA，提供字节代码执行的硬件和优化软件加速功能。

**ARM版本Ⅵ ： V6版架构**

V6版架构是2001年发布的，首先在2002年春季发布的ARM11处理器中使用。在降低耗电量地同时，还强化了图形处理性能。通过追加有效进行多媒体处理的SIMD(Single Instruction, Multiple Data，单指令多数据 )功能，将语音及图像的处理功能提高到了原型机的4倍。

**此架构在V5版基础上增加了以下功能：**

- THUMBTM：35%代码压缩；
- DSP扩充：高性能定点DSP功能；
- JazelleTM：Java性能优化，可提高8倍；
- Media扩充：音/视频性能优化，可提高4倍

**ARM版本ⅤⅡ：V7版架构**

V7架构是在ARMv6架构的基础上诞生的。该架构采用了Thumb-2技术,它是在ARM的Thumb代码压缩技术的基础上发展起来的, 并且保持了对现存ARM解决方案的完整的代码兼容性。

Thumb-2技术比纯32位代码少使用31％的内存,减小了系统开销。同时能够提供比已有的基于Thumb技术的解决方案高出38％的性能。

ARMv7架构还采用了NEON技术,将DSP和媒体处理能力提高了近4倍 , 并支持改良的浮点运算, 满足下一代3D图形、游戏物理应用以及传统嵌入式控制应用的需求。此外,ARMv7还支持改良的运行环境,以迎合不断增加的JIT(Just In Time)和DAC(DynamicAdaptive Compilation)技术的使用。

**ARM版本ⅤⅢ：V8版架构**

这是一个新的IP核，针对高性能的嵌入式信号处理应用而设计的，v8架构是在32位ARM架构上进行开发的，将被首先用于对扩展虚拟地址和64位数据处理技术有更高要求的产品领域，如企业应用、高档消费电子产品。

ARMv8架构包含两个执行状态：AArch64和AArch32。AArch64执行状态针对64位处理技术，引入了一个全新指令集A64；而AArch32执行状态将支持现有的ARM指令集。

目前的ARMv7架构的主要特性都将在ARMv8架构中得以保留或进一步拓展，如：TrustZone技术、虚拟化技术及NEON advanced SIMD技术，等。

![img](https://pic3.zhimg.com/80/v2-c25eb8ccafb9e9518fa31f444c42e4de_720w.webp)

其中左侧的就是架构，右侧的是处理器，也可以叫核。arm首个最成功的cpu是ARM7TDMI，是基于ARMv4的。ARM架构包含了下述RISC特性：

- 读取/储存 架构
- 不支援地址不对齐内存存取（ARMv6内核现已支援）
- 正交指令集（任意存取指令可以任意的寻址方式存取数据Orthogonal instruction set）
- 大量的16 × 32-bit 寄存器阵列（register file）
- 固定的32 bits 操作码（opcode）长度，降低编码数量所产生的耗费，减轻解码和流水线化的负担。
- 大多均为一个CPU周期执行。不同版本的架构会有所调整。

和三星相同的其他和arm合作的各大厂商通常会把它的CPU和各类外围IP都放到一起，然后自己拿着图纸去流片，生产出来的也是一个正方形，下面有很多引脚，这个东西不仅包含了CPU，还包含了其他的控制器，这个东西就叫做SOC(system on chip)。从英文来看，所谓的四核SOC什么的，本意就不是单指CPU，而是四核系统。

所以目前各大厂商所做的事情，就是买来ARM的授权，得到ARM处理器的源代码，而后自己搞一些外围IP(或者买或者自己设计)，组成一个SOC后，去流片。不同的SOC，架构不同(就是CPU如何和IP联系起来，有的以总线为核心，有的以DDR为核心)，所以，海思是拥有自主产权的SOC架构。可是，无论任何厂商，再怎么折腾，都没有怎么动过CPU，ARM核心就好好的呆在那里，那就是中央处理器。

![img](https://pic4.zhimg.com/80/v2-b8ac9e00374fc700f49bbff716a67c67_720w.webp)

**理器**

- ARM Cortex-A ：为传统的、基于虚拟存储的操作系统和应用程序而设计，支持 ARM、Thumb 和 Thumb-2 指令集；
- ARM Cortex-R：针对实时系统设计，支持 ARM、Thumb 和 Thumb-2 指令集；
- ARM Cortex-M：为对 价格敏感的产品设计，只支持 Thumb-2 指令集。

## ARM命名规则

第一个数字：系列名称：eg.ARM7、ARM9

第二个数字：Memory system

```text
2：带有MMU

4：带有MPU

6：无MMU与MPU
12345
```

第三个数字：Memory size

```text
0：标准Cache（4-128k）

2：减小的Cache

6：可变的Cache
12345
```

第四个字符：

```text
T：表示支持Thumb指令集

D：表示支持片上调试（Debug）

M：表示内嵌硬件乘法器（Multiplier）

I ：支持片上断点和调试点

E：表示支持增强型DSP功能

J ：表示支持Jazelle技术，即Java加速器

S：表示全合成式
```

----

版权声明：本文为知乎博主「玩转Linux内核」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
原文链接：https://zhuanlan.zhihu.com/p/449582466