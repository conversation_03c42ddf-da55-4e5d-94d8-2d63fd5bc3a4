## 1、定义

- 文件系统为我们直接操作文件和目录与存储设备之间充当桥梁和中介的角色

## 2、分类

- 基本文件文件（拓展文件系统）
  - ext
    - 单文件大小最大2GB
    - 数据块分散在存储设备（碎片化），读取速度慢
  - ext2
    - 单文件最大支持32TB
    - 数据块分组，读取速度提升
    - 系统崩溃和断电时易损坏文件系统
    - 存储文件依赖于索引节点表
- 日志文件系统
  - 定义
    - 先将数据写入到日志，直到文件成功写入存储设备和更新索引节点表后再删除日志项，系统崩溃或断电恢复后，继续将上次未写入存储设备的日志文件继续写入
  - 日志方法
    - 数据模式
      - 索引节点和文件都会被写入日志（安全性好但性能差）
    - 有序模式
      - 只有索引节点数据会被写入日志，但只有数据成功写入后才删除（安全性和性能居中）
    - 回写模式
      - 只有索引节点数据会被写入日志，但不控制文件数据何时写入（安全性差但性能好）
  - 分类
    - ext3
      - 日志方法为有序模式（可修改）
      - 没有数据压缩和加密功能
      - 无法恢复误删文件
    - ext4
      - 支持数据压缩和加密功能
      - 支持extent(区段)特性
      - 预分配技术为大文件预留空间
    - Reiser
      - 日志方法为回写模式
      - 支持在线调整文件系统大小
      - 可把一个文件数据写到另一个文件的数据块的空白位置（尾部压缩技术）
    - JFS
      - 日志方法为有序模式
      - 基于extent的文件分配，减少碎片化
    - XFS
      - 日志方法为回写模式
      - 在线调整文件系统仅支持扩大
- 写时复制文件系统(cow)
  - 定义
    - 修改数据时，先把源文件克隆或打快照的方式，避免直接修改源文件的内容，然后文件系统会再另外一个位置创建一块空间，以供存放新数据
  - 分类
    - Btrf
      - 高稳定性和易用性，支持动态调整已挂载的文件系统大小
    - ZFS
      - 无GPL许可，非Linux默认文件系统

## 3、命令

- 创建分区
  - 创建分区
    `fdisk /dev/sdx`
  - 打印存储设备/dev/sdx的详细信息
    `p`
  - 创建一个新分区
    `n`
  - 指定分区数量
    `num`
  - 指定起始扇区
    `输入2048或回车`
  - 指定终止扇区
    `+num G或回车(如果是单个分区)`
  - 保存更改信息
    `w`
- 创建文件系统
  - 格式化文件系统
    `mkfs.ext4 /dev/sdx`
  - 挂载到虚拟目录的挂载点
    `mount -t ext4 /dev/sdx /mnt/`
  - 实现开机自挂载
    `echo -e "/dev/sdx\t/mnt\t ext4\t defaults\t 0 0" >>/etc/fstab`
- LVM
  - 1.定义物理卷
    - 在建立的分区基础上改变分区类型
      `t`
    - 声明该分区仅为LVM系统的一部分
      `8e`
    - 打印该分区信息
      `p`
    - 保存分区信息更改
      `w`
  - 2.创建物理卷
    - 把分区定义为LVM系统的分区
      `pvcreate /dev/sdx(x=a-z)`
    - 查看已创建的物理卷列表
      `pvdisplay /dev/sdx`
  - 3.创建卷组
    - 创建卷组
      `vgcreate vgname /dev/sdx`
    - 查看已创建卷组信息
      `vgdisplay vgname`
  - 4.创建逻辑卷
    - 创建占用全部逻辑区段(卷组)的逻辑卷
      `lvcreate -l 100%FREE -n lvname vgname`
    - 查看已创建的逻辑卷信息
      `lvdisplay vgname`
  - 5.创建文逻辑卷件系统
    `mkfs.ext4 /dev/vgname/lvname`
  - 6.挂载逻辑卷到虚拟目录
    `mount /dev/vgname/lvname /mnt`
- LVM修改
  - 激活或禁用卷组
    `vgchange`
  - 删除卷组
    `vgremove`
  - 把物理卷加入到卷组中
    `vgextend`
  - 从卷组中删除物理卷
    `vgreduce`
  - 增加逻辑卷大小
    `lvextend`
  - 缩减逻辑卷大小
    `lvreduce`
  - 重新格式化逻辑卷的文件系统
    `resize2fs`

## 4、注意事项

> - 文件系统通过索引节点表来存放文件信息，并通过索引节点号来标识文件
> - IDE驱动器的存储设备名使用dev/hdx，SCSI和SATA驱动器的存储设备名使用/dev/sdx(x=a-z)
> - 传统模式的拓展文件系统空间只能通过调整单块磁盘的分区大小或是购置更大磁盘，再把文件系统手动移动到新磁盘的方式
> - 逻辑卷管理允许将一个或多个物理磁盘和物理分区加入到现有文件系统，动态拓展文件系统空间
> - 一个或多个物理磁盘或物理分区组成一个卷组
> - 逻辑分区建立在卷组基础上，逻辑分区包含文件系统
> - 一个逻辑卷相当于一个物理分区，可以在逻辑卷建立文件系统，然后挂载到虚拟目录使用
> - 逻辑卷管理架构：硬盘--物理分区--物理卷--卷组--逻辑分区--逻辑卷
> - LVM使用的三种技术为快照，条带化，镜像
> - 卷组名用来标识创建逻辑卷时使用的卷组

----

版权声明：本文为博主[Tjane'Blogs](https://www.cnblogs.com/tjane)的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。

原文链接： https://www.cnblogs.com/tjane/p/16795365.html

