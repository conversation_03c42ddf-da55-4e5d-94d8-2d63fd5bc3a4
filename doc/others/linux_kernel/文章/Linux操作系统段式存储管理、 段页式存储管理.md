## 1、段式存储管理

### 1.1分段

- 进程的地址空间：按照程序自身的逻辑关系划分为若干个段，每个段都有一个段名（在低级语言中，程序员使用段名来编程），每段从0开始编址。
- 内存分配规则：以段为单位进行分配，每个段在内存中占连续空间，但各段之间可以不相邻。



![img](https://pic3.zhimg.com/80/v2-0b9b91ad1f8f67e7e15a0fc719bc1502_720w.webp)



- 分段系统的逻辑地址结构由段号（段名）和段内地址（段内偏移量）所组成。



![img](https://pic1.zhimg.com/80/v2-918b6a6b3e75201ee2d205365b162f18_720w.webp)



### 1.2段表



![img](https://pic2.zhimg.com/80/v2-9cb62088c79fd7e8f31b17fad2d1d8bd_720w.webp)



- 每一个程序设置一个段表，放在内存,属于进程的现场信息

  

### 1.3地址变换



![img](https://pic2.zhimg.com/80/v2-d0a5d7ae823e2c0de3196de7d181d849_720w.webp)



![img](https://pic3.zhimg.com/80/v2-6449d02941134e902da8ff539a929fce_720w.webp)



### 1.4段的保护

- 越界中断处理

1.进程在执行过程中，有时需要扩大分段，如数据段。由于要访问的地址超出原有的段长，所以发越界中断。操作系统处理中断时 ，首先判断该段的“扩充位”，如可扩充，则增加段的长度；否则按出错处理

- 缺段中断处理

1. 检查内存中是否有足够的空闲空间
   ①若有，则装入该段，修改有关数据结构，中断返回
   ②若没有，检查内存中空闲区的总和是否满足要求，是则应采用紧缩技术，转 ① ；否则，淘汰一（些）段，转①

### 1.5段的动态连接

1. 为何要进行段的动态链接？
2. 大型程序由若干程序段，若干数据段组成
3. 进程的某些程序段在进程运行期间可能根本不用
4. 互斥执行的程序段没有必要同时驻留内存
5. 有些程序段执行一次后不再用到
6. 静态链接花费时间，浪费空间

- 在一个程序运行开始时，只将主程序段装配好并调入主存。其它各段的装配是在主程序段运行过程中逐步进行的。每当需要调用一个新段时，再将这个新段装配好，并与主程序段连接。
  页式存储管理：难以完成动态链接，其逻辑地址是一维的

### 1.6信息的保护与共享

- 这里主要与页式存储管理进行一下对比。
- 分段比分页更容易实现信息的共享和保护。



![img](https://pic1.zhimg.com/80/v2-8e41f75bf364037e526db0f656082c48_720w.webp)



- 纯代码举例：比如，有一个代码段只是简单的输出“Hello World!”。



![img](https://pic3.zhimg.com/80/v2-bc3414d6cfe0c16d679779b1f0217f26_720w.webp)



### 1.7页式系统与段式系统的对比



![img](https://pic2.zhimg.com/80/v2-a8a0f2c4f0bd8f666f321b565d41f095_720w.webp)



- 补充：
- 段长是可变的，页的大小是固定的。

1. 分段存储：段内地址W字段溢出将产生越界中断。
2. 分页存储：段内地址W字段溢出会自动加入到页号中。

### 1.8总结



![img](https://pic1.zhimg.com/80/v2-6db3da902a93c013e81a2c67d9093b44_720w.webp)

## 2、段页式存储管理

### 2.1分页、分段的有缺点分析



![img](https://pic3.zhimg.com/80/v2-20513fde9949e32446b67e53ad670b7e_720w.webp)



### 2.2基本思想

- 用户程序划分：按段式划分（对用户来讲，按段的逻辑关系进行划分；对系统讲，按页划分每一段）
- 逻辑地址：



![img](https://pic4.zhimg.com/80/v2-b1839841926eb05069b042989e8cfd3f_720w.webp)



- 内存划分：按页式存储管理方案
- 内存分配：以页为单位进行分配



![img](https://pic2.zhimg.com/80/v2-572d543c9b5da3027966ded34d56dfd1_720w.webp)



### 2.3逻辑地址结构



![img](https://pic1.zhimg.com/80/v2-f49ff2368aa4983ffe010a1b5c1f04fc_720w.webp)

### 2.4段表页表



![img](https://pic3.zhimg.com/80/v2-89c049df540ee806c93e1635e2f72d7a_720w.webp)

### 2.5地址转换



![img](https://pic3.zhimg.com/80/v2-f83f355d2c2b1da2c9a25a73176fb896_720w.webp)

### 2.6评价

- 优点：

1. 保留了分段和请求分页存储管理的全部优点
2. 提供了虚存空间，能更有效利用主存

- 缺点：

1. 增加了硬件成本

2. 系统复杂度较大

   

### 2.7总结



![img](https://pic1.zhimg.com/80/v2-7495c4ec766121e40fe80b7a42160108_720w.webp)

------

版权声明：本文为知乎博主「Linux内核库」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文 出处链接及本声明。 

原文链接：https://zhuanlan.zhihu.com/p/466602063
