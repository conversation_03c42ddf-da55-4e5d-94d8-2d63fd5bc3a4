## 一. 前言
 
  Linux操作系统内核是服务端学习的根基，也是提高编程能力、源码阅读能力和进阶知识学习能力的重要部分，本文开始将记录Linux操作系统中的各个部分源码学习历程。

  关于如何学习源码，个人觉得可以从以下角度入手，有效地提高阅读和学习的效率。（学习语言就不说了，这是基本功。学习IDE推荐Source Insight或者Visual Studio，网站源码阅读推荐woboq）

* 理解代码的组织结构。 以Linux源码举例，首先你得知道操作系统分为哪几个部分，他们单独做了什么功能，如何进行配合完成更为具体的功能。建立整体的印象有助于后续深入学习的时候方便理解，毕竟代码是用的不是看的，理解他的作用有利于理解为什么要这么做。

* 深入各个模块学习
  * 模块接口： 这里推荐微软的画图工具visio或者思维导图xmind，用其画图可以将各个模块的接口列出，并绘制各个模块之间的关系，通过了解接口可以清楚各个模块之间的关系，即绘制模块组织图
  * 工作流程：通过上面一步得到各模块间的关系，然后实际用断点或log等方式看一看整体的工作流程，在模块组织图的基础上绘制程序流程图
  * 模块粘合层：我们的代码有很多都是用来粘合代码的，比如中间件（middleware）、Promises 模式、回调（Callback）、代理委托、依赖注入等。这些代码模块间的粘合技术是非常重要的，因为它们会把本来平铺直述的代码给分裂开来，让你不容易看明白它们的关系。这些可以作为程序流程图的补充，让其中本来无法顺畅衔接的地方变得通畅无阻。
  * 模块具体实现 ：这是最难得地方，涉及到大量具体源码的学习。深入细节容易迷失在细节的海洋里，因此需要有一些重点去关注，将非重点的内容省略。通过学习绘制模块具体架构图和模块的算法时序图，可以帮助你更好的掌握源码的精髓。

* 需要关注的包括
  * 代码逻辑。代码有两种逻辑，一种是业务逻辑，这种逻辑是真正的业务处理逻辑；另一种是控制逻辑，这种逻辑只是用控制程序流转的，不是业务逻辑。比如：flag 之类的控制变量，多线程处理的代码，异步控制的代码，远程通讯的代码，对象序列化反序列化的代码等。这两种逻辑你要分开，很多代码之所以混乱就是把这两种逻辑混在一起了。
  * 重要的算法。一般来说，我们的代码里会有很多重要的算法，我说的并不一定是什么排序或是搜索算法，可能会是一些其它的核心算法，比如一些索引表的算法，全局唯一 ID 的算法、信息推荐的算法、统计算法、通读算法（如 Gossip）等。这些比较核心的算法可能会非常难读，但它们往往是最有技术含量的部分。
  * 底层交互。有一些代码是和底层系统的交互，一般来说是和操作系统或是 JVM 的交互。因此，读这些代码通常需要一定的底层技术知识，不然，很难读懂。

* 可以忽略的包括
  * 出错处理。根据二八原则，20% 的代码是正常的逻辑，80% 的代码是在处理各种错误，所以，你在读代码的时候，完全可以把处理错误的代码全部删除掉，这样就会留下比较干净和简单的正常逻辑的代码。排除干扰因素，可以更高效地读代码。
  * 数据处理。只要你认真观察，就会发现，我们好多代码就是在那里倒腾数据。比如 DAO、DTO，比如 JSON、XML，这些代码冗长无聊，不是主要逻辑，可以不理。

* 忽略过多的实现细节。在第一遍阅读源码时，已弄懂整体流程为主，至于具体的实现细节先简单的理清处过一遍，不用过于纠结。当梳理清楚全部的框架逻辑后，第二遍再深入的学习研究各个模块的实现，此时应该解决第一遍中的疑惑。第三遍可以跳出代码的实现，来看Linux的设计思路、编程艺术和演进之路。

* 重在实践。Linux的代码都是可以调试的，看很多遍也许不如跟着调试走一遍，然后再自己修改修改做一些小测试。

* 传授知识。当你能将知识讲述给别人听，并让别人听懂时，你已经可以自豪的说洞悉了这些知识。所以不妨从一个小的例子开始自说自话，看能不能自圆其说，甚至写成博客、做成PPT给大家讲解。

  说了一大堆的废话，下面就正式开始操作系统的深入学习记录之旅了。

## 二. 混沌初开

  本文分析从按下电源键到加载BIOS以及后续bootloader的整个过程。犹如盘古开天辟地一般，该过程将混沌的操作系统世界分为清晰的内核态和用户态，并经历从实模式到保护模式的变化。这里先简单介绍一下名词，便于后续理解。

* 实模式（Real Mode)：又名 Real Address Mode，在此模式下地址访问的是真实地内存地址所在位置。在此模式下，可以使用20位（1MB）的地址空间，软件可以不受限制的操作所有地址的空间和IO设备。
* 保护模式（Protected Mode)：又名 Protected Virtual Address Mode，采用虚拟内存、页等机制对内存进行了保护，比起实模式更为安全可靠，同时也增加了灵活性和扩展性。

### 2.1 从启动电源到BIOS
  
  当我们按下电源键，主板会发向电源组发出信号，接收到信号后，电源会提供合适的电压给计算机。当主板收到电源正常启动的信号后，主板会启动CPU。CPU重置所有寄存器数据，并设置初始化数据，这个初始化数据在X86架构里如下所示：

    IP          0xfff0
    CS selector 0xf000
    CS base     0xffff0000
    IP/EIP (Instruction Pointer) : 指令指针寄存器，记录将要执行的指令在代码段内的偏移地址
    CS（Code Segment Register）：代码段寄存器，指向CPU当前执行代码在内存中的区域（定义了存放代码的存储器的起始地址）
  
  实模式采取内存段来管理 0 - 0xFFFFF的这1M内存空间，但是由于只有16位寄存器，所以最大地址只能表示为0xFFFFF（64KB)，因此不得不采取将内存按段划分为64KB的方式来充分利用1M空间。也就是上所示的，采取段选择子 + 偏移量的表示法。这种方法在保护模式中对于页的设计上也沿用了下来，可谓祖传的智慧了。具体的计算公式如下所示：

    PhysicalAddress = Segment Selector * 16 + Offset
    
  该部分由硬件完成，通过计算访问0XFFFF0，如果该位置没有可执行代码则计算机无法启动。如果有，则执行该部分代码，这里也就是我们故事的开始，BIOS程序了。

### 2.2 BIOS到BootLoader

  BIOS执行程序存储在ROM中，起始位置为0XFFFF0，当CS:IP指向该位置时，BIOS开始执行。BIOS主要包括以下内存映射：

    0x00000000 - 0x000003FF - Real Mode Interrupt Vector Table
    0x00000400 - 0x000004FF - BIOS Data Area
    0x00000500 - 0x00007BFF - Unused
    0x00007C00 - 0x00007DFF - Our Bootloader
    0x00007E00 - 0x0009FFFF - Unused
    0x000A0000 - 0x000BFFFF - Video RAM (VRAM) Memory
    0x000B0000 - 0x000B7777 - Monochrome Video Memory
    0x000B8000 - 0x000BFFFF - Color Video Memory
    0x000C0000 - 0x000C7FFF - Video ROM BIOS
    0x000C8000 - 0x000EFFFF - BIOS Shadow Area
    0x000F0000 - 0x000FFFFF - System BIOS
 
 其中最重要的莫过于中断向量表和中断服务程序。BIOS程序在内存最开始的位置（0x00000）用1 KB的内存空间（0x00000～0x003FF）构建中断向量表，在紧挨着它的位置用256字节的内存空间构建BIOS数据区（0x00400～0x004FF），并在大约57 KB以后的位置（0x0E05B）加载了8 KB左右的与中断向量表相应的若干中断服务程序。中断向量表中有256个中断向量，每个中断向量占4字节，其中两个字节是CS的值，两个字节是IP的值。每个中断向量都指向一个具体的中断服务程序。

 BIOS程序会选择一个启动设备，并将控制权转交给启动扇区中的代码。主要工作即使用中断向量和中断服务程序完成BootLoader的加载，最终将boot.img加载至0X7C00的位置启动。Linux内核通过Boot Protocol定义如何实现该引导程序，有如GRUB 2和syslinux等具体实现方式，这里仅介绍GRUB2。

### 2.3 BootLoader的工作

  boot.img由boot.S编译而成，512字节，安装在启动盘的第一个扇区，即MBR。由于空间有限，其代码十分简单，仅仅是起到一个引导的作用，指向后续的核心镜像文件，即core.img。core.img包括很多重要的部分，如lzma_decompress.img、diskboot.img、kernel.img等，结构如下图。

![image](https://user-images.githubusercontent.com/87457873/128177703-cd37ad73-edd7-4c6f-8b05-158760d2dab2.png)

整个加载流程如下：

1、boot.img加载core.img的第一个扇区，即diskboot.img，对应代码为diskboot.S<br>
2、diskboot.img加载core.img的其他部分模块，先是解压缩程序 lzma_decompress.img，再往下是 kernel.img，最后是各个模块 module 对应的映像。这里需要注意，它不是 Linux 的内核，而是 grub 的内核。注意，lzma_decompress.img 对应的代码是 startup_raw.S，本来 kernel.img 是压缩过的，现在执行的时候，需要解压缩。<br>
3、加载完core之后，启动grub_main函数。<br>
4、grub_main函数初始化控制台，计算模块基地址，设置 root 设备，读取 grub 配置文件，加载模块。最后，将 GRUB 置于 normal 模式，在这个模式中，grub_normal_execute (from grub-core/normal/main.c) 将被调用以完成最后的准备工作，然后显示一个菜单列出所用可用的操作系统。当某个操作系统被选择之后，grub_menu_execute_entry 开始执行，它将调用 GRUB 的 boot 命令，来引导被选中的操作系统。<br>
  
  在这之前，我们所有遇到过的程序都非常非常小，完全可以在实模式下运行，但是随着我们加载的东西越来越大，实模式这 1M 的地址空间实在放不下了，所以在真正的解压缩之前，lzma_decompress.img 做了一个重要的决定，就是调用 real_to_prot，切换到保护模式，这样就能在更大的寻址空间里面，加载更多的东西。

  开机时的16位实模式与内核启动的main函数执行需要的32位保护模式之间有很大的差距，这个差距谁来填补？head.S做的就是这项工作。就像 kernel boot protocol 所描述的，引导程序必须填充 kernel setup header （位于 kernel setup code 偏移 0x01f1 处） 的必要字段，这些均在head.S中定义。在这期间，head程序打开A20，打开pe、pg，废弃旧的、16位的中断响应机制，建立新的32位的IDT……这些工作都做完了，计算机已经处在32位的保护模式状态了，调用32位内核的一切条件已经准备完毕，这时顺理成章地调用main函数。后面的操作就可以用32位编译的main函数完成，从而正式启动内核，进入波澜壮阔的Linux内核操作系统之中。

## 三. 总结

  本文介绍了从按下电源开关至加载完毕BootLoader的整个过程，后续将继续分析从实模式进入保护模式，从而启动内核创建0号、1号、2号进程的整个过程。本文介绍过程中忽略了很多汇编代码以及一些虽然很重要但是不属于基本流程的知识，有兴趣了解的可以根据文中链接、文末的源码和参考资料进行更深入的学习研究。
























