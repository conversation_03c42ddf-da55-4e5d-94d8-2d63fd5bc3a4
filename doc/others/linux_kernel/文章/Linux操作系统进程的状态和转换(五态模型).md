## 1、进程的状态和装换

### 1.1进程的三态模型

**按进程在执行过程中的不同情况至少要定义三种状态：**

- 运行（running）态：进程占有处理器正在运行的状态。进程已获得CPU，其程序正在执行。在单处理机系统中，只有一个进程处于执行状态； 在多处理机系统中，则有多个进程处于执行状态。
- 就绪（ready）态：进程具备运行条件，等待系统分配处理器以便运行的状态。当进程已分配到除CPU以外的所有必要资源后，只要再获得CPU，便可立即执行，进程这时的状态称为就绪状态。在一个系统中处于就绪状态的进程可能有多个，通常将它们排成一个队列，称为就绪队列。
- 等待（wait）态：又称阻塞态或睡眠态，指进程不具备运行条件，正在等待某个时间完成的状态。也称为等待或睡眠状态，一个进程正在等待某一事件发生（例如请求I/O而等待I/O完成等）而暂时停止运行，这时即使把处理机分配给进程也无法运行，故称该进程处于阻塞状态。

![img](https://pic1.zhimg.com/80/v2-9d41935db8f3c558cc4904278295ae30_720w.webp)

**引起进程状态转换的具体原因如下：**

- 运行态→等待态：等待使用资源；
- 如等待外设传输；等待人工干预。
- 等待态→就绪态：资源得到满足；
- 如外设传输结束；人工干预完成。
- 运行态→就绪态：运行时间片到；

**出现有更高优先权进程。就绪态—→运行态：CPU 空闲时选择一个就绪进程。**

### 1.2 进程的五态模型

五态模型在三态模型的基础上增加了新建态（new）和终止态（exit）：

- 新建态：对应于进程被创建时的状态，尚未进入就绪队列。创建一个进程需要通过两个步骤：1.为新进程分配所需要的资源和建立必要的管理信息。2.设置该进程为就绪态，并等待被调度执行。
- 终止态：指进程完成任务到达正常结束点，或出现无法克服的错误而异常终止，或被操作系统及有终止权的进程所终止时所处的状态。处于终止态的进程不再被调度执行，下一步将被系统撤销，最终从系统中消失。终止一个进程需要两个步骤：1.先对操作系统或相关的进程进行善后处理（如抽取信息）。2.然后回收占用的资源并被系统删除。

![img](https://pic3.zhimg.com/80/v2-4a98dc88b067603584f3c0d6a688b4f6_720w.webp)

**引起进程状态转换的具体原因如下：**

- NULL→新建态：执行一个程序，创建一个子进程。
- 新建态→就绪态：当操作系统完成了进程创建的必要操作，并且当前系统的性能和虚拟内存的容量均允许。
- 运行态→终止态：当一个进程到达了自然结束点，或是出现了无法克服的错误，或是被操作系统所终结，或是被其他有终止权的进程所终结。
- 运行态→就绪态：运行时间片到；出现有更高优先权进程。
- 运行态→等待态：等待使用资源；如等待外设传输；等待人工干预。
- 就绪态→终止态：未在状态转换图中显示，但某些操作系统允许父进程终结子进程。
- 等待态→终止态：未在状态转换图中显示，但某些操作系统允许父进程终结子进程。
- 终止态→NULL：完成善后操作。

### 1.3 进程的七态模型

三态模型和五态模型都是假设所有进程都在内存中的事实上有序不断的创建进程，当系统资源尤其是内存资源已经不能满足进程运行的要求时，必须把某些进程挂起（suspend），对换到磁盘对换区中，释放它占有的某些资源，暂时不参与低级调度。起到平滑系统操作负荷的目的。

> 引起进程挂起的原因是多样的，主要有：
> 1.终端用户的请求。当终端用户在自己的程序运行期间发现有可疑问题时，希望暂停使自己的程序静止下来。亦即，使正在执行的进程暂停执行；若此时用户进程正处于就绪状态而未执行，则该进程暂不接受调度，以便用户研究其执行情况或对程序进行修改。我们把这种静止状态成为“挂起状态”。 2.父进程的请求。有时父进程希望挂起自己的某个子进程，以便考察和修改子进程，或者协调各子进程间的活动。 3.负荷调节的需要。当实时系统中的工作负荷较重，已可能影响到对实时任务的控制时，可由系统把一些不重要的进程挂起，以保证系统能正常运行。 4.操作系统的需要。操作系统有时希望挂起某些进程，以便检查运行中的资源使用情况或进行记账。 5.对换的需要。为了缓和内存紧张的情况，将内存中处于阻塞状态的进程换至外存上。

七态模型在五态模型的基础上增加了挂起就绪态（ready suspend）和挂起等待态（blocked suspend）。

- 挂起就绪态：进程具备运行条件，但目前在外存中，只有它被对换到内存才能被调度执行。
- 挂起等待态：表明进程正在等待某一个事件发生且在外存中。

![img](https://pic3.zhimg.com/80/v2-03cd4971e9b96643bc50267bdb924d42_720w.webp)

**引起进程状态转换的具体原因如下：**

等待态→挂起等待态：操作系统根据当前资源状况和性能要求，可以决定把等待态进程对换出去成为挂起等待态。

挂起等待态→挂起就绪态：引起进程等待的事件发生之后，相应的挂起等待态进程将转换为挂起就绪态挂起就绪态→就绪态：当内存中没有就绪态进程，或者挂起就绪态进程具有比就绪态进程更高的优先级，系统将把挂起就绪态进程转换成就绪态。

就绪态→挂起就绪态：操作系统根据当前资源状况和性能要求，也可以决定把就绪态进程对换出去成为挂起就绪态。

挂起等待态→等待态：当一个进程等待一个事件时，原则上不需要把它调入内存。但是在下面一种情况下，这一状态变化是可能的。当一个进程退出后，主存已经有了一大块自由空间,而某个挂起等待态进程具有较高的优先级并且操作系统已经得知导致它阻塞的事件即将结束，此时便发生了这一状态变化。

运行态→挂起就绪态：当一个具有较高优先级的挂起等待态进程的等待事件结束后，它需要抢占 CPU，而此时主存空间不够，从而可能导致正在运行的进程转化为挂起就绪态。另外处于运行态的进程也可以自己挂起自己。

新建态→挂起就绪态：考虑到系统当前资源状况和性能要求，可以决定新建的进程将被对换出去成为挂起就绪态。

挂起进程等同于不在内存中的进程，因此挂起进程将不参与低级调度直到它们被调换进内存。

**挂起进程具有如下特征：**

- 该进程不能立即被执行
- 挂起进程可能会等待一个事件，但所等待的事件是独立于挂起条件的，事件结束并不能导致进程具备执行条件。 （等待事件结束后进程变为挂起就绪态）
- 进程进入挂起状态是由于操作系统、父进程或进程本身阻止它的运行。
- 结束进程挂起状态的命令只能通过操作系统或父进程发出。

-----------------

版权声明：本文为知乎博主「玩转Linux内核」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
原文链接：https://zhuanlan.zhihu.com/p/447668827