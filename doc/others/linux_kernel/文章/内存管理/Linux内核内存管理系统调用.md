## 1、什么是系统调用

简单来说，系统调用就是用户程序和硬件设备之间的桥梁。

用户程序在需要的时候，通过系统调用来使用硬件设备。

系统调用的存在，有以下重要的意义:

1）用户程序通过系统调用来使用硬件，而不用关心具体的硬件设备，这样大大简化了用户程序的开发。

比如：用户程序通过write()系统调用就可以将数据写入文件，而不必关心文件是在磁盘上还是软盘上，或者其他存储上。

2）系统调用使得用户程序有更好的可移植性。

只要操作系统提供的系统调用接口相同，用户程序就可在不用修改的情况下，从一个系统迁移到另一个操作系统。

3）系统调用使得内核能更好的管理用户程序，增强了系统的稳定性。

因为系统调用是内核实现的，内核通过系统调用来控制开放什么功能及什么权限给用户程序。

这样可以避免用户程序不正确的使用硬件设备，从而破坏了其他程序。

4）系统调用有效的分离了用户程序和内核的开发。

用户程序只需关心系统调用API，通过这些API来开发自己的应用，不用关心API的具体实现。

内核则只要关心系统调用API的实现，而不必管它们是被如何调用的。

用户程序，系统调用，内核，硬件设备的调用关系如下图：

![img](https://pic4.zhimg.com/80/v2-3b6310a5b0286d44ff7ef95c029339e3_720w.webp)

## 2、Linux上的系统调用实现原理

要想实现系统调用，主要实现以下几个方面：

1. 通知内核调用一个哪个系统调用
2. 用户程序把系统调用的参数传递给内核
3. 用户程序获取内核返回的系统调用返回值

下面看看Linux是如何实现上面3个功能的。

### **2.1通知内核调用一个哪个系统调用**

每个系统调用都有一个系统调用号，系统调用发生时，内核就是根据传入的系统调用号来知道是哪个系统调用的。

在x86架构中，用户空间将系统调用号是放在eax中的，系统调用处理程序通过eax取得系统调用号。

系统调用号定义在内核代码：arch/alpha/include/asm/unistd.h 中，可以看出linux的系统调用不是很多。

### **2.2用户程序把系统调用的参数传递给内核**

系统调用的参数也是通过寄存器传给内核的，在x86系统上，系统调用的前5个参数放在ebx,ecx,edx,esi和edi中，如果参数多的话，还需要用个单独的寄存器存放指向所有参数在用户空间地址的指针。

一般的系统调用都是通过C库(最常用的是glibc库)来访问的，Linux内核提供一个从用户程序直接访问系统调用的方法。

----

版权声明：本文为知乎博主「[极致Linux内核](https://www.zhihu.com/people/linuxwang-xian-sheng)」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。

原文链接：https://zhuanlan.zhihu.com/p/549908561