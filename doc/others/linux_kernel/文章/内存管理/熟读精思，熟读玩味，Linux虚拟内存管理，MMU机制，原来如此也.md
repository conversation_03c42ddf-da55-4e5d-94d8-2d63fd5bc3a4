## MMU

现代操作系统普遍采用虚拟内存管理（Virtual Memory Management）机制，这需要处理器中的MMU（Memory Management Unit，内存管理单元）提供支持。

首先引入 PA 和 VA 两个概念。

## PA

如果处理器没有MMU，或者有MMU但没有启用，CPU执行单元发出的内存地址将直接传到芯片引脚上，被内存芯片（以下称为物理内存，以便与虚拟内存区分）接收，这称为PA（Physical Address，以下简称PA），如下图所示。

![image](https://user-images.githubusercontent.com/87457873/127427861-3e570e26-bc38-4f7c-9bea-39838f710081.png)


## VA
如果处理器启用了MMU，CPU执行单元发出的内存地址将被MMU截获，从CPU到MMU的地址称为虚拟地址（Virtual Address，以下简称VA），而MMU将这个地址翻译成另一个地址发到CPU芯片的外部地址引脚上，也就是将VA映射成PA，如下图所示。

![image](https://user-images.githubusercontent.com/87457873/127427893-f5b2ed4b-d905-40c3-9898-ea9ff060d4bd.png)

如果是32位处理器，则内地址总线是32位的，与CPU执行单元相连（图中只是示意性地画了4条地址线），而经过MMU转换之后的外地址总线则不一定是32位的。也就是说，虚拟地址空间和物理地址空间是独立的，32位处理器的虚拟地址空间是4GB，而物理地址空间既可以大于也可以小于4GB。

MMU将VA映射到PA是以页（Page）为单位的，32位处理器的页尺寸通常是4KB。例如，MMU可以通过一个映射项将VA的一页0xb7001000~0xb7001fff映射到PA的一页0x2000~0x2fff，如果CPU执行单元要访问虚拟地址0xb7001008，则实际访问到的物理地址是0x2008。物理内存中的页称为物理页面或者页帧（Page Frame）。虚拟内存的哪个页面映射到物理内存的哪个页帧是通过页表（Page Table）来描述的，页表保存在物理内存中，MMU会查找页表来确定一个VA应该映射到什么PA。

## 进程地址空间

![image](https://user-images.githubusercontent.com/87457873/127427923-59164b9b-fd56-4cca-9a77-d8adee1eb927.png)

x86平台的虚拟地址空间是0x0000 0000~0xffff ffff，大致上前3GB（0x0000 0000~0xbfff ffff）是用户空间，后1GB（0xc000 0000~0xffff ffff）是内核空间。

Text Segmest 和 Data Segment

* Text Segment，包含.text段、.rodata段、.plt段等。是从/bin/bash加载到内存的，访问权限为r-x。
* Data Segment，包含.data段、.bss段等。也是从/bin/bash加载到内存的，访问权限为rw-。

## 堆和栈

* 堆（heap）：堆说白了就是电脑内存中的剩余空间，malloc函数动态分配内存是在这里分配的。在动态分配内存时堆空间是可以向高地址增长的。堆空间的地址上限称为Break，堆空间要向高地址增长就要抬高Break，映射新的虚拟内存页面到物理内存，这是通过系统调用brk实现的，malloc函数也是调用brk向内核请求分配内存的。
* 栈（stack）：栈是一个特定的内存区域，其中高地址的部分保存着进程的环境变量和命令行参数，低地址的部分保存函数栈帧，栈空间是向低地址增长的，但显然没有堆空间那么大的可供增长的余地，因为实际的应用程序动态分配大量内存的并不少见，但是有几十层深的函数调用并且每层调用都有很多局部变量的非常少见。

如果写程序的时候没有注意好内存的分配问题，在堆和栈这两个地方可能产生以下几种问题：

1、内存泄露：如果你在一个函数里通过 malloc 在堆里申请了一块空间，并在栈里声明一个指针变量保存它，那么当该函数结束时，该函数的成员变量将会被释放，包括这个指针变量，那么这块空间也就找不回来了，也就无法得到释放。久而久之，可能造成下面的内存泄露问题。<br>
2、栈溢出：如果你放太多数据到栈中（例如大型的结构体和数组），那么就可能会造成“栈溢出”（Stack Overflow）问题，程序也将会终止。为了避免这个问题，在声明这类变量时应使用 malloc 申请堆的空间。<br>
3、野指针 和 段错误：如果一个指针所指向的空间已经被释放，此时再试图用该指针访问已经被释放了的空间将会造成“段错误”（Segment Fault）问题。此时指针已经变成野指针，应该及时手动将野指针置空。<br>

## 虚拟内存管理的作用

1、虚拟内存管理可以控制物理内存的访问权限。物理内存本身是不限制访问的，任何地址都可以读写，而操作系统要求不同的页面具有不同的访问权限，这是利用CPU模式和MMU的内存保护机制实现的。<br>
2、虚拟内存管理最主要的作用是让每个进程有独立的地址空间。所谓独立的地址空间是指，不同进程中的同一个VA被MMU映射到不同的PA，并且在某一个进程中访问任何地址都不可能访问到另外一个进程的数据，这样使得任何一个进程由于执行错误指令或恶意代码导致的非法内存访问都不会意外改写其它进程的数据，不会影响其它进程的运行，从而保证整个系统的稳定性。另一方面，每个进程都认为自己独占整个虚拟地址空间，这样链接器和加载器的实现会比较容易，不必考虑各进程的地址范围是否冲突。<br>

![image](https://user-images.githubusercontent.com/87457873/127428070-9b54fd66-034e-4b57-b5ba-948b5611d9b8.png)

3、VA到PA的映射会给分配和释放内存带来方便，物理地址不连续的几块内存可以映射成虚拟地址连续的一块内存。比如要用malloc分配一块很大的内存空间，虽然有足够多的空闲物理内存，却没有足够大的连续空闲内存，这时就可以分配多个不连续的物理页面而映射到连续的虚拟地址范围。

![image](https://user-images.githubusercontent.com/87457873/127428084-23b8e2ca-a89e-4ea3-8ec7-c96a6d2848ea.png)

4、一个系统如果同时运行着很多进程，为各进程分配的内存之和可能会大于实际可用的物理内存，虚拟内存管理使得这种情况下各进程仍然能够正常运行。因为各进程分配的只不过是虚拟内存的页面，这些页面的数据可以映射到物理页面，也可以临时保存到磁盘上而不占用物理页面，在磁盘上临时保存虚拟内存页面的可能是一个磁盘分区，也可能是一个磁盘文件，称为交换设备（Swap Device）。当物理内存不够用时，将一些不常用的物理页面中的数据临时保存到交换设备，然后这个物理页面就认为是空闲的了，可以重新分配给进程使用，这个过程称为换出（Page out）。如果进程要用到被换出的页面，就从交换设备再加载回物理内存，这称为换入（Page in）。换出和换入操作统称为换页（Paging），因此：\[\mbox{系统中可分配的内存总量} = \mbox{物理内存的大小} + \mbox{交换设备的大小}\]

如下图所示。第一张图是换出，将物理页面中的数据保存到磁盘，并解除地址映射，释放物理页面。第二张图是换入，从空闲的物理页面中分配一个，将磁盘暂存的页面加载回内存，并建立地址映射。

![image](https://user-images.githubusercontent.com/87457873/127428113-d8d630b3-cbce-4740-bbf5-06e330a9c57e.png)

## malloc 和 free

C标准库函数malloc可以在堆空间动态分配内存，它的底层通过brk系统调用向操作系统申请内存。动态分配的内存用完之后可以用free释放，更准确地说是归还给malloc，这样下次调用malloc时这块内存可以再次被分配。

```c
#include <stdlib.h>

void *malloc(size_t size);
返回值：成功返回所分配内存空间的首地址，出错返回NULL

void free(void *ptr);
```

malloc的参数size表示要分配的字节数，如果分配失败（可能是由于系统内存耗尽）则返回NULL。由于malloc函数不知道用户拿到这块内存要存放什么类型的数据，所以返回通用指针void *，用户程序可以转换成其它类型的指针再访问这块内存。malloc函数保证它返回的指针所指向的地址满足系统的对齐要求，例如在32位平台上返回的指针一定对齐到4字节边界，以保证用户程序把它转换成任何类型的指针都能用。
动态分配的内存用完之后可以用free释放掉，传给free的参数正是先前malloc返回的内存块首地址。

**示例**

```c
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

typedef struct {
    int number;
    char *msg;
} unit_t;

int main(void)
{
    unit_t *p = malloc(sizeof(unit_t));

    if (p == NULL) {
        printf("out of memory\n");
        exit(1);
    }
    p->number = 3;
    p->msg = malloc(20);
    strcpy(p->msg, "Hello world!");
    printf("number: %d\nmsg: %s\n", p->number, p->msg);
    free(p->msg);
    free(p);
    p = NULL;

    return 0;
}
```

**说明**

* `unit_t *p = malloc(sizeof(unit_t))`;这一句，等号右边是`void *`类型，等号左边是`unit_t *`类型，编译器会做隐式类型转换，我们讲过`void *`类型和任何指针类型之间可以相互隐式转换。
* 虽然内存耗尽是很不常见的错误，但写程序要规范，malloc之后应该判断是否成功。以后要学习的大部分系统函数都有成功的返回值和失败的返回值，每次调用系统函数都应该判断是否成功。
* `free(p)`;之后，p所指的内存空间是归还了，但是p的值并没有变，因为从free的函数接口来看根本就没法改变p的值，p现在指向的内存空间已经不属于用户，换句话说，p成了野指针，为避免出现野指针，我们应该在`free(p)`;之后手动置`p = NULL`;。
* 应该先`free(p->msg)`，再`free(p)`。如果先`free(p)`，p成了野指针，就不能再通过`p->msg`访问内存了。

## 内存泄漏

如果一个程序长年累月运行（例如网络服务器程序），并且在循环或递归中调用malloc分配内存，则必须有free与之配对，分配一次就要释放一次，否则每次循环都分配内存，分配完了又不释放，就会慢慢耗尽系统内存，这种错误称为内存泄漏（Memory Leak）。另外，malloc返回的指针一定要保存好，只有把它传给free才能释放这块内存，如果这个指针丢失了，就没有办法free这块内存了，也会造成内存泄漏。例如：

```c
void foo(void)
{
    char *p = malloc(10);
    ...
}
```

foo函数返回时要释放局部变量p的内存空间，它所指向的内存地址就丢失了，这10个字节也就没法释放了。内存泄漏的Bug很难找到，因为它不会像访问越界一样导致程序运行错误，少量内存泄漏并不影响程序的正确运行，大量的内存泄漏会使系统内存紧缺，导致频繁换页，不仅影响当前进程，而且把整个系统都拖得很慢。

关于malloc和free还有一些特殊情况。malloc(0)这种调用也是合法的，也会返回一个非NULL的指针，这个指针也可以传给free释放，但是不能通过这个指针访问内存。free(NULL)也是合法的，不做任何事情，但是free一个野指针是不合法的，例如先调用malloc返回一个指针p，然后连着调用两次free§;，则后一次调用会产生运行时错误。
