# Debian透明网关配置指南

将Debian系统配置为透明网关，实现类似OpenWrt的路由器功能和透明代理。

## 1. 系统准备

### 1.1 安装必要软件包
```bash
sudo apt update
sudo apt install -y iptables iptables-persistent bridge-utils hostapd dnsmasq
sudo apt install -y v2ray xray clash-premium # 选择一个代理软件
```

### 1.2 启用IP转发
```bash
# 临时启用
echo 1 | sudo tee /proc/sys/net/ipv4/ip_forward

# 永久启用
echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf
echo 'net.ipv6.conf.all.forwarding=1' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## 2. 网络接口配置

### 2.1 配置网络接口 (/etc/netplan/01-network-manager-all.yaml)
```yaml
network:
  version: 2
  renderer: NetworkManager
  ethernets:
    eth0:  # WAN口 - 连接上级路由器
      dhcp4: true
    eth1:  # LAN口 - 内网接口
      addresses:
        - *************/24
      dhcp4: false
      dhcp6: false
```

### 2.2 应用网络配置
```bash
sudo netplan apply
sudo systemctl restart NetworkManager
```

## 3. DHCP服务配置

### 3.1 配置dnsmasq (/etc/dnsmasq.conf)
```bash
# 备份原配置
sudo cp /etc/dnsmasq.conf /etc/dnsmasq.conf.bak

# 创建新配置
sudo tee /etc/dnsmasq.conf > /dev/null <<EOF
# 基本设置
interface=eth1
bind-interfaces
domain-needed
bogus-priv

# DHCP设置
dhcp-range=*************00,***************,12h
dhcp-option=option:router,*************
dhcp-option=option:dns-server,*************

# DNS设置
server=*******
server=*******
cache-size=1000

# 禁用系统解析器
port=53
listen-address=127.0.0.1,*************
EOF
```

### 3.2 启动DHCP服务
```bash
sudo systemctl enable dnsmasq
sudo systemctl start dnsmasq
```

## 4. iptables防火墙配置

### 4.1 基本NAT规则
```bash
#!/bin/bash
# 保存为 /etc/iptables-rules.sh

# 清空现有规则
iptables -F
iptables -X
iptables -t nat -F
iptables -t nat -X
iptables -t mangle -F
iptables -t mangle -X

# 设置默认策略
iptables -P INPUT ACCEPT
iptables -P FORWARD ACCEPT
iptables -P OUTPUT ACCEPT

# NAT规则 - 将内网流量转发到外网
iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE

# 允许已建立的连接返回
iptables -A FORWARD -i eth0 -o eth1 -m state --state RELATED,ESTABLISHED -j ACCEPT

# 允许内网访问外网
iptables -A FORWARD -i eth1 -o eth0 -j ACCEPT

# 允许本地回环
iptables -A INPUT -i lo -j ACCEPT
iptables -A OUTPUT -o lo -j ACCEPT

# 保存规则
iptables-save > /etc/iptables/rules.v4
```

### 4.2 执行防火墙配置
```bash
sudo chmod +x /etc/iptables-rules.sh
sudo /etc/iptables-rules.sh
```

## 5. 透明代理配置

### 5.1 V2Ray透明代理配置

#### 5.1.1 安装V2Ray
```bash
# 官方脚本安装
curl -Ls https://install.direct/go.sh | sudo bash
```

#### 5.1.2 V2Ray配置文件 (/etc/v2ray/config.json)
```json
{
  "log": {
    "loglevel": "warning"
  },
  "inbounds": [
    {
      "tag": "transparent",
      "port": 12345,
      "protocol": "dokodemo-door",
      "settings": {
        "network": "tcp,udp",
        "followRedirect": true
      },
      "streamSettings": {
        "sockopt": {
          "tproxy": "tproxy"
        }
      }
    },
    {
      "tag": "dns-in",
      "port": 5353,
      "protocol": "dokodemo-door",
      "settings": {
        "address": "*******",
        "port": 53,
        "network": "tcp,udp"
      }
    }
  ],
  "outbounds": [
    {
      "tag": "proxy",
      "protocol": "vmess",
      "settings": {
        "vnext": [
          {
            "address": "your-proxy-server.com",
            "port": 443,
            "users": [
              {
                "id": "your-uuid-here",
                "security": "auto"
              }
            ]
          }
        ]
      },
      "streamSettings": {
        "network": "ws",
        "security": "tls",
        "wsSettings": {
          "path": "/your-path"
        }
      }
    },
    {
      "tag": "direct",
      "protocol": "freedom"
    },
    {
      "tag": "dns-out",
      "protocol": "dns"
    }
  ],
  "routing": {
    "domainStrategy": "IPOnDemand",
    "rules": [
      {
        "type": "field",
        "inboundTag": ["dns-in"],
        "outboundTag": "dns-out"
      },
      {
        "type": "field",
        "ip": ["***********/16", "10.0.0.0/8", "**********/12"],
        "outboundTag": "direct"
      },
      {
        "type": "field",
        "outboundTag": "proxy"
      }
    ]
  }
}
```

### 5.2 透明代理iptables规则
```bash
#!/bin/bash
# 保存为 /etc/transparent-proxy-rules.sh

# 创建新的链
iptables -t mangle -N V2RAY
iptables -t mangle -N V2RAY_MARK

# 直连的目标网段
iptables -t mangle -A V2RAY -d *********/8 -j RETURN
iptables -t mangle -A V2RAY -d *********/4 -j RETURN
iptables -t mangle -A V2RAY -d ***************/32 -j RETURN
iptables -t mangle -A V2RAY -d ***********/16 -p tcp -j RETURN
iptables -t mangle -A V2RAY -d ***********/16 -p udp ! --dport 53 -j RETURN

# 给TCP打标记
iptables -t mangle -A V2RAY -p tcp -j TPROXY --on-port 12345 --tproxy-mark 1
iptables -t mangle -A V2RAY -p udp -j TPROXY --on-port 12345 --tproxy-mark 1

# 应用到PREROUTING链
iptables -t mangle -A PREROUTING -j V2RAY

# 本机发出的流量
iptables -t mangle -A V2RAY_MARK -d *********/8 -j RETURN
iptables -t mangle -A V2RAY_MARK -d *********/4 -j RETURN
iptables -t mangle -A V2RAY_MARK -d ***************/32 -j RETURN
iptables -t mangle -A V2RAY_MARK -d ***********/16 -p tcp -j RETURN
iptables -t mangle -A V2RAY_MARK -d ***********/16 -p udp ! --dport 53 -j RETURN
iptables -t mangle -A V2RAY_MARK -j MARK --set-mark 1

iptables -t mangle -A OUTPUT -j V2RAY_MARK

# DNS重定向
iptables -t nat -A PREROUTING -p udp --dport 53 -j REDIRECT --to-ports 5353

# 保存规则
iptables-save > /etc/iptables/rules.v4
```

### 5.3 路由表配置
```bash
# 添加到 /etc/rc.local 或创建systemd服务

# 创建路由表
ip rule add fwmark 1 table 100
ip route add local 0.0.0.0/0 dev lo table 100
```

## 6. 服务管理

### 6.1 创建透明代理服务
```bash
# 创建 /etc/systemd/system/transparent-proxy.service
sudo tee /etc/systemd/system/transparent-proxy.service > /dev/null <<EOF
[Unit]
Description=Transparent Proxy Service
After=network.target

[Service]
Type=oneshot
ExecStart=/etc/transparent-proxy-rules.sh
ExecStart=/sbin/ip rule add fwmark 1 table 100
ExecStart=/sbin/ip route add local 0.0.0.0/0 dev lo table 100
RemainAfterExit=yes

[Install]
WantedBy=multi-user.target
EOF
```

### 6.2 启动所有服务
```bash
sudo systemctl enable transparent-proxy
sudo systemctl enable v2ray
sudo systemctl enable dnsmasq

sudo systemctl start transparent-proxy
sudo systemctl start v2ray
sudo systemctl start dnsmasq
```

## 7. 高级配置

### 7.1 分流规则
可以配置域名分流，让特定域名走直连：

```json
{
  "routing": {
    "rules": [
      {
        "type": "field",
        "domain": ["geosite:cn"],
        "outboundTag": "direct"
      },
      {
        "type": "field",
        "ip": ["geoip:cn", "geoip:private"],
        "outboundTag": "direct"
      }
    ]
  }
}
```

### 7.2 Web管理界面
可以安装OpenWrt的Luci界面或其他Web管理工具：

```bash
# 安装nginx和相关工具
sudo apt install nginx php-fpm
# 配置Web界面...
```

## 8. 故障排除

### 8.1 检查服务状态
```bash
sudo systemctl status v2ray
sudo systemctl status dnsmasq
sudo netstat -tulnp | grep :53
sudo netstat -tulnp | grep :12345
```

### 8.2 检查路由规则
```bash
ip rule list
ip route show table 100
iptables -t mangle -L -n
iptables -t nat -L -n
```

### 8.3 测试连接
```bash
# 在客户端测试
curl -I http://www.google.com
nslookup google.com
```

## 9. 安全注意事项

1. 定期更新系统和代理软件
2. 配置适当的防火墙规则
3. 监控系统资源使用情况
4. 备份重要配置文件
5. 设置访问日志和监控

## 10. 性能优化

1. 调整内核参数优化网络性能
2. 使用SSD存储提高I/O性能
3. 合理配置代理软件的并发连接数
4. 使用高性能的DNS服务器

这样配置后，Debian系统就可以像OpenWrt一样工作，为连接到它的设备提供透明代理服务。
