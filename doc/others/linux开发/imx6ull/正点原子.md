# 系统移植篇

## 初次编译

```
#!/bin/bash                                                                                                            
make ARCH=arm CROSS_COMPILE=arm-linux-gnueabihf- distclean                                                             
make ARCH=arm CROSS_COMPILE=arm-linux-gnueabihf- mx6ull_alientek_emmc_defconfig                                        
make V=1 ARCH=arm CROSS_COMPILE=arm-linux-gnueabihf- -j32
```

## 命令使用

### 获取信息

#### bdinfo

获取板子信息

#### printenv

显示环境变量，比如 baudrate、board_name、board_rec、boot_fdt、bootcmd等等

#### version

查看uboot的版本号

### 修改环境变量

#### setenv

修改DRAM中的环境变量值

#### saveenv

保存环境变量到flash中
注意：mmc0是sd卡，mmc1是emmc

```
setenv bootargs 'console=ttymxc0,115200 root=/dev/mmcblk1p2 rootwait rw'
saveenv
设置 shell和根目录
```

如果是新建或者删除，直接用setenv就可以了

### 内存操作命令

#### md

显示内存值
`md[.b, .w, .l] address [# of objects]`

#### nm

用于修改指定地址的内存值
`nm [.b, .w, .l] address`

#### mm

修改制定内存值，而且地址会自增

#### mw

使用一个指定的数据填充一段内存
`mw [.b, .w, .l] address value [count]`

#### cp

数据拷贝命令
`cp [.b, .w, .l] source target count`

#### cmp

比较两段内存是否相等
`cmp [.b, .w, .l] addr1 addr2 count`

### 网络操作命令

有两个网口，要连接ENET2,phy网络号为1

#### 使用到的环境变量

1. ipaddr 开发板ip地址，可以使用dhcp命令获取
2. ethaddr 开发板的MAC地址，一定要设置
3. gatewayip 网关地址
4. netmask 子网掩码
5. serverip 服务器ip地址，用于调试代码

```
setenv ipaddr ************
setenv ethaddr b8:ae:1d:01:00:00
setenv gatewayip ***********
setenv netmask *************
setenv serverip *************
saveenv
```

#### ping

只能ping别人

#### dhcp

用于从路由器获取ip地址，同时会通过tftp来启动linux内核

#### nfs

下载服务器中的文件
`nfs [loadAddress] [[hostIPaddr:]bootfilename]`
用来调试linux镜像和设备树文件
`nfs 80800000 *************:/home/<USER>/linux/nfs/zImage`
下载linux镜像到80800000

### EMMC和SD卡操作命令

```
mmc info 输出 MMC 设备信息
mmc read 读取 MMC 中的数据。
mmc wirte 向 MMC 设备写入数据。
mmc rescan 扫描 MMC 设备。
mmc part 列出 MMC 设备的分区。
mmc dev 切换 MMC 设备。
mmc list 列出当前有效的所有 MMC 设备。
mmc hwpartition 设置 MMC 设备的分区。
mmc bootbus…… 设置指定 MMC 设备的 BOOT_BUS_WIDTH 域的值。
mmc bootpart…… 设置指定 MMC 设备的 boot 和 RPMB 分区的大小。
mmc partconf…… 设置指定 MMC 设备的 PARTITION_CONFG 域的值。
mmc rst 复位 MMC 设备
mmc setdsr 设置 DSR 寄存器的值。
```

#### mmc dev

切换设备
`mmc dev [dev] [part]`

### FAT 格式文件系统操作命令

#### fatinfo

`fatinfo <interface> [<dev[:part]>]`

#### fatls

`fatls <interface> [<dev[:part]>] [directory]`

#### fstype

`fstype <interface> <dev>:<part>`

#### fatload

`fatload <interface> [<dev[:part]> [<addr> [<filename> [bytes [pos]]]]]`
例如：
`fatload mmc 1:1 80800000 zImage`

#### fatwrite

`fatwrite <interface> <dev[:part]> <addr> <filename> <bytes>`
默认没有使能，要在mx6ull_alientek_emmc.h中加上一行
`#define CONFIG_FAT_WRITE /* 使能 fatwrite 命令 */`
通常用于在tftp下载后使用这个下入mmc设备中

### EXT 格式文件系统操作命令

ext2load、ext2ls、ext4load、ext4ls 和 ext4write
和fat是差不多的

### BOOT 操作命令

#### bootz

在系统和设备树下载到DRAM之后，可以使用bootz只当linux镜像文件，initrd，fdt的位置
`bootz [addr [initrd[:size]] [fdt]]`
一般而言，使用如下

```
fatload mmc 1:1 80800000 zImage
fatload mmc 1:1 83000000 imx6ull-14x14-emmc-7-1024x600-c.dtb
bootz 80800000 - 83000000
```

#### bootm

用于启动uImage镜像文件
和bootz差不多

#### boot

boot命令会读取bootcmd环境变量来启动

```
网络
setenv bootcmd 'tftp 80800000 zImage; tftp 83000000 imx6ull-14x14-emmc-7-1024x600-c.dtb; bootz 80800000 - 83000000'
saveenv
boot

EMMC
setenv bootcmd 'fatload mmc 1:1 80800000 zImage; fatload mmc 1:1 83000000imx6ull-14x14-emmc-7-1024x600-c.dtb; bootz 80800000 - 83000000'
savenev
boot
```

bootargs用于设置跟文件目录

### 其他常用命令

#### reset

复位用的

#### go

`go addr [arg ...]`

```
tftp 87800000 printf.bin
go 87800000
```

#### run

用于运行环境变量中定义的命令
在EMMC和网络启动来回切换，所以自己定义环境变量

```
setenv mybootemmc 'fatload mmc 1:1 80800000 zImage; fatload mmc 1:1 83000000 imx6ull-14x14-emmc-7-1024x600-c.dtb;bootz 80800000 - 83000000'
setenv mybootnand 'nand read 80800000 4000000 800000;nand read 83000000 6000000 100000;bootz 80800000 - 83000000'
setenv mybootnet 'tftp 80800000 zImage; tftp 83000000imx6ull-14x14-emmc-7-1024x600-c.dtb; 
bootz 80800000 - 83000000'
saveenv

run mybootemmc
run mytoobnand
run mybootnet
```

#### mtest

内存读写测试命令

# linux驱动开发篇

## 字符设备驱动

### 驱动模块的加载和卸载

```
module_init(xxx_init); //注册模块加载函数
module_exit(xxx_exit); //注册模块卸载函数

示例代码 40.2.1.1 字符设备驱动模块加载和卸载函数模板
1 /* 驱动入口函数 */
2 static int __init xxx_init(void)
3 {
4 /* 入口函数具体内容 */
5 return 0;
6 }
7 
8 /* 驱动出口函数 */
9 static void __exit xxx_exit(void)
10 {
11 /* 出口函数具体内容 */
12 }
13
14 /* 将上面两个函数指定为驱动的入口和出口函数 */
15 module_init(xxx_init);
16 module_exit(xxx_exit);


insmod drv.ko 加载，不能解决依赖关系
modprobe 可以解决
rmmod drv.ko 直接卸载
modprobe -r drv.ko 连着没有在被依赖的驱动一起卸载，最好不要用这个

```

### 字符设备注册与注销

```
static inline int register_chrdev(unsigned int major, const char *name,
const struct file_operations *fops)
static inline void unregister_chrdev(unsigned int major, const char *name)

示例代码 40.2.2.1 加入字符设备注册和注销
1 static struct file_operations test_fops;
2
3 /* 驱动入口函数 */
4 static int __init xxx_init(void)
5 {
6 /* 入口函数具体内容 */
7 int retvalue = 0;
8
9 /* 注册字符设备驱动 */
10 retvalue = register_chrdev(200, "chrtest", &test_fops);
11 if(retvalue < 0){
12 /* 字符设备注册失败,自行处理 */
13 }
14 return 0;
15 }
16
17 /* 驱动出口函数 */
18 static void __exit xxx_exit(void)
19 {
20 /* 注销字符设备驱动 */
21 unregister_chrdev(200, "chrtest");
22 }
23
24 /* 将上面两个函数指定为驱动的入口和出口函数 */
25 module_init(xxx_init);
26 module_exit(xxx_exit);
```

### 动态分配设备号

```
int alloc_chrdev_region(dev_t *dev, unsigned baseminor, unsigned count, const char *name)
dev：保存申请到的设备号。
baseminor：次设备号起始地址，alloc_chrdev_region 可以申请一段连续的多个设备号，这
些设备号的主设备号一样，但是次设备号不同，次设备号以 baseminor 为起始地址地址开始递
增。一般 baseminor 为 0，也就是说次设备号从 0 开始。
count：要申请的设备号数量。
name：设备名字。


void unregister_chrdev_region(dev_t from, unsigned count)
from：要释放的设备号。
count：表示从 from 开始，要释放的设备号数量。
```

### 创建设备节点文件

驱动加载成功需要在/dev 目录下创建一个与之对应的设备节点文件，应用程序就是通过操
作这个设备节点文件来完成对具体设备的操作。
`mknod /dev/chrdevbase c 200 0`

## LED驱动

### 内存映射

因为mmu的关系，所以需要使用物理内存和虚拟内存的转换

```

1 #define ioremap(cookie,size) __arm_ioremap((cookie), (size), 
MT_DEVICE)
2
3 void __iomem * __arm_ioremap(phys_addr_t phys_addr, size_t size, 
unsigned int mtype)
4 {
5 return arch_ioremap_caller(phys_addr, size, mtype,
__builtin_return_address(0));
6 }

phys_addr：要映射的物理起始地址。
size：要映射的内存空间大小。
mtype：ioremap 的类型，可以选择 MT_DEVICE、MT_DEVICE_NONSHARED、
MT_DEVICE_CACHED 和 MT_DEVICE_WC，ioremap 函数选择 MT_DEVICE。
返回值：__iomem 类型的指针，指向映射后的虚拟空间首地址。


#define SW_MUX_GPIO1_IO03_BASE (0X020E0068)
static void __iomem* SW_MUX_GPIO1_IO03;
SW_MUX_GPIO1_IO03 = ioremap(SW_MUX_GPIO1_IO03_BASE, 4);



void iounmap (volatile void __iomem *addr)

iounmap(SW_MUX_GPIO1_IO03);
```

### IO内存访问函数

```
1 u8 readb(const volatile void __iomem *addr)
2 u16 readw(const volatile void __iomem *addr)
3 u32 readl(const volatile void __iomem *addr)

1 void writeb(u8 value, volatile void __iomem *addr)
2 void writew(u16 value, volatile void __iomem *addr)
3 void writel(u32 value, volatile void __iomem *addr)
```

## 设备树OF函数

Linux 内核使用 device_node 结构体来描述一个节点，此结构体定
义在文件 include/linux/of.h 中，定义如下：

```
示例代码 43.3.9.1 device_node 节点
49 struct device_node {
50 const char *name; /* 节点名字 */
51 const char *type; /* 设备类型 */
52 phandle phandle;
53 const char *full_name; /* 节点全名 */
54 struct fwnode_handle fwnode;
55
56 struct property *properties; /* 属性 */
57 struct property *deadprops; /* removed 属性 */
58 struct device_node *parent; /* 父节点 */
59 struct device_node *child; /* 子节点 */
60 struct device_node *sibling;
61 struct kobject kobj;
62 unsigned long _flags;
63 void *data;
64 #if defined(CONFIG_SPARC)
65 const char *path_component_name;
66 unsigned int unique_id;
67 struct of_irq_controller *irq_trans;
68 #endif
69 };
```

### 查找结点函数

```
of_find_node_by_name 函数通过节点名字查找指定的节点，函数原型如下：
struct device_node *of_find_node_by_name(struct device_node *from,
const char *name);
函数参数和返回值含义如下：
from：开始查找的节点，如果为 NULL 表示从根节点开始查找整个设备树。
name：要查找的节点名字。
返回值：找到的节点，如果为 NULL 表示查找失败。


of_find_node_by_type 函数通过 device_type 属性查找指定的节点，函数原型如下：
struct device_node *of_find_node_by_type(struct device_node *from, const char *type)
函数参数和返回值含义如下：
from：开始查找的节点，如果为 NULL 表示从根节点开始查找整个设备树。
type：要查找的节点对应的 type 字符串，也就是 device_type 属性值。
返回值：找到的节点，如果为 NULL 表示查找失败。

of_find_compatible_node 函数根据 device_type 和 compatible 这两个属性查找指定的节点，
函数原型如下：
struct device_node *of_find_compatible_node(struct device_node *from,
const char *type, 
const char *compatible)
函数参数和返回值含义如下：
from：开始查找的节点，如果为 NULL 表示从根节点开始查找整个设备树。
type：要查找的节点对应的 type 字符串，也就是 device_type 属性值，可以为 NULL，表示
忽略掉 device_type 属性。
compatible：要查找的节点所对应的 compatible 属性列表。
返回值：找到的节点，如果为 NULL 表示查找失败

of_find_matching_node_and_match 函数通过 of_device_id 匹配表来查找指定的节点，函数原
型如下：
struct device_node *of_find_matching_node_and_match(struct device_node *from,
 const struct of_device_id *matches,
 const struct of_device_id **match)
函数参数和返回值含义如下：
from：开始查找的节点，如果为 NULL 表示从根节点开始查找整个设备树。
matches：of_device_id 匹配表，也就是在此匹配表里面查找节点。
match：找到的匹配的 of_device_id。
返回值：找到的节点，如果为 NULL 表示查找失败

of_find_node_by_path 函数通过路径来查找指定的节点，函数原型如下：
inline struct device_node *of_find_node_by_path(const char *path)
函数参数和返回值含义如下：
path：带有全路径的节点名，可以使用节点的别名，比如“/backlight”就是 backlight 这个
节点的全路径。
返回值：找到的节点，如果为 NULL 表示查找失败
```

### 查找父子节点

```
of_get_parent 函数用于获取指定节点的父节点(如果有父节点的话)，函数原型如下：
struct device_node *of_get_parent(const struct device_node *node)
函数参数和返回值含义如下：
node：要查找的父节点的节点。
返回值：找到的父节点。


of_get_next_child 函数用迭代的方式查找子节点，函数原型如下：
struct device_node *of_get_next_child(const struct device_node *node,
 struct device_node *prev)
函数参数和返回值含义如下：
node：父节点。
prev：前一个子节点，也就是从哪一个子节点开始迭代的查找下一个子节点。可以设置为
NULL，表示从第一个子节点开始。
返回值：找到的下一个子节点。
```

### 提取属性值的OF函数

```
Linux 内
核中使用结构体 property 表示属性，此结构体同样定义在文件 include/linux/of.h 中，内容如下：
示例代码 43.9.3.1 property 结构体
35 struct property {
36 char *name; /* 属性名字 */
37 int length; /* 属性长度 */
38 void *value; /* 属性值 */
39 struct property *next; /* 下一个属性 */
40 unsigned long _flags;
41 unsigned int unique_id;
42 struct bin_attribute attr;
43 };

of_find_property 函数用于查找指定的属性，函数原型如下：
property *of_find_property(const struct device_node *np,
 const char *name,
 int *lenp)
函数参数和返回值含义如下：
np：设备节点。
name： 属性名字。
lenp：属性值的字节数
返回值：找到的属性。
of_property_count_elems_of_size 函数用于获取属性中元素的数量，比如 reg 属性值是一个
数组，那么使用此函数可以获取到这个数组的大小，此函数原型如下：
int of_property_count_elems_of_size(const struct device_node *np,
 const char *propname,
int elem_size)
函数参数和返回值含义如下：
np：设备节点。
proname： 需要统计元素数量的属性名字。
elem_size：元素长度。
返回值：得到的属性元素数量。

of_property_read_u32_index 函数用于从属性中获取指定标号的 u32 类型数据值(无符号 32
位)，比如某个属性有多个 u32 类型的值，那么就可以使用此函数来获取指定标号的数据值，此
函数原型如下：
int of_property_read_u32_index(const struct device_node *np,
 const char *propname,
 u32 index, 
 u32 *out_value)
函数参数和返回值含义如下：
np：设备节点。
proname： 要读取的属性名字。
index：要读取的值标号。
out_value：读取到的值
返回值：0 读取成功，负值，读取失败，-EINVAL 表示属性不存在，-ENODATA 表示没有
要读取的数据，-EOVERFLOW 表示属性值列表太小。



这 4 个函数分别是读取属性中 u8、u16、u32 和 u64 类型的数组数据，比如大多数的 reg 属
性都是数组数据，可以使用这 4 个函数一次读取出 reg 属性中的所有数据。这四个函数的原型
如下：
int of_property_read_u8_array(const struct device_node *np,
const char *propname, 
u8 *out_values, 
size_t sz)
int of_property_read_u16_array(const struct device_node *np,
 const char *propname, 
 u16 *out_values, 
 size_t sz)
int of_property_read_u32_array(const struct device_node *np,
 const char *propname, 
 u32 *out_values,
 size_t sz)
int of_property_read_u64_array(const struct device_node *np,
 const char *propname, 
 u64 *out_values,
size_t sz)
函数参数和返回值含义如下：
np：设备节点。
proname： 要读取的属性名字。
out_value：读取到的数组值，分别为 u8、u16、u32 和 u64。
sz：要读取的数组元素数量。
返回值：0，读取成功，负值，读取失败，-EINVAL 表示属性不存在，-ENODATA 表示没
有要读取的数据，-EOVERFLOW 表示属性值列表太小。

有些属性只有一个整形值，这四个函数就是用于读取这种只有一个整形值的属性，分别用
于读取 u8、u16、u32 和 u64 类型属性值，函数原型如下：
int of_property_read_u8(const struct device_node *np, 
const char *propname,
u8 *out_value)
int of_property_read_u16(const struct device_node *np, 
const char *propname,
u16 *out_value)
int of_property_read_u32(const struct device_node *np, 
const char *propname,
u32 *out_value)
int of_property_read_u64(const struct device_node *np, 
const char *propname,
u64 *out_value)
函数参数和返回值含义如下：
np：设备节点。
proname： 要读取的属性名字。
out_value：读取到的数组值。
返回值：0，读取成功，负值，读取失败，-EINVAL 表示属性不存在，-ENODATA 表示没
有要读取的数据，-EOVERFLOW 表示属性值列表太小。

of_property_read_string 函数用于读取属性中字符串值，函数原型如下：
int of_property_read_string(struct device_node *np, 
 const char *propname,
 const char **out_string)
函数参数和返回值含义如下：
np：设备节点。
proname： 要读取的属性名字。
out_string：读取到的字符串值。
返回值：0，读取成功，负值，读取失败。

of_n_addr_cells 函数用于获取#address-cells 属性值，函数原型如下：
int of_n_addr_cells(struct device_node *np)
函数参数和返回值含义如下：
np：设备节点。
返回值：获取到的#address-cells 属性值。

of_size_cells 函数用于获取#size-cells 属性值，函数原型如下：
int of_n_size_cells(struct device_node *np)
函数参数和返回值含义如下：
np：设备节点。
返回值：获取到的#size-cells 属性值。

```

### 其他

```
of_device_is_compatible 函数用于查看节点的 compatible 属性是否有包含 compat 指定的字
符串，也就是检查设备节点的兼容性，函数原型如下：
int of_device_is_compatible(const struct device_node *device,
 const char *compat)
函数参数和返回值含义如下：
device：设备节点。
compat：要查看的字符串。
返回值：0，节点的 compatible 属性中不包含 compat 指定的字符串；正数，节点的 compatible
属性中包含 compat 指定的字符串。

of_get_address 函数用于获取地址相关属性，主要是“reg”或者“assigned-addresses”属性
值，函数原型如下：
const __be32 *of_get_address(struct device_node *dev, 
int index, 
u64 *size,
 unsigned int *flags)
函数参数和返回值含义如下：
dev：设备节点。
index：要读取的地址标号。
size：地址长度。
flags：参数，比如 IORESOURCE_IO、IORESOURCE_MEM 等
返回值：读取到的地址数据首地址，为 NULL 的话表示读取失败。


of_translate_address 函数负责将从设备树读取到的地址转换为物理地址，函数原型如下：
u64 of_translate_address(struct device_node *dev,
const __be32 *in_addr)
函数参数和返回值含义如下：
dev：设备节点。
in_addr：要转换的地址。
返回值：得到的物理地址，如果为 OF_BAD_ADDR 的话表示转换失败。

```

#### resource

```
IIC、SPI、GPIO 等这些外设都有对应的寄存器，这些寄存器其实就是一组内存空间，Linux
内核使用 resource 结构体来描述一段内存空间，“resource”翻译出来就是“资源”，因此用 resource
结构体描述的都是设备资源信息，resource 结构体定义在文件 include/linux/ioport.h 中，定义如
下：
示例代码 ******** resource 结构体
18 struct resource {
19 resource_size_t start;
20 resource_size_t end;
21 const char *name;
22 unsigned long flags;
23 struct resource *parent, *sibling, *child;
24 };


此函数看名字像是从设
备树里面提取资源值，但是本质上就是将 reg 属性值，然后将其转换为 resource 结构体类型，
函数原型如下所示
int of_address_to_resource(struct device_node *dev, 
 int index,
 struct resource *r)
函数参数和返回值含义如下：
dev：设备节点。
index：地址资源标号。
r：得到的 resource 类型的资源值。
返回值：0，成功；负值，失败。


of_iomap 函数用于直接内存映射，以前我们会通过 ioremap 函数来完成物理地址到虚拟地
址的映射，采用设备树以后就可以直接通过 of_iomap 函数来获取内存地址所对应的虚拟地址，
不需要使用 ioremap 函数了。当然了，你也可以使用 ioremap 函数来完成物理地址到虚拟地址
的内存映射，只是在采用设备树以后，大部分的驱动都使用 of_iomap 函数了。of_iomap 函数本
质上也是将 reg 属性中地址信息转换为虚拟地址，如果 reg 属性有多段的话，可以通过 index 参
数指定要完成内存映射的是哪一段，of_iomap 函数原型如下：
void __iomem *of_iomap(struct device_node *np, 
int index)
函数参数和返回值含义如下：
np：设备节点。
index：reg 属性中要完成内存映射的段，如果 reg 属性只有一段的话 index 就设置为 0。
返回值：经过内存映射后的虚拟内存首地址，如果为 NULL 的话表示内存映射失败。

```

## pinctl和gpio子系统

pinctrl 子系统主要工作内容
<1>获取设备树中 pin 信息，管理系统中所有的可以控制的 pin， 在系统初始化的时候， 枚举所有可以控制的 pin， 并标识这些 pin。
<2>根据获取到的 pin 信息来设置 pin 的复用功能，对于 SOC 而言， 其引脚除了配置成普通的 GPIO 之外，若干个引脚还可以组成一个 pin group， 形成特定的功能。
<3>根据获取到的 pin 信息来设置 pin 的电气特性，比如上/下拉、速度、驱动能力等。

当使用 pinctrl 子系统将引脚的复用设置为 GPIO，可以使用 GPIO 子系统来操作GPIO，Linux 内核提供了 pinctrl 子系统和 gpio 子系统用于 GPIO 驱动。

通过 GPIO 子系统功能要实现：

<1>引脚功能的配置（设置为 GPIO，GPIO 的方向， 输入输出模式，读取/设置 GPIO 的值）
<2>实现软硬件的分离（分离出硬件差异， 有厂商提供的底层支持； 软件分层。 驱动只需要调用接口 API 即可操作 GPIO）
<3>iommu 内存管理（直接调用宏即可操作 GPIO）
gpio 子系统的主要目的就是方便驱动开发者使用 gpio，驱动开发者在设备树中添加 gpio 相关信息，然后就可以在驱动程序中使用 gpio 子系统提供的 API函数来操作 GPIO， Linux 内核向驱动开发者屏蔽掉了 GPIO 的设置过程，极大的方便了驱动开发者使用 GPIO。

### gpio子系统api函数

```
gpio_request 函数用于申请一个 GPIO 管脚，在使用一个 GPIO 之前一定要使用 gpio_request
进行申请，函数原型如下：
int gpio_request(unsigned gpio, const char *label)
函数参数和返回值含义如下：
gpio：要申请的 gpio 标号，使用 of_get_named_gpio 函数从设备树获取指定 GPIO 属性信
息，此函数会返回这个 GPIO 的标号。
label：给 gpio 设置个名字。
返回值：0，申请成功；其他值，申请失败。

如果不使用某个 GPIO 了，那么就可以调用 gpio_free 函数进行释放。函数原型如下：
void gpio_free(unsigned gpio)
函数参数和返回值含义如下：
gpio：要释放的 gpio 标号。
返回值：无。

此函数用于设置某个 GPIO 为输入，函数原型如下所示：
int gpio_direction_input(unsigned gpio)
函数参数和返回值含义如下：
gpio：要设置为输入的 GPIO 标号。
返回值：0，设置成功；负值，设置失败。

此函数用于设置某个 GPIO 为输出，并且设置默认输出值，函数原型如下：
int gpio_direction_output(unsigned gpio, int value)
函数参数和返回值含义如下：
gpio：要设置为输出的 GPIO 标号。
value：GPIO 默认输出值。
返回值：0，设置成功；负值，设置失败。

此函数用于获取某个 GPIO 的值(0 或 1)，此函数是个宏，定义所示：
#define gpio_get_value __gpio_get_value
int __gpio_get_value(unsigned gpio)
函数参数和返回值含义如下：
gpio：要获取的 GPIO 标号。
返回值：非负值，得到的 GPIO 值；负值，获取失败。

此函数用于设置某个 GPIO 的值，此函数是个宏，定义如下
#define gpio_set_value __gpio_set_value
void __gpio_set_value(unsigned gpio, int value)
函数参数和返回值含义如下：
gpio：要设置的 GPIO 标号。
value：要设置的值。
返回值：无
关于 gpio 子系统常用的 API 函数就讲这些，这些是我们用的最多的。
```

### gpio系统相关的OF函数

```
of_gpio_named_count 函数用于获取设备树某个属性里面定义了几个 GPIO 信息，要注意的
是空的 GPIO 信息也会被统计到，比如：
gpios = <0
 &gpio1 1 2
 0
 &gpio2 3 4>;
上述代码的“gpios”节点一共定义了 4 个 GPIO，但是有 2 个是空的，没有实际的含义。
通过 of_gpio_named_count 函数统计出来的 GPIO 数量就是 4 个，此函数原型如下：
int of_gpio_named_count(struct device_node *np, const char *propname)
函数参数和返回值含义如下：
np：设备节点。
propname：要统计的 GPIO 属性。
返回值：正值，统计到的 GPIO 数量；负值，失败。

和 of_gpio_named_count 函数一样，但是不同的地方在于，此函数统计的是“gpios”这个属
性的 GPIO 数量，而 of_gpio_named_count 函数可以统计任意属性的 GPIO 信息，函数原型如下
所示：
int of_gpio_count(struct device_node *np)
函数参数和返回值含义如下：
np：设备节点。
返回值：正值，统计到的 GPIO 数量；负值，失败。

此函数获取 GPIO 编号，因为 Linux 内核中关于 GPIO 的 API 函数都要使用 GPIO 编号，
此函数会将设备树中类似<&gpio5 7 GPIO_ACTIVE_LOW>的属性信息转换为对应的 GPIO 编
号，此函数在驱动中使用很频繁！函数原型如下：
int of_get_named_gpio(struct device_node *np,
 const char *propname, 
int index)
函数参数和返回值含义如下：
np：设备节点。
propname：包含要获取 GPIO 信息的属性名。
index：GPIO 索引，因为一个属性里面可能包含多个 GPIO，此参数指定要获取哪个 GPIO
的编号，如果只有一个 GPIO 信息的话此参数为 0。
返回值：正值，获取到的 GPIO 编号；负值，失败。

```

## linux并发与竞争

### 原子整形操作api

```
ATOMIC_INIT(int i) 定义原子变量的时候对其初始化。
int atomic_read(atomic_t *v) 读取 v 的值，并且返回。
void atomic_set(atomic_t *v, int i) 向 v 写入 i 值。
void atomic_add(int i, atomic_t *v) 给 v 加上 i 值。
void atomic_sub(int i, atomic_t *v) 从 v 减去 i 值。
void atomic_inc(atomic_t *v) 给 v 加 1，也就是自增。
void atomic_dec(atomic_t *v) 从 v 减 1，也就是自减
int atomic_dec_return(atomic_t *v) 从 v 减 1，并且返回 v 的值。
int atomic_inc_return(atomic_t *v) 给 v 加 1，并且返回 v 的值。
int atomic_sub_and_test(int i, atomic_t *v) 从 v 减 i，如果结果为 0 就返回真，否则返回假
int atomic_dec_and_test(atomic_t *v) 从 v 减 1，如果结果为 0 就返回真，否则返回假
int atomic_inc_and_test(atomic_t *v) 给 v 加 1，如果结果为 0 就返回真，否则返回假
int atomic_add_negative(int i, atomic_t *v) 给 v 加 i，如果结果为负就返回真，否则返回假
```

### 原子位操作api

```
void set_bit(int nr, void *p) 将 p 地址的第 nr 位置 1。
void clear_bit(int nr,void *p) 将 p 地址的第 nr 位清零。
void change_bit(int nr, void *p) 将 p 地址的第 nr 位进行翻转。
int test_bit(int nr, void *p) 获取 p 地址的第 nr 位的值。
int test_and_set_bit(int nr, void *p) 将 p 地址的第 nr 位置 1，并且返回 nr 位原来的值。
int test_and_clear_bit(int nr, void *p) 将 p 地址的第 nr 位清零，并且返回 nr 位原来的值。
int test_and_change_bit(int nr, void *p) 将 p 地址的第 nr 位翻转，并且返回 nr 位原来的值。
```

### 自旋锁

```
64 typedef struct spinlock {
65 union {
66 struct raw_spinlock rlock;
67
68 #ifdef CONFIG_DEBUG_LOCK_ALLOC
69 # define LOCK_PADSIZE (offsetof(struct raw_spinlock, dep_map))
70 struct {
71 u8 __padding[LOCK_PADSIZE];
72 struct lockdep_map dep_map;
73 };
74 #endif
75 };
76 } spinlock_t;
```

#### 自旋锁api

```

DEFINE_SPINLOCK(spinlock_t lock) 定义并初始化一个自选变量。
int spin_lock_init(spinlock_t *lock) 初始化自旋锁。
void spin_lock(spinlock_t *lock) 获取指定的自旋锁，也叫做加锁。
void spin_unlock(spinlock_t *lock) 释放指定的自旋锁。
int spin_trylock(spinlock_t *lock) 尝试获取指定的自旋锁，如果没有获取到就返回 0
int spin_is_locked(spinlock_t *lock)
检查指定的自旋锁是否被获取，如果没有被获取就
返回非 0，否则返回 0。
```

中断打断线程容易发生死锁，需要在获取锁之前关闭本地中断，api如下

```
void spin_lock_irq(spinlock_t *lock) 禁止本地中断，并获取自旋锁。
void spin_unlock_irq(spinlock_t *lock) 激活本地中断，并释放自旋锁。
void spin_lock_irqsave(spinlock_t *lock, 
unsigned long flags)
保存中断状态，禁止本地中断，并获取自旋锁。
void spin_unlock_irqrestore(spinlock_t 
*lock, unsigned long flags)
将中断状态恢复到以前的状态，并且激活本地中断，
释放自旋锁。
```

不过并不推荐用spin_lock_irq，推荐使用会保存状态的。示范如下

```
1 DEFINE_SPINLOCK(lock) /* 定义并初始化一个锁 */
2 
3 /* 线程 A */
4 void functionA (){
5 unsigned long flags; /* 中断状态 */
6 spin_lock_irqsave(&lock, flags) /* 获取锁 */
7 /* 临界区 */
8 spin_unlock_irqrestore(&lock, flags) /* 释放锁 */
9 }
10
11 /* 中断服务函数 */
12 void irq() {
13 spin_lock(&lock) /* 获取锁 */
14 /* 临界区 */
15 spin_unlock(&lock) /* 释放锁 */
16 }
```

#### 下半部自旋锁api

```
void spin_lock_bh(spinlock_t *lock) 关闭下半部，并获取自旋锁。
void spin_unlock_bh(spinlock_t *lock) 打开下半部，并释放自旋锁。
```

### 信号量

```
struct semaphore {
 raw_spinlock_t lock;
 unsigned int count;
 struct list_head wait_list;
};
```

#### 信号量api

```
DEFINE_SEAMPHORE(name) 定义一个信号量，并且设置信号量的值为 1。
void sema_init(struct semaphore *sem, int val) 初始化信号量 sem，设置信号量值为 val。
void down(struct semaphore *sem)
获取信号量，因为会导致休眠，因此不能在中
断中使用。
int down_trylock(struct semaphore *sem);
尝试获取信号量，如果能获取到信号量就获
取，并且返回 0。如果不能就返回非 0，并且
不会进入休眠。
int down_interruptible(struct semaphore *sem)
获取信号量，和 down 类似，只是使用 down 进
入休眠状态的线程不能被信号打断。而使用此
函数进入休眠以后是可以被信号打断的。
void up(struct semaphore *sem) 释放信号量

struct semaphore sem; /* 定义信号量 */
sema_init(&sem, 1); /* 初始化信号量 */
down(&sem); /* 申请信号量 */
/* 临界区 */
up(&sem); /* 释放信号量 */
```

### 互斥体

```
struct mutex {
 /* 1: unlocked, 0: locked, negative: locked, possible waiters */
 atomic_t count;
 spinlock_t wait_lock;
};
```

#### 互斥体 API

```
DEFINE_MUTEX(name) 定义并初始化一个 mutex 变量。
void mutex_init(mutex *lock) 初始化 mutex。
void mutex_lock(struct mutex *lock)
获取 mutex，也就是给 mutex 上锁。如果获
取不到就进休眠。
void mutex_unlock(struct mutex *lock) 释放 mutex，也就给 mutex 解锁。
int mutex_trylock(struct mutex *lock)
尝试获取 mutex，如果成功就返回 1，如果失
败就返回 0。
int mutex_is_locked(struct mutex *lock)
判断 mutex 是否被获取，如果是的话就返回
1，否则返回 0。
int mutex_lock_interruptible(struct mutex *lock)
使用此函数获取信号量失败进入休眠以后可
以被信号打断。

1 struct mutex lock; /* 定义一个互斥体 */
2 mutex_init(&lock); /* 初始化互斥体 */
3
4 mutex_lock(&lock); /* 上锁 */
5 /* 临界区 */
6 mutex_unlock(&lock); /* 解锁 */
```

## 内核定时器

### 定时器api

```
init_timer 函数负责初始化 timer_list 类型变量，当我们定义了一个 timer_list 变量以后一定
要先用 init_timer 初始化一下。init_timer 函数原型如下：
void init_timer(struct timer_list *timer)
函数参数和返回值含义如下：
timer：要初始化定时器。
返回值：没有返回值。

add_timer 函数用于向 Linux 内核注册定时器，使用 add_timer 函数向内核注册定时器以后，
定时器就会开始运行，函数原型如下：
void add_timer(struct timer_list *timer)
函数参数和返回值含义如下：
timer：要注册的定时器。
返回值：没有返回值。

del_timer 函数用于删除一个定时器，不管定时器有没有被激活，都可以使用此函数删除。
在多处理器系统上，定时器可能会在其他的处理器上运行，因此在调用 del_timer 函数删除定时
器之前要先等待其他处理器的定时处理器函数退出。del_timer 函数原型如下：
int del_timer(struct timer_list * timer)
函数参数和返回值含义如下：
timer：要删除的定时器。
返回值：0，定时器还没被激活；1，定时器已经激活。

del_timer_sync 函数是 del_timer 函数的同步版，会等待其他处理器使用完定时器再删除，
del_timer_sync 不能使用在中断上下文中。del_timer_sync 函数原型如下所示：
int del_timer_sync(struct timer_list *timer)
函数参数和返回值含义如下：
timer：要删除的定时器。
返回值：0，定时器还没被激活；1，定时器已经激活。

mod_timer 函数用于修改定时值，如果定时器还没有激活的话，mod_timer 函数会激活定时
器！函数原型如下：
int mod_timer(struct timer_list *timer, unsigned long expires)
函数参数和返回值含义如下：
timer：要修改超时时间(定时值)的定时器。
expires：修改后的超时时间。
返回值：0，调用 mod_timer 函数前定时器未被激活；1，调用 mod_timer 函数前定时器已
被激活。

内核定时器一般的使用流程如下所示：
1 struct timer_list timer; /* 定义定时器 */
2 
3 /* 定时器回调函数 */
4 void function(unsigned long arg)
5 { 
6 /* 
7 * 定时器处理代码
8 */
9 
10 /* 如果需要定时器周期性运行的话就使用 mod_timer
11 * 函数重新设置超时值并且启动定时器。
12 */
13 mod_timer(&dev->timertest, jiffies + msecs_to_jiffies(2000));
14 }
15
16 /* 初始化函数 */
17 void init(void) 
18 {
19 init_timer(&timer); /* 初始化定时器 */
20
21 timer.function = function; /* 设置定时处理函数 */
22 timer.expires=jffies + msecs_to_jiffies(2000);/* 超时时间 2 秒 */
23 timer.data = (unsigned long)&dev; /* 将设备结构体作为参数 */
24 
25 add_timer(&timer); /* 启动定时器 */
26 }
27
28 /* 退出函数 */
29 void exit(void)
30 {
31 del_timer(&timer); /* 删除定时器 */
32 /* 或者使用 */
33 del_timer_sync(&timer);
34 }
```

## 内核中断

### 中断api

```
在 Linux 内核中要想使用某个中断是需要申请的，request_irq 函数用于申请中断，request_irq
函数可能会导致睡眠，因此不能在中断上下文或者其他禁止睡眠的代码段中使用 request_irq 函
数。request_irq 函数会激活(使能)中断，所以不需要我们手动去使能中断，request_irq 函数原型
如下：
int request_irq(unsigned int irq, 
irq_handler_t handler, 
unsigned long flags,
 const char *name, 
void *dev)
函数参数和返回值含义如下：
irq：要申请中断的中断号。
handler：中断处理函数，当中断发生以后就会执行此中断处理函数。
flags：中断标志，可以在文件 include/linux/interrupt.h 里面查看所有的中断标志

IRQF_SHARED
多个设备共享一个中断线，共享的所有中断都必须指定此标志。
如果使用共享中断的话，request_irq 函数的 dev 参数就是唯一
区分他们的标志。
IRQF_ONESHOT 单次中断，中断执行一次就结束。
IRQF_TRIGGER_NONE 无触发。
IRQF_TRIGGER_RISING 上升沿触发。
IRQF_TRIGGER_FALLING 下降沿触发。
IRQF_TRIGGER_HIGH 高电平触发。
IRQF_TRIGGER_LOW 低电平触发。

name：中断名字，设置以后可以在/proc/interrupts 文件中看到对应的中断名字。
dev：如果将 flags 设置为 IRQF_SHARED 的话，dev 用来区分不同的中断，一般情况下将
dev 设置为设备结构体，dev 会传递给中断处理函数 irq_handler_t 的第二个参数。
返回值：0 中断申请成功，其他负值 中断申请失败，如果返回-EBUSY 的话表示中断已经
被申请了。


使用中断的时候需要通过 request_irq 函数申请，使用完成以后就要通过 free_irq 函数释放
掉相应的中断。如果中断不是共享的，那么 free_irq 会删除中断处理函数并且禁止中断。free_irq
函数原型如下所示：
void free_irq(unsigned int irq, 
void *dev)
函数参数和返回值含义如下：
irq：要释放的中断。
dev：如果中断设置为共享(IRQF_SHARED)的话，此参数用来区分具体的中断。共享中断
只有在释放最后中断处理函数的时候才会被禁止掉。
返回值：无。


使用 request_irq 函数申请中断的时候需要设置中断处理函数，中断处理函数格式如下所示：
irqreturn_t (*irq_handler_t) (int, void *)
第一个参数是要中断处理函数要相应的中断号。第二个参数是一个指向 void 的指针，也就
是个通用指针，需要与 request_irq 函数的 dev 参数保持一致。用于区分共享中断的不同设备，
dev 也可以指向设备数据结构。中断处理函数的返回值为 irqreturn_t 类型，irqreturn_t 类型定义
如下所示：
示例代码 51.1.1.1 irqreturn_t 结构
10 enum irqreturn {
11 IRQ_NONE = (0 << 0),
12 IRQ_HANDLED = (1 << 0),
13 IRQ_WAKE_THREAD = (1 << 1),
14 };
15
16 typedef enum irqreturn irqreturn_t;
可以看出 irqreturn_t 是个枚举类型，一共有三种返回值。一般中断服务函数返回值使用如
下形式：
return IRQ_RETVAL(IRQ_HANDLED)


常用的中断使用和禁止函数如下所示：
void enable_irq(unsigned int irq)
void disable_irq(unsigned int irq)
enable_irq 和 disable_irq 用于使能和禁止指定的中断，irq 就是要禁止的中断号。disable_irq
函数要等到当前正在执行的中断处理函数执行完才返回，因此使用者需要保证不会产生新的中
断，并且确保所有已经开始执行的中断处理程序已经全部退出。在这种情况下，可以使用另外
一个中断禁止函数：
void disable_irq_nosync(unsigned int irq)
disable_irq_nosync 函数调用以后立即返回，不会等待当前中断处理程序执行完毕。上面三
个函数都是使能或者禁止某一个中断，有时候我们需要关闭当前处理器的整个中断系统，也就
是在学习 STM32 的时候常说的关闭全局中断，这个时候可以使用如下两个函数：
local_irq_enable()
local_irq_disable()
local_irq_enable 用于使能当前处理器中断系统，local_irq_disable 用于禁止当前处理器中断
系统。
```

### 软中断

```
示例代码 51.1.2.1 softirq_action 结构体
433 struct softirq_action
434 {
435 void (*action)(struct softirq_action *);
436 };
在 kernel/softirq.c 文件中一共定义了 10 个软中断，如下所示：
示例代码 51.1.2.2 softirq_vec 数组
static struct softirq_action softirq_vec[NR_SOFTIRQS];
NR_SOFTIRQS 是枚举类型，定义在文件 include/linux/interrupt.h 中，定义如下：
示例代码 51.1.2.3 softirq_vec 数组
enum
{
 HI_SOFTIRQ=0, /* 高优先级软中断 */
 TIMER_SOFTIRQ, /* 定时器软中断 */
 NET_TX_SOFTIRQ, /* 网络数据发送软中断 */
 NET_RX_SOFTIRQ, /* 网络数据接收软中断 */
 BLOCK_SOFTIRQ, 
 BLOCK_IOPOLL_SOFTIRQ, 
 TASKLET_SOFTIRQ, /* tasklet 软中断 */
 SCHED_SOFTIRQ, /* 调度软中断 */
 HRTIMER_SOFTIRQ, /* 高精度定时器软中断 */
 RCU_SOFTIRQ, /* RCU 软中断 */
 NR_SOFTIRQS
};
```

#### 软中断api

```
要使用软中断，必须先使用 open_softirq 函数注
册对应的软中断处理函数，open_softirq 函数原型如下：
void open_softirq(int nr, void (*action)(struct softirq_action *))
函数参数和返回值含义如下：
nr：要开启的软中断，在示例代码 51.1.2.3 中选择一个。
action：软中断对应的处理函数。
返回值：没有返回值。
注册好软中断以后需要通过 raise_softirq 函数触发，raise_softirq 函数原型如下：
void raise_softirq(unsigned int nr)
函数参数和返回值含义如下：
nr：要触发的软中断，在示例代码 51.1.2.3 中选择一个。
返回值：没有返回值。
```

### tasklet

```
484 struct tasklet_struct
485 {
486 struct tasklet_struct *next; /* 下一个 tasklet */
487 unsigned long state; /* tasklet 状态 */
488 atomic_t count; /* 计数器，记录对 tasklet 的引用数 */
489 void (*func)(unsigned long); /* tasklet 执行的函数 */
490 unsigned long data; /* 函数 func 的参数 */
491 };
```

#### tasklet api

```
如果要使用 tasklet，必须先定义一个 tasklet，然后使用 tasklet_init 函数初始化 tasklet，
taskled_init 函数原型如下：
void tasklet_init(struct tasklet_struct *t,
void (*func)(unsigned long), 
unsigned long data);
函数参数和返回值含义如下：
t：要初始化的 tasklet
func：tasklet 的处理函数。
data：要传递给 func 函数的参数
返回值：没有返回值。
也可以使用宏 DECLARE_TASKLET 来一次性完成 tasklet 的定义和初始化，
DECLARE_TASKLET 定义在 include/linux/interrupt.h 文件中，定义如下:
DECLARE_TASKLET(name, func, data)
其中 name 为要定义的 tasklet 名字，这个名字就是一个 tasklet_struct 类型的时候变量，func
就是 tasklet 的处理函数，data 是传递给 func 函数的参数。
在上半部，也就是中断处理函数中调用 tasklet_schedule 函数就能使 tasklet 在合适的时间运
行，tasklet_schedule 函数原型如下：
void tasklet_schedule(struct tasklet_struct *t)
函数参数和返回值含义如下：
t：要调度的 tasklet，也就是 DECLARE_TASKLET 宏里面的 name。
返回值：没有返回值。
```

### 工作队列

工作队列是另外一种下半部执行方式，工作队列在进程上下文执行，工作队列将要推后的
工作交给一个内核线程去执行，因为工作队列工作在进程上下文，因此工作队列允许睡眠或重
新调度。因此如果你要推后的工作可以睡眠那么就可以选择工作队列，否则的话就只能选择软
中断或 tasklet。

```
简单创建工作很简单，直接定义一个 work_struct 结构体
变量即可，然后使用 INIT_WORK 宏来初始化工作，INIT_WORK 宏定义如下：
#define INIT_WORK(_work, _func)
_work 表示要初始化的工作，_func 是工作对应的处理函数。
也可以使用 DECLARE_WORK 宏一次性完成工作的创建和初始化，宏定义如下：
#define DECLARE_WORK(n, f)
n 表示定义的工作(work_struct)，f 表示工作对应的处理函数。
和 tasklet 一样，工作也是需要调度才能运行的，工作的调度函数为 schedule_work，函数原
型如下所示：
bool schedule_work(struct work_struct *work)
函数参数和返回值含义如下：
work：要调度的工作。
返回值：0 成功，其他值 失败。
关于工作队列的参考使用示例如下所示：
示例代码 51.1.2.11 工作队列使用示例
/* 定义工作(work) */
struct work_struct testwork;
/* work 处理函数 */
void testwork_func_t(struct work_struct *work);
{
 /* work 具体处理内容 */
}
/* 中断处理函数 */
irqreturn_t test_handler(int irq, void *dev_id)
{
 ......
 /* 调度 work */
 schedule_work(&testwork);
 ......
}
/* 驱动入口函数 */
static int __init xxxx_init(void)
{
 ......
 /* 初始化 work */
 INIT_WORK(&testwork, testwork_func_t);
 /* 注册中断处理函数 */
 request_irq(xxx_irq, test_handler, 0, "xxx", &xxx_dev);
 ......
}
```

### 获取中断号

```
可以通过 irq_of_parse_and_map 函数从 interupts 属性中提取到对应的设备号，函数原型如下：
unsigned int irq_of_parse_and_map(struct device_node *dev,
 int index)
函数参数和返回值含义如下：
dev：设备节点。
index：索引号，interrupts 属性可能包含多条中断信息，通过 index 指定要获取的信息。
返回值：中断号。
如果使用 GPIO 的话，可以使用 gpio_to_irq 函数来获取 gpio 对应的中断号，函数原型如
下：
int gpio_to_irq(unsigned int gpio)
函数参数和返回值含义如下：
gpio：要获取的 GPIO 编号。
返回值：GPIO 对应的中断号。
```
