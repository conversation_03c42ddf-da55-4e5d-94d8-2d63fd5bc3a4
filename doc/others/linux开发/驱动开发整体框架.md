# 模块加载/卸载函数注册

首先是MODULE_INIT()和MODULE_EXIT()进行模块加载函数和模块卸载函数的注册

# 字符设备注册

## 静态分配

如果是字符设备那么就需要用函数注册字符设备，分别是：
register_chrdev()和unregister_chrdev()
这两个函数一般都是在入口函数和出口函数中进行使用

## 动态分配

使用alloc_chrdev_region()，register_chrdev_region()进行申请
后面的可以手动指定主设备号和次设备号
alloc_xx使用的是结构体的地址，所以要获取设备号可以用MAJOR和MINOR对申请好的cdev结构体进行分析
unregister_chrdev_region()进行注销
动态分配需要用到一个字符设备结构cdev，这个结构体主要有两个重要变量为设备号和设备文件操作函数集合
cdev_init()函数就是将操作函数集合指针放进去
这一切都做完后还有cdev_add和cdev_del函数想Linux系统添加和删除设备

# 自动创建设备节点
使用modprobe加载驱动后还需要mknod手动创建设备节点
Linux下使用udev进行设插拔设备文件的创建和删除，嵌入式使用的是mdev
这一步一般在字符设备添加之后
想要自动创建设备需要先创建一个class用来自动对驱动文件进行分类
函数是class_create()和class_destory()
然后使用class放入device_create()函数中进行自动创建
随后使用device_destory()进行设备的删除

# 内核操作函数集

一般需要定义一个file_operations结构体变量，其中比较重要的就是open,release这些函数
其中.owner一般赋值为THIS_MODULE

# 内存映射

使用ioremap()和iounmap()这两个宏将物理内存映射到虚拟内存上

# platform
将设备、总线、驱动分层的思想就是platform架构
linux使用bus_type结构体表示总线
里面主要包含总线名字，总线属性，设备属性，驱动属性
platform_bus_type就是platform的bus_type的一个具体实例

## match 

match函数很重要，platform的匹配方式：
1.OF类型的匹配 ，驱动文件中会有一个of_match_table()的成员变量，匹配成功，就执行probe函数
2. ACPI匹配方式
3. id_table匹配，platform_driver有一个id_table结构体
4. 比较name字段

## platform_driver结构体
主要成员：
probe函数，匹配成功就会触发这个函数
device_driver，这个相当于基类，包含了这个结构体，相当于使用复合结构
这个里面主要包含：
of_match_table,同样是数组，每个匹配项都为of_device_id结构体类型
id_table表，每个元素的类型为platform_device_id

当定义好一个platform结构体之后需要向linux注册
platform_driver_register()
platform_driver_unregister()

```
/* 设备结构体 */
1 struct xxx_dev{
2 struct cdev cdev;
3 /* 设备结构体其他具体内容 */
4 };
5 
6 struct xxx_dev xxxdev; /* 定义个设备结构体变量 */
7 
8 static int xxx_open(struct inode *inode, struct file *filp)
9 { 
10 /* 函数具体内容 */
11 return 0;
12 }
13
14 static ssize_t xxx_write(struct file *filp, const char __user *buf,
size_t cnt, loff_t *offt)
15 {
16 /* 函数具体内容 */
17 return 0;
18 }
19
20 /*
21 * 字符设备驱动操作集
22 */
23 static struct file_operations xxx_fops = {
24 .owner = THIS_MODULE,
25 .open = xxx_open,
26 .write = xxx_write,
27 };
28
29 /*
30 * platform 驱动的 probe 函数
31 * 驱动与设备匹配成功以后此函数就会执行
32 */
33 static int xxx_probe(struct platform_device *dev)
34 { 
35 ......
36 cdev_init(&xxxdev.cdev, &xxx_fops); /* 注册字符设备驱动 */
37 /* 函数具体内容 */
38 return 0;
39 }
40
41 static int xxx_remove(struct platform_device *dev)
42 {
43 ......
44 cdev_del(&xxxdev.cdev);/* 删除 cdev */
45 /* 函数具体内容 */
46 return 0;
47 }
48
49 /* 匹配列表 */
50 static const struct of_device_id xxx_of_match[] = {
51 { .compatible = "xxx-gpio" },
52 { /* Sentinel */ }
53 };
54
55 /* 
56 * platform 平台驱动结构体
57 */
58 static struct platform_driver xxx_driver = {
59 .driver = {
60 .name = "xxx",
61 .of_match_table = xxx_of_match,
62 },
63 .probe = xxx_probe,
64 .remove = xxx_remove,
65 };
66 
67 /* 驱动模块加载 */
68 static int __init xxxdriver_init(void)
69 {
70 return platform_driver_register(&xxx_driver);
71 }
72
73 /* 驱动模块卸载 */
74 static void __exit xxxdriver_exit(void)
75 {
76 platform_driver_unregister(&xxx_driver);
77 }
78
79 module_init(xxxdriver_init);
80 module_exit(xxxdriver_exit);
81 MODULE_LICENSE("GPL");
82 MODULE_AUTHOR("zuozhongkai");
```

## platform_device结构体
如果支持设备树，那么就不应该用这个了，应该直接用设备树
主要成员：
name 设备名字
num_resources资源数量
resource表示资源
主要架构：
```
1 /* 寄存器地址定义*/
2 #define PERIPH1_REGISTER_BASE (0X20000000) /* 外设 1 寄存器首地址 */ 
3 #define PERIPH2_REGISTER_BASE (0X020E0068) /* 外设 2 寄存器首地址 */
4 #define REGISTER_LENGTH 4
5 
6 /* 资源 */
7 static struct resource xxx_resources[] = {
8 [0] = {
9 .start = PERIPH1_REGISTER_BASE,
10 .end = (PERIPH1_REGISTER_BASE + REGISTER_LENGTH - 1),
11 .flags = IORESOURCE_MEM,
12 }, 
13 [1] = {
14 .start = PERIPH2_REGISTER_BASE,
15 .end = (PERIPH2_REGISTER_BASE + REGISTER_LENGTH - 1),
16 .flags = IORESOURCE_MEM,
17 },
18 };
19
20 /* platform 设备结构体 */
21 static struct platform_device xxxdevice = {
22 .name = "xxx-gpio",
23 .id = -1,
24 .num_resources = ARRAY_SIZE(xxx_resources),
25 .resource = xxx_resources,
26 };
27 
28 /* 设备模块加载 */
29 static int __init xxxdevice_init(void)
30 {
31 return platform_device_register(&xxxdevice);
32 }
33
34 /* 设备模块注销 */
35 static void __exit xxx_resourcesdevice_exit(void)
36 {
37 platform_device_unregister(&xxxdevice);
38 }
39
40 module_init(xxxdevice_init);
41 module_exit(xxxdevice_exit);
42 MODULE_LICENSE("GPL");
43 MODULE_AUTHOR("zuozhongkai");

```

## MISC设备驱动
misc可以让我们简化字符设备驱动的编写
主要使用miscdevice结构体，主要包含：
子设备号，设备名字，设备操作集
传统设备创建过程：
```
alloc_chrdev_region()
cdev_init()
cdev_add()
class_create()
device_create()
```
可以直接用misc_register()
注销可以直接用misc_deregister()
即直接替代如下：
```
cdev_del()
unregister_chrdev_region()
device_destroy()
class_destory()
```

## input子系统
分为驱动层、核心层、事件层
input_init()会注册一个类，并注册主设备号13
注册input_dev，主要包含：
各种坐标位图等等

编写设备驱动的时候需要先申请一个结构体变量，使用input_allocate_device()
input_free_device()
初始化完成后也需要注册
input_register_device()
input_unregister_device()
```
1 struct input_dev *inputdev; /* input 结构体变量 */
2 
3 /* 驱动入口函数 */
4 static int __init xxx_init(void)
5 {
6 ......
7 inputdev = input_allocate_device(); /* 申请 input_dev */
8 inputdev->name = "test_inputdev"; /* 设置 input_dev 名字 */
9 
10 /*********第一种设置事件和事件值的方法***********/
11 __set_bit(EV_KEY, inputdev->evbit); /* 设置产生按键事件 */
12 __set_bit(EV_REP, inputdev->evbit); /* 重复事件 */
13 __set_bit(KEY_0, inputdev->keybit); /*设置产生哪些按键值 */
14 /************************************************/
15 
16 /*********第二种设置事件和事件值的方法***********/
17 keyinputdev.inputdev->evbit[0] = BIT_MASK(EV_KEY) |
BIT_MASK(EV_REP);
18 keyinputdev.inputdev->keybit[BIT_WORD(KEY_0)] |=
BIT_MASK(KEY_0);
19 /************************************************/
20
21 /*********第三种设置事件和事件值的方法***********/
22 keyinputdev.inputdev->evbit[0] = BIT_MASK(EV_KEY) |
BIT_MASK(EV_REP);
23 input_set_capability(keyinputdev.inputdev, EV_KEY, KEY_0);
24 /************************************************/
25 
26 /* 注册 input_dev */
27 input_register_device(inputdev);
28 ......
29 return 0;
30 }
31
32 /* 驱动出口函数 */
33 static void __exit xxx_exit(void)
34 {
35 input_unregister_device(inputdev); /* 注销 input_dev */
36 input_free_device(inputdev); /* 删除 input_dev */
37 }
```
### 上报输入功能
基本上都用到了input_event()

# I2C驱动框架

主要分为两个部分：
1. I2C总线驱动
SOC的I2C控制器驱动，也叫做i2C适配器驱动。
2. I2C设备驱动

## I2C抽象总线
platform是一条虚拟出来的一条总线，I2C直接使用I2C总线就可以了
Linux将总线驱动抽象成i2c_adapter
主要包含：
总线访问算法    i2c_algorithm
这个算法主要包含：
master_xfer 传输函数
smbus_xfer  SMBUS总线的传输函数
functionality   

设置好这两个结构体之后就是注册
i2c_add_adapter()
i2c_add_numbered_adapter()

i2c_del_adapter()

## I2C设备驱动

主要使用i2c_client和i2c_driver结构体
i2c_client主要包含：
芯片地址，名字，对应的I2C适配器，设备结构体，中断
i2c_driver主要包含：
probe函数
device_driver驱动结构体，使用设备树，需要设置of_match_table成员变量
id_table传统的，未使用设备树的设备匹配
构建完成后向Linux注册：
i2c_register_driver()
i2c_add_driver  这是一个宏，只是做了一个简单的封装
i2c_del_driver()    

# SPI驱动框架

## SPI主机驱动
spi_master结构体用来表示SPI主机驱动
主要包含：transfer函数、transfer_onr_message函数
spi_alloc_master()
spi_master_put()
分别用于申请和释放spi_master
之后就需要注册和注销
spi_register_master()
spi_unregister_master()

## SPI设备驱动
使用spi_driver结构体，主要包含：
probe函数等，当然，也包括device_driver
初始化之后需要注册：
spi_register_driver()
spi_unregister_driver()



# 其他信息

一个硬件设备一般都有一些属性，比如主设备号，类，设备，开关状态等等，这些都可以写到私有结构体中
在使用open()时，一般会传入一个file结构体，其中有一个private_data变量，可以将设备私有结构体赋值给这个变量

一般也要添加LICENSE和作者信息
MODULE_LICENSE()
MODULE_AUTHOR()

# 设备树编写
设备树文件有且只有一个根节点
根节点下啊有众多节点，节点则是由一堆的属性组成

1. compatible 属性
兼容性属性，是一个字符串列表
一般驱动程序文件都会有一个OF匹配表
比如:

```
static const struct of_device_id xxx[]={};

MODULE_DEVICE_TABLE(of,xxx);

static struct platform_driver xxx={
    .driver={
        ...
        .of_match_table=xxx,
    },
    ...
}
```

2. model属性
描述设备模块信息

3. status属性
okay 可操作 disabled 不可操作，未来可操作
fail 不可操作 fail-sss 后面是检测到的错误

4. #address-cells和#size-cells
两个都是用来描述子节点的地址信息
address-cells决定了reg中地址信息的字长
size-cells决定了长度信息所占的字长
一般而言`reg<address1 length1 address2 length2>`

5. reg
一般都是(address,length)对

6. ranges
是一个地址映射/转换表
ranges<子空间起始 父空间起始 长度>

7. name
已弃用，用于记录节点名字

8. device_type
用于描述FCode，已弃用

9. 特殊节点
aliases 用于别名
chosen 一般用于uboot传递数据用

## 设备树常用的OF函数
Linux使用device_node结构体来描述一个节点

### 查找结点的函数

of_find_node_by_name()
of_find_node_by_type()
of_find_compatible_node()
of_find_mathching_node_and_match()
of_find_node_by_path()

### 查找父子节点的OF函数
of_get_parent()
of_get_next_child()

### 提取属性值的OF函数
of_find_property()
of_property_count_elems_of_size()
...

### 其他常用的OF函数
of_device_is_compatible()查看兼容性
of_get_address()获取地址相关属性
of_translate_address()设备树地址->物理地址
of_address_to_resource()将外设翻译为地址资源
of_iomap()用于直接内存映射参数为：设备节点device_node，reg中的属性段index


一般使用流程：
of_find_node_by_path()获取设备节点
of_find_property()获取compatible属性内容
of_property_read_string()获取status属性内容
of_property_read_u32_array()获取reg属性内容
使用of_iomap()对地址进行一起的映射

## pinctrl子系统驱动
一般会在设备树里面创建一个节点来描述PIN的配置信息
一般叫做iomuxc节点
iomucx中的子节点一般由一个宏定义和一个16进制数
宏定义用于设置复用功能，16进制数用于设置电气特性

## gpio子系统驱动
例如在usdhc1节点中的
```
&usdhc1{
    ...
    cd-gpios=<&gpio1 19 GPIO_ACTIVE_LOW>;
    ...
}
```
gpio1定义在dtsi文件中

### gpio子系统api函数
gpio_request()申请一个管脚
gpio_free()
gpio_diretion_input()
gpio_diretion_output()
gpio_get_value()
gpio_set_value()

### gpio相关的OG函数
of_gpio_named_count()
of_gpio_count()
of_get_named_gpio()

一般流程：
init中使用
of_find_node_by_path()获取设备节点
of_get_named_gpio()获取GPIO编号
gpio_direction_output()使用GPIO编号设置输出模式
alloc_chrdev_region()创建设备号
cdev_init()对cdev添加操作函数集合
cdev_add()向linux添加cdev
class_create()
device_create()

exit中使用
cdev_del()删除cdev
unregister_chrdev_region()注销设备号
device_destroy()
class_create()

## 中断信息节点
intc是中断控制器节点
### #interrupt—cells
表示中断控制器下设备的cells大小
arm有三个cells，第一个是中断类型，SPI和PPS，第二个是中断号，第三个是标志（怎么触发中断）

### 获取中断号
irq_of_parse_and_map()提取中断号，需要用到device_node
gpio_io_irq()

# 内核定时器
Linux内核使用timer_list结构体表示内核定时器
其中主要包含定时器超时时间
定时处理函数
要传递给function函数的参数

## 主要api
init_timer()
add_timer()
del_timer()
del_timer_sync()
mod_timer()

# Linux中断api函数
1. 中断号
2. request_irq()申请中断，要求中断号，中断处理函数，中断标志（怎么触发），终端名字，dev等
3. free_irq()
4. 中断处理函数
5. 中断使能与禁止函数enable_irq()/disable_irq()
local_irq_save()禁止中断，并将状态保存在flags中
local_irq_restore()恢复中断，将中断到flags状态

## 上半部与下半部
下半部已被弃用，现在都是使用软中断和tasklet来替代BH

### 软中断

open_softirq()注册
raise_softirq()触发软中断
软中断初始化必须要静态编译的时候注册，会默认打开TSASKLET_SOFTIRQ和HI_SOFTIRQ

### tasklet

使用tasklet_struct表示
内部包含下一个tasklet
tasklet状态
计数器
tasklet执行的函数
函数func的参数

task_init()初始化
也可以使用DECLARE_TASKLET对tasklet一次性完成定义和初始化

### 工作队列

如果可以睡眠就可以用这个
使用work_struct表示一个工作
里面有工作队列处理函数
使用workqueue_struct表示一个工作队列
使用woker结构体表示一个工作者线程
它也可以用宏一次性定义和初始化
调用也要用schedule_xxx()


