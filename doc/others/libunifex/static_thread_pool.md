# 使用示例
```
int main() {
  static_thread_pool tpContext;           // 创建线程池
  auto tp = tpContext.get_scheduler();    // 获取调度器

  sync_wait(when_all(                     // 等待所有任务完成
      run_on(tp, [&] { /* task 1 */ }),  // 提交任务1
      run_on(tp, [&] { /* task 2 */ }),  // 提交任务2
      run_on(tp, [&] { /* task 3 */ })   // 提交任务3
  ));
}

```

# 工作窃取
```
void context::run(std::uint32_t index) noexcept {
  while (true) {
    task_base* task = nullptr;
    
    // 1. 先尝试窃取其他线程的任务
    for (std::uint32_t i = 0; i < threadCount_; ++i) {
      auto queueIndex = (index + i) < threadCount_ ? 
                       (index + i) : (index + i - threadCount_);
      auto& state = threadStates_[queueIndex];
      task = state.try_pop();  // 尝试无阻塞窃取
      if (task != nullptr) break;
    }

    // 2. 如果没窃取到，才从自己队列获取
    if (task == nullptr) {
      task = threadStates_[index].pop();  // 阻塞获取自己的任务
      if (task == nullptr) return;  // 停止信号
    }

    task->execute(task);
  }
}
```
# 负载均衡
```

void context::enqueue(task_base* task) noexcept {
  // 1. 轮询选择初始线程
  const std::uint32_t startIndex =
      nextThread_.fetch_add(1, std::memory_order_relaxed) % threadCount;

  // 2. 尝试无阻塞投递
  for (std::uint32_t i = 0; i < threadCount; ++i) {
    const auto index = (startIndex + i) < threadCount ? 
                      (startIndex + i) : (startIndex + i - threadCount);
    if (threadStates_[index].try_push(task)) {
      return;
    }
  }

  // 3. 强制投递到初始选择的线程
  threadStates_[startIndex].push(task);
}

```


