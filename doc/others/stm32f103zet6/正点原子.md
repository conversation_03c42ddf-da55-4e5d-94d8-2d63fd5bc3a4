# GPIO
## gpio的八种模式
1. 输入浮空 -上拉和下拉电阻关闭
2. 输入上拉 -内部上拉，默认高电平
3. 输入下拉 -内部下拉，默认低电平
4. 模拟功能 -ADC、DAC施密特触发器也要关闭
5. 开漏输出 -p-mos关闭
6. 推挽输出 -两个mos都可开关
7. 开漏式复用
8. 推挽式复用

## gpio配置步骤
1. 使能时钟- __HAL_RCC_GPIOx_CLK_ENABLE()
2. 设置工作模式 HAL_GPIO_Init()
3. 设置输出状态（可选） HAL_GPIO_WritePin() HAL_GPIO_TogglePin()
4. 读取输入状态（可选） HAL_GPIO_ReadPin()

```c
typedef struct 
{ 
  uint32_t Pin;        /* 引脚号 */ 
  uint32_t Mode;   /* 模式设置 */ 
  uint32_t Pull;       /* 上拉下拉设置 */ 
  uint32_t Speed;  /* 速度设置 */ 
} GPIO_InitTypeDef;

```
## led.h
```c

/******************************************************************************************/
/* LED端口定义 */
#define LED0(x)   do{ x ? \
                      HAL_GPIO_WritePin(LED0_GPIO_PORT, LED0_GPIO_PIN, GPIO_PIN_SET) : \
                      HAL_GPIO_WritePin(LED0_GPIO_PORT, LED0_GPIO_PIN, GPIO_PIN_RESET); \
                  }while(0)      /* LED0翻转 */

#define LED1(x)   do{ x ? \
                      HAL_GPIO_WritePin(LED1_GPIO_PORT, LED1_GPIO_PIN, GPIO_PIN_SET) : \
                      HAL_GPIO_WritePin(LED1_GPIO_PORT, LED1_GPIO_PIN, GPIO_PIN_RESET); \
                  }while(0)      /* LED1翻转 */

/* LED取反定义 */
#define LED0_TOGGLE()   do{ HAL_GPIO_TogglePin(LED0_GPIO_PORT, LED0_GPIO_PIN); }while(0)        /* 翻转LED0 */
#define LED1_TOGGLE()   do{ HAL_GPIO_TogglePin(LED1_GPIO_PORT, LED1_GPIO_PIN); }while(0)        /* 翻转LED1 */

/******************************************************************************************/
/* 外部接口函数*/
void led_init(void); 

```
## led.c
```c

void led_init(void)
{
    GPIO_InitTypeDef gpio_init_struct;
    LED0_GPIO_CLK_ENABLE();                                 /* LED0时钟使能 */
    LED1_GPIO_CLK_ENABLE();                                 /* LED1时钟使能 */

    gpio_init_struct.Pin = LED0_GPIO_PIN;                   /* LED0引脚 */
    gpio_init_struct.Mode = GPIO_MODE_OUTPUT_PP;            /* 推挽输出 */
    gpio_init_struct.Pull = GPIO_PULLUP;                    /* 上拉 */
    gpio_init_struct.Speed = GPIO_SPEED_FREQ_HIGH;          /* 高速 */
    HAL_GPIO_Init(LED0_GPIO_PORT, &gpio_init_struct);       /* 初始化LED0引脚 */

    gpio_init_struct.Pin = LED1_GPIO_PIN;                   /* LED1引脚 */
    HAL_GPIO_Init(LED1_GPIO_PORT, &gpio_init_struct);       /* 初始化LED1引脚 */
    

    LED0(1);                                                /* 关闭 LED0 */
    LED1(1);                                                /* 关闭 LED1 */
}
```
## KEY连接原理
WK_UP连接PA0输入高电平，所以需要输入下拉模式，默认低电平
PE234，为KEY012 接地，所以需要输入上拉

# 中断
## STM32 NVIC的使用
1. 设置中断分组 HAL_NVIC_SetPriorityGrouping
2. 设置中断优先级 HAL_NVIC_SetPriorit
3. 使能中断 HAL_NVIC_EnableIRQ

## EXTI的HAL库设置步骤
1. 使能GPIO时钟 __HAL_RCC_GPIOx_CLK_ENABLE
2. GPIO/AFIO(SYSCFG)/EXTI HAL_GPIO_Init，一步到位 设置上升沿还是下降沿，模式，还有上拉下拉
3. 设置中断分组 HAL_NVIC_SetPriorityGrouping
4. 设置中断优先级 HAL_NVIC_SetPriority
5. 使能中断 HAL_NVIC_EnableIRQ
6. 设计中断服务函数 EXTIx_IRQHandler，中断服务函数，清中断标志！

## 中断原理
首先初始化gpio，编写中断函数，在中断处理函数中触发总的回调函数，并在其中加入需要的gpio信息，这样就可以处理不同的io口
在这其中，所有的exitX对应到所有的p（a~k）X，即所有的pX0对应到exit0
