# 对象构造做到线程安全
1. 不要在构造函数中注册任何回调
2. 不要在构造函数中传递this指针，即使是最后一行

# 不能在析构函数中使用自己的mutex
因为这时mutex可能已经被析构函数销毁

# 单个函数要锁住想同类型的多个对象需要注意死锁
```cpp
void swap(Counter& a, Counter& b) {
    // 锁住对象 a 的互斥锁
    MutexLockGuard aLock(a.mutex_);
    // 锁住对象 b 的互斥锁
    MutexLockGuard bLock(b.mutex_);

    // 交换两个对象的值
    int64_t value = a.value_;
    a.value_ = b.value_;
    b.value_ = value;
}
```
swap(a,b)和swap(b,a)同时使用就有可能死锁
解决方案：比较mutex对象的地址，始终先加锁地址较小的mutex

# 原始指针的不妥
## 多线程问题：

在多线程中使用无法提示使用者是否资源有效

## 空悬指针 ：
    两个指针指向同一个对象，但是free了一个指针

解决方法：设置一层间接层引入引用计数 这就是一个计数型智能指针

## 一个万能的解决方案
引入另外一层间接性，使用对象来管理共享资源
当然，自己写十分困难，直接使用shared_ptr/weak_ptr即可

## 重复释放，内存泄漏
使用scoped_ptr,只在对象析构的时候释放一次，且对象析构时自动释放内存

## 不配对的new[]/delete

把new[]全部替换为std:vector/scoped_array

# 使用智能指针优化观察者类
```
class Observable {
public:
    void register_(std::weak_ptr<Observer> x);
    void notifyObservers();

private:
    mutable MutexLock mutex_;
    std::vector<std::weak_ptr<Observer>> observers_;
    typedef std::vector<std::weak_ptr<Observer>>::iterator Iterator;
};

void Observable::register_(std::weak_ptr<Observer> x) {
    MutexLockGuard lock(mutex_);  // 保护共享资源的并发访问
    observers_.push_back(x);      // 将观察者添加到列表中
}
void Observable::notifyObservers() {
    MutexLockGuard lock(mutex_);  // 加锁，保护共享资源
    Iterator it = observers_.begin();

    while (it != observers_.end()) {
        std::shared_ptr<Observer> obj(it->lock());  // 尝试提升weak_ptr为shared_ptr
        if (obj) {
            // 提升成功，引用计数值至少为2
            obj->update();  // 调用观察者的更新方法
            ++it;
        } else {
            // weak_ptr指向的对象已经销毁，从容器中移除
            it = observers_.erase(it);
        }
    }
}

```
虽然实现了大部分的线程安全，但是还是有些问题
## 侵入性
要求外部的Observer类必须shared_ptr管理
##  不是完全线程安全
在这种设计中，Observer 对象的析构函数可能会调用 Subject::unregister(this)，而此时 Subject 对象本身可能已经被销毁了。为了解决这个问题，通常要求 Observable 使用 shared_ptr 管理其生命周期，并且在 Observer 中保存一个 `weak_ptr<Observable>`，而不是一个裸指针或 `shared_ptr<Observable>`。
##  锁争用
Observable 的 register_()、unregister() 和 notifyObservers() 都用了互斥锁（mutex_）来同步。这个设计虽然保证了线程安全，但也可能导致锁争用问题，特别是在 notifyObservers() 方法中。

## 死锁问题
这个问题涉及在 update() 函数中可能调用 register_() 或 unregister() 的情况。假设 mutex_ 是不可重入的，那么在这种情况下会导致死锁。即使 mutex_ 是可重入的，也可能导致迭代器失效的问题，因为 vector 在遍历期间如果发生修改，可能导致未定义行为甚至崩溃。

# shared_ptr技术与陷阱
## 意外延长对象的生命期
shared_ptr可拷贝构造与赋值，如果漏掉一个就可能永存
比如上面的`std::vector<std::weak_ptr<Observer>>`如果使用强引用，那么就无法触发析构函数，除非手动调用unregister()。
boost::bind会把实参拷贝一份，如果是一个强引用，那么它的寿命不会短于boost::function。
## 函数参数
shared_ptr的拷贝开销要比原始指针要高，最好是使用引用模式
## `shared_ptr` 的高级特性与应用场景

`shared_ptr` 是 C++ 标准库中广泛使用的智能指针，自动管理动态分配对象的生命周期。本文探讨了其一些高级特性以及在内存管理、跨模块安全性和二进制兼容性方面的应用。

### 1. 析构动作在创建时被捕获

- **捕获机制**: `shared_ptr` 在创建时能够捕获并保存对象的销毁方式。这使得即使对象在不同模块间传递，`shared_ptr` 也能正确销毁对象。
- **灵活性**: 这种捕获的“析构行为”可以是函数指针、仿函数（functor）、lambda 等。

### 2. 虚析构不再是必需的

- 传统 C++ 面向对象编程中，通常需要通过基类的虚析构函数（`virtual`）来确保通过基类指针删除派生类对象时调用正确的析构函数。
- 但使用 `shared_ptr` 后，可以通过自定义删除器代替虚析构函数，确保正确的析构行为。

### 3. `shared_ptr<void>` 可以持有任何对象并安全释放

- 即使 `shared_ptr` 被声明为持有 `void` 类型的指针，通过自定义删除器，它仍能正确管理和释放任意类型的对象。
- 这种特性使得 `shared_ptr<void>` 成为一种通用的资源管理工具，适用于各种对象类型。

### 4. 跨模块的安全性

- 在涉及多个模块（如 DLL、动态库等）的项目中，不同模块可能会创建或销毁对象。如果模块 A 创建了对象，而模块 B 错误地销毁了这个对象（因为内存管理策略不同），会导致崩溃。
- `shared_ptr` 通过将销毁策略内嵌到其内部，避免了这种跨模块内存管理问题。

### 5. 二进制兼容性

- 当类的定义发生变化（如增加成员变量）时，如果用户代码通过动态库使用这个类且未重新编译，通常会出现二进制兼容性问题。
- 然而，如果对象是通过动态库中的工厂函数创建，并返回 `shared_ptr<T>`，即使类的内部结构发生变化，用户代码仍可正常工作，只要不直接访问类的成员变量。

### 6. 析构行为的定制

- `shared_ptr` 允许自定义析构行为。通过指定自定义删除器（如函数指针、仿函数或 lambda 表达式），你可以精确控制对象的销毁过程。
- 这种灵活性特别适合复杂资源的管理，如文件句柄、网络连接等。

## 析构所在的线程
最后一个指向x的shared_ptr离开其作用域的时候，x会同时在同一个线程析构，这有可能会比较耗时，所以可以使用一个专门的线程去做析构。

# 对象池

1. 设计一个对象池，根据key返回对象
2. 使用弱指针`std::map<string,weak_ptr<stock>>stocks_`保存key
3. stocks_要能够自动清理空的项，使用stared_ptr的自定义析构函数实现

```
namespace version3
{

class StockFactory : boost::noncopyable
{
 public:

  boost::shared_ptr<Stock> get(const string& key)
  {
    boost::shared_ptr<Stock> pStock;
    muduo::MutexLockGuard lock(mutex_);
    boost::weak_ptr<Stock>& wkStock = stocks_[key];
    pStock = wkStock.lock();
    if (!pStock)
    {
      pStock.reset(new Stock(key),
                   boost::bind(&StockFactory::deleteStock, this, _1));
      wkStock = pStock;
    }
    return pStock;
  }

 private:

  void deleteStock(Stock* stock)
  {
    printf("deleteStock[%p]\n", stock);
    if (stock)
    {
      muduo::MutexLockGuard lock(mutex_);
      stocks_.erase(stock->key());  // This is wrong, see removeStock below for correct implementation.
    }
    delete stock;  // sorry, I lied
  }
  mutable muduo::MutexLock mutex_;
  std::map<string, boost::weak_ptr<Stock> > stocks_;
};

}
```
## enable_shared_from_this
如果stockfactory生命期比stock短，stock析构时去回调stockfactory会导致core dump。

一个成员函数想要获得指向当前对象的shared_ptr可以使用enable_shared_from_this。

使用shared_from_this代替this指针，这样在function中就保存了一份shared_ptr，可以保证在使用的时候stockfactory还活着

## 弱回调

function保存shared_ptr延长了stockfactory的生命期。
我们需要的是“如果对象还活着，就调用他的成员函数，否则忽略之”可以把function中绑定的shared_from_this强制转型为weak_ptr，在回调中再尝试提升。这种便称为弱回调。
```
class StockFactory : public boost::enable_shared_from_this<StockFactory>,
                     boost::noncopyable
{
 public:
  boost::shared_ptr<Stock> get(const string& key)
  {
    boost::shared_ptr<Stock> pStock;
    muduo::MutexLockGuard lock(mutex_);
    boost::weak_ptr<Stock>& wkStock = stocks_[key];
    pStock = wkStock.lock();
    if (!pStock)
    {
      pStock.reset(new Stock(key),
                   boost::bind(&StockFactory::weakDeleteCallback,
                               boost::weak_ptr<StockFactory>(shared_from_this()),
                               _1));
      wkStock = pStock;
    }
    return pStock;
  }

 private:
  static void weakDeleteCallback(const boost::weak_ptr<StockFactory>& wkFactory,
                                 Stock* stock)
  {
    printf("weakDeleteStock[%p]\n", stock);
    boost::shared_ptr<StockFactory> factory(wkFactory.lock());
    if (factory)
    {
      factory->removeStock(stock);
    }
    else
    {
      printf("factory died.\n");
    }
    delete stock;  // sorry, I lied
  }

  void removeStock(Stock* stock)
  {
    if (stock)
    {
      muduo::MutexLockGuard lock(mutex_);
      auto it = stocks_.find(stock->key());
      if (it != stocks_.end() && it->second.expired())
      {
        stocks_.erase(stock->key());
      }
    }
  }

 private:
  mutable muduo::MutexLock mutex_;
  std::map<string, boost::weak_ptr<Stock> > stocks_;
};
```

# 小结
1. 原始指针暴露给多个线程往往会造成race condition或额外的薄记负担。
2. 统一使用智能指针来管理对象的生命期，在多线程中尤其重要
3. shared_ptr被bind和容器拷贝后，可能会延长对象的生命期。
4. weak_ptr可以当作弱回调、对象池等
