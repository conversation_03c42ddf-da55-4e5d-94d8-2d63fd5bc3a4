# 显卡驱动
    官方的最初版本最好用，但是更新换源后要把所有的github源全删了，更新的驱动有问题
# ssh
    官方系统
    默认没有enable
    要开启允许root ssh
# docker
## portainer
### potainer的安装
sudo docker pull portainer/portainer


docker run -p 9000:9000 -p 8000:8000 --name portainer \
--restart=always \
-v /var/run/docker.sock:/var/run/docker.sock \
-v /mnt/nvme0/dockerdata/portainer/data:/data \
-d portainer/portainer
## openwrt
首先确定IP，我这里有线连接rock的路由地址是********，rock有线口的地址是**********

开启混淆模式，也即网络接口会接收处理通过该接口的所有数据包，而不仅仅是目前地址是自己的数据包
```
# 查看网口的名字
netstat -i
# 开启混淆模式
sudo ip link set enP4p65s0 promisc on
```

创建一个docker网络
```
# -d macvlan：指定使用 "macvlan" 网络驱动程序。"macvlan" 允许将容器连接到物理网络，并为每个容器分配一个唯一的 MAC 地址。简而言之就是让容器模拟成了一个实体识别。
# --subnet=***********/24：该地址是你准备将容器扔到什么网络环境中，这里我扔到了路由器下
# --gateway=***********：指定网络的网关地址，该网络环境的网关就是路由器了
# -o parent=enP4p65s0：指定父网络接口。这表示该 Docker 网络将使用主机上的 "enP4p65s0" 接口作为物理网络连接。
# macvlan：指定要创建的 Docker 网络的名称。
docker network create -d macvlan -o parent=enP4p65s0  macnet
```

查看结果
```
sudo docker network ls
```

创建容器
```
docker run -d --name=openwrt --restart=always --network=macnet --privileged unifreq/openwrt-aarch64:latest
```

查看修改容器网络环境

```
sudo docker exec openwrt ifconfig

br-lan    Link encap:Ethernet  HWaddr 02:42:C0:A8:02:16
        inet addr:192.168.1.1  Bcast:*************  Mask:*************
        UP BROADCAST RUNNING MULTICAST  MTU:1500  Metric:1
        RX packets:126 errors:0 dropped:0 overruns:0 frame:0
        TX packets:24 errors:0 dropped:0 overruns:0 carrier:0
        collisions:0 txqueuelen:1000
        RX bytes:14103 (13.7 KiB)  TX bytes:5112 (4.9 KiB)
```

修改新的lan地址：

```
docker exec -it openwrt bash   
vim /etc/config/network

config interface 'lan'
  option type 'bridge'
  option ifname 'eth0'
  option proto 'static'
  option ipaddr '**********'
  option netmask '*************'
  option dns '********'
  option gateway '********'
  option ip6assign '60'
```

修改地址
```
/etc/init.d/network restart
然后通过armbian-config把wifi改成ac模式(network->Hostapd)
```

### 设置自动任务
```
#!/bin/bash
ip link set enP4p65s0 promisc on > /dev/null 2>&1
ip link add macvlan-proxy link enP4p65s0 type macvlan mode bridge
ip addr add ************* dev macvlan-proxy
ip link set macvlan-proxy up
ip route add ************* dev macvlan-proxy
ip route add default via ************* dev macvlan-proxy
exit 0
```
## jellyfin

sudo docker run -d \
 --privileged \
 --name=jellyfin \
 --volume /home/<USER>/jellyfin/config:/config \
 --volume /home/<USER>/jellyfin/cache:/cache \
 --volume /media/jellyfin:/media \
 --net=host \
 --restart=unless-stopped \
`for dev in dri dma_heap mali0 rga mpp_service \
   iep mpp-service vpu_service vpu-service \
   hevc_service hevc-service rkvdec rkvenc vepu h265e ; do \
  [ -e "/dev/$dev" ] && echo " --device /dev/$dev"; \
 done` \
 jellyfin/jellyfin

#!/bin/bash
ip link set enP4p65s0 promisc on > /dev/null 2>&1
ip link add macvlan-proxy link enP4p65s0 type macvlan mode bridge
ip addr add ******** dev macvlan-proxy
ip link set macvlan-proxy up
ip route add ********** dev macvlan-proxy
route add default gw ********** macvlan-proxy

# smanga
docker run -itd --name smanga \
-p 3333:3306 \
-p 9797:80 \
-v /media/smanga:/mnt \
-v /home/<USER>/smanga:/data \
--restart always \
lkw199711/smanga;

# komga
docker create \
  --name=komga \
  -p 25600:25600 \
  -v /media/komga:/data \
  -v /home/<USER>/komga:/config \
  --restart always \
  gotson/komga

# qbit

docker run -d \
  --name=qbittorrent \
  -e WEBUI_PORT=8080 \
  -p 8080:8080 \
  -p 6881:6881 \
  -p 6881:6881/udp \
  -v /home/<USER>/qbit/config:/config \
  -v /media/qbit:/downloads \
  --restart always \
lscr.io/linuxserver/qbittorrent:latest

# alist
docker run -d \
    --name alist \
    -v /home/<USER>/alist:/opt/alist/data \
    -v /mnt/nvme0:/mnt/nvme0
    -p 5244:5244 \
    --restart always \
    xhofe/alist:latest

# nginx
docker run -it -d \
    --net=host \
    --name nginx \
    --restart always \
    nginx

## nginx配置jellyfin

```
nginx 的 nginx.conf 添加如下
location ~ /alist {
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Host $http_host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header Range $http_range;
            proxy_set_header If-Range $http_if_range;
            proxy_redirect off;
            proxy_pass http://127.0.0.1:19952; #这是我的配置端口
            #the max size of file to upload
            client_max_body_size 20000m;
        }

alist 的config.json里的"site_url": ""修改为 "site_url": "alist"
```


# gitlab-ce

## 拉取Gitlab镜像
docker pull gitlab/gitlab-ce:latest


# 启动容器
docker run \
 -itd  \
 -p 3414:80 \
 -p 8022:22 \
 -v /mnt/nvme0/dockerdata/gitlab/etc:/etc/gitlab  \
 -v /mnt/nvme0/dockerdata/gitlab/log:/var/log/gitlab \
 -v /mnt/nvme0/dockerdata/gitlab/opt:/var/opt/gitlab \
 --restart always \
 --privileged=true \
 --name gitlab \
 gitlab/gitlab-ce



# v2raya
```
先安装v2ray-core，从此处下载：

https://github.com/v2fly/v2ray-core/releases
解压：

sudo unzip v2ray-linux-64.zip -d /usr/local/v2ray-core
然后拷贝geoip.dat和geosite.dat到/usr/local/share/v2ray/：

sudo mkdir -p /usr/local/share/v2ray/
sudo mv /usr/local/v2ray-core/*dat /usr/local/share/v2ray/


cut-off
v2rayA安装，从此处下载：

https://github.com/v2rayA/v2rayA/releases/
下载对应架构的deb包后安装：

sudo dpkg -i installer_debian_x64_*.deb


cut-off
配置v2rayA：

vim /etc/default/v2raya

# 添加如下配置

V2RAYA_V2RAY_BIN=/usr/local/v2ray-core/v2ray
V2RAYA_V2RAY_CONFDIR=/usr/local/v2ray-core

# 输入":wq!"后可保存更改
设置开机自启：

sudo systemctl enable --now v2raya
sudo systemctl status v2raya
```