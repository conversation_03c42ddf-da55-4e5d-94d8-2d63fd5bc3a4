#include <dt-bindings/clock/imx6ul-clock.h>
#include <dt-bindings/gpio/gpio.h>
#include <dt-bindings/input/input.h>
#include <dt-bindings/interrupt-controller/arm-gic.h>
#include <dt-bindings/media/video-interfaces.h>
#include "imx6ul-pinfunc.h"
#include "imx6ull-pinfunc.h"
#include "imx6ull-pinfunc-snvs.h"

/dts-v1/;

/ {
	#address-cells = <1>;
	#size-cells = <1>;
	/*
	 * The decompressor and also some bootloaders rely on a
	 * pre-existing /chosen node to be available to insert the
	 * command line and merge other ATAGS info.
	 */
    model = "Freescale i.MX6 UltraLiteLite 14x14 EVK Board";
	compatible = "fsl,imx6ull-14x14-evk", "fsl,imx6ull";
	chosen {
        stdout-path = &uart1;
    };

	aliases {
		ethernet0 = &fec1;
		ethernet1 = &fec2;
		gpio0 = &gpio1;
		gpio1 = &gpio2;
		gpio2 = &gpio3;
		gpio3 = &gpio4;
		gpio4 = &gpio5;
		i2c0 = &i2c1;
		i2c1 = &i2c2;
		i2c2 = &i2c3;
		i2c3 = &i2c4;
		mmc0 = &usdhc1;
		mmc1 = &usdhc2;
		serial0 = &uart1;
		serial2 = &uart3;
		serial3 = &uart4;
		serial4 = &uart5;
		serial5 = &uart6;
		serial6 = &uart7;
		serial7 = &uart8;
		sai1 = &sai1;
		sai2 = &sai2;
		sai3 = &sai3;
		spi0 = &ecspi1;
		spi1 = &ecspi2;
		spi2 = &ecspi3;
		spi3 = &ecspi4;
		usb0 = &usbotg1;
		usb1 = &usbotg2;
		usbphy0 = &usbphy1;
		usbphy1 = &usbphy2;
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;

		cpu0: cpu@0 {
			compatible = "arm,cortex-a7";
			device_type = "cpu";
			reg = <0>;
			clock-frequency = <900000000>;
			clock-latency = <61036>; /* two CLK32 periods */
			#cooling-cells = <2>;
			operating-points = <
                /* kHz	uV */
                900000	1275000
                792000	1225000
                528000	1175000
                396000	1025000
                198000	950000
            >;
			fsl,soc-operating-points = <
                /* KHz	uV */
                900000	1250000
                792000	1175000
                528000	1175000
                396000	1175000
                198000	1175000
            >;
			clocks = <&clks IMX6UL_CLK_ARM>,
				 <&clks IMX6UL_CLK_PLL2_BUS>,
				 <&clks IMX6UL_CLK_PLL2_PFD2>,
				 <&clks IMX6UL_CA7_SECONDARY_SEL>,
				 <&clks IMX6UL_CLK_STEP>,
				 <&clks IMX6UL_CLK_PLL1_SW>,
				 <&clks IMX6UL_CLK_PLL1_SYS>;
			clock-names = "arm", "pll2_bus",  "pll2_pfd2_396m",
				      "secondary_sel", "step", "pll1_sw",
				      "pll1_sys";
			arm-supply = <&reg_arm>;
			soc-supply = <&reg_soc>;
			nvmem-cells = <&cpu_speed_grade>;
			nvmem-cell-names = "speed_grade";
		};
	};

    memory@80000000 {
		device_type = "memory";
		reg = <0x80000000 0x20000000>;
	};

    leds {
        compatible = "gpio-leds";
        led0 {
            label = "red";
            gpios = <&gpio1 3 GPIO_ACTIVE_LOW>;
            linux,default-trigger = "heartbeat";
            default-state = "on";
            };
		beep0{
			label = "beep";
            gpios = <&gpio5 1 GPIO_ACTIVE_HIGH>;
            default-state = "on";
		};
	};

	spi-4 {
        compatible = "spi-gpio";
        pinctrl-names = "default";
        pinctrl-0 = <&pinctrl_spi4>;
        status = "disabled";
        sck-gpios = <&gpio5 11 0>;
        mosi-gpios = <&gpio5 10 0>;
        cs-gpios = <&gpio5 7 GPIO_ACTIVE_LOW>;
        num-chipselects = <1>;
        #address-cells = <1>;
        #size-cells = <0>;

    };
	
	keys {
		compatible = "gpio-keys";
		#address-cells = <1>;
		#size-cells = <0>;
		autorepeat;
		key0 {
			label = "GPIO Key Enter";
			linux,code = <KEY_ENTER>;
			gpios = <&gpio1 18 GPIO_ACTIVE_LOW>;
		};
	};


    backlight_display: backlight-display {
		compatible = "pwm-backlight";
		pwms = <&pwm1 0 5000000 0>;
		brightness-levels = <0 4 8 16 32 64 128 255>;
		default-brightness-level = <6>;
		status = "okay";
	};

    reg_sd1_vmmc: regulator-sd1-vmmc {
		compatible = "regulator-fixed";
		regulator-name = "VSD_3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		/*gpio = <&gpio1 9 GPIO_ACTIVE_HIGH>;*/
		enable-active-high;
	};

    reg_peri_3v3: regulator-peri-3v3 {
		compatible = "regulator-fixed";
		pinctrl-names = "default";
		pinctrl-0 = <&pinctrl_peri_3v3>;
		regulator-name = "VPERI_3V3";
		regulator-min-microvolt = <3300000>;
		regulator-max-microvolt = <3300000>;
		gpio = <&gpio5 2 GPIO_ACTIVE_LOW>;
		/*
		 * If you want to want to make this dynamic please
		 * check schematics and test all affected peripherals:
		 *
		 * - sensors
		 * - ethernet phy
		 * - can
		 * - bluetooth
		 * - wm8960 audio codec
		 * - ov5640 camera
		 */
		regulator-always-on;
	};

    panel {
		compatible = "panel-dpi";
		backlight = <&backlight_display>;
		
		width-mm = <154>;    /* 屏幕物理宽度 */
		height-mm = <86>;    /* 屏幕物理高度 */
		
		panel-timing {
			clock-frequency = <51200000>;
			hactive = <1024>;
			vactive = <600>;
			hfront-porch = <160>;
			hback-porch = <140>;
			hsync-len = <20>;
			vback-porch = <20>;
			vfront-porch = <12>;
			vsync-len = <3>;
			hsync-active = <0>;
			vsync-active = <0>;
			de-active = <1>;
			pixelclk-active = <0>;
		};

		port {
			panel_in: endpoint {
				remote-endpoint = <&display_out>;
			};
		};
	};

	timer {
		compatible = "arm,armv7-timer";
		interrupts = <GIC_PPI 13 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 14 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 11 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>,
			     <GIC_PPI 10 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_LOW)>;
		interrupt-parent = <&intc>;
		status = "disabled";
	};

	sound-wm8960 {
		compatible = "fsl,imx-audio-wm8960";
		model = "wm8960-audio";
		audio-cpu = <&sai2>;
		audio-codec = <&codec>;
		audio-asrc = <&asrc>;
		hp-det-gpio = <&gpio5 4 0>;
		audio-routing =
			"Headphone Jack", "HP_L",
			"Headphone Jack", "HP_R",
			"Ext Spk", "SPK_LP",
			"Ext Spk", "SPK_LN",
			"Ext Spk", "SPK_RP",
			"Ext Spk", "SPK_RN",
			"LINPUT2", "Mic Jack",
			"LINPUT3", "Mic Jack",
			"RINPUT1", "AMIC",
			"RINPUT2", "AMIC",
			"Mic Jack", "MICB",
			"AMIC", "MICB";
	};

	ckil: clock-cli {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <32768>;
		clock-output-names = "ckil";
	};

	osc: clock-osc {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <********>;
		clock-output-names = "osc";
	};

	ipp_di0: clock-di0 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <0>;
		clock-output-names = "ipp_di0";
	};

	ipp_di1: clock-di1 {
		compatible = "fixed-clock";
		#clock-cells = <0>;
		clock-frequency = <0>;
		clock-output-names = "ipp_di1";
	};

	pmu {
		compatible = "arm,cortex-a7-pmu";
		interrupt-parent = <&gpc>;
		interrupts = <GIC_SPI 94 IRQ_TYPE_LEVEL_HIGH>;
	};

	soc: soc {
		#address-cells = <1>;
		#size-cells = <1>;
		compatible = "simple-bus";
		interrupt-parent = <&gpc>;
		ranges;

		ocram: sram@900000 {
			compatible = "mmio-sram";
			reg = <0x00900000 0x20000>;
			ranges = <0 0x00900000 0x20000>;
			#address-cells = <1>;
			#size-cells = <1>;
		};

		intc: interrupt-controller@a01000 {
			compatible = "arm,gic-400", "arm,cortex-a7-gic";
			interrupts = <GIC_PPI 9 (GIC_CPU_MASK_SIMPLE(1) | IRQ_TYPE_LEVEL_HIGH)>;
			#interrupt-cells = <3>;
			interrupt-controller;
			interrupt-parent = <&intc>;
			reg = <0x00a01000 0x1000>,
			      <0x00a02000 0x2000>,
			      <0x00a04000 0x2000>,
			      <0x00a06000 0x2000>;
		};

		dma_apbh: dma-controller@1804000 {
			compatible = "fsl,imx6q-dma-apbh", "fsl,imx28-dma-apbh";
			reg = <0x01804000 0x2000>;
			interrupts = <0 13 IRQ_TYPE_LEVEL_HIGH>,
				     <0 13 IRQ_TYPE_LEVEL_HIGH>,
				     <0 13 IRQ_TYPE_LEVEL_HIGH>,
				     <0 13 IRQ_TYPE_LEVEL_HIGH>;
			#dma-cells = <1>;
			dma-channels = <4>;
			clocks = <&clks IMX6UL_CLK_APBHDMA>;
		};

		gpmi: nand-controller@1806000 {
			compatible = "fsl,imx6q-gpmi-nand";
			#address-cells = <1>;
			#size-cells = <0>;
			reg = <0x01806000 0x2000>, <0x01808000 0x2000>;
			reg-names = "gpmi-nand", "bch";
			interrupts = <0 15 IRQ_TYPE_LEVEL_HIGH>;
			interrupt-names = "bch";
			clocks = <&clks IMX6UL_CLK_GPMI_IO>,
				 <&clks IMX6UL_CLK_GPMI_APB>,
				 <&clks IMX6UL_CLK_GPMI_BCH>,
				 <&clks IMX6UL_CLK_GPMI_BCH_APB>,
				 <&clks IMX6UL_CLK_PER_BCH>;
			clock-names = "gpmi_io", "gpmi_apb", "gpmi_bch",
				      "gpmi_bch_apb", "per1_bch";
			dmas = <&dma_apbh 0>;
			dma-names = "rx-tx";
			status = "disabled";
		};

		aips1: bus@2000000 {
			compatible = "fsl,aips-bus", "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x02000000 0x100000>;
			ranges;

			spba-bus@2000000 {
				compatible = "fsl,spba-bus", "simple-bus";
				#address-cells = <1>;
				#size-cells = <1>;
				reg = <0x02000000 0x40000>;
				ranges;

				ecspi1: spi@2008000 {
					#address-cells = <1>;
					#size-cells = <0>;
					compatible = "fsl,imx6ul-ecspi", "fsl,imx51-ecspi";
					reg = <0x02008000 0x4000>;
					interrupts = <GIC_SPI 31 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_ECSPI1>,
						 <&clks IMX6UL_CLK_ECSPI1>;
					clock-names = "ipg", "per";
					dmas = <&sdma 3 7 1>, <&sdma 4 7 2>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				ecspi2: spi@200c000 {
					#address-cells = <1>;
					#size-cells = <0>;
					compatible = "fsl,imx6ul-ecspi", "fsl,imx51-ecspi";
					reg = <0x0200c000 0x4000>;
					interrupts = <GIC_SPI 32 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_ECSPI2>,
						 <&clks IMX6UL_CLK_ECSPI2>;
					clock-names = "ipg", "per";
					dmas = <&sdma 5 7 1>, <&sdma 6 7 2>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				ecspi3: spi@2010000 {
					#address-cells = <1>;
					#size-cells = <0>;
					compatible = "fsl,imx6ul-ecspi", "fsl,imx51-ecspi";
					reg = <0x02010000 0x4000>;
					interrupts = <GIC_SPI 33 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_ECSPI3>,
						 <&clks IMX6UL_CLK_ECSPI3>;
					clock-names = "ipg", "per";
					dmas = <&sdma 7 7 1>, <&sdma 8 7 2>;
					dma-names = "rx", "tx";
					fsl,spi-num-chipselects = <1>;
                    cs-gpios = <&gpio1 20 GPIO_ACTIVE_LOW>;
                    pinctrl-names = "default";
                    pinctrl-0 = <&pinctrl_ecspi3>;
                    status = "okay";

                    spidev: icm20608@0 {
                        compatible = "invensense,icm20608";
                        spi-max-frequency = <8000000>;
                        reg = <0>;
                    };
				};

				ecspi4: spi@2014000 {
					#address-cells = <1>;
					#size-cells = <0>;
					compatible = "fsl,imx6ul-ecspi", "fsl,imx51-ecspi";
					reg = <0x02014000 0x4000>;
					interrupts = <GIC_SPI 34 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_ECSPI4>,
						 <&clks IMX6UL_CLK_ECSPI4>;
					clock-names = "ipg", "per";
					dmas = <&sdma 9 7 1>, <&sdma 10 7 2>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				uart7: serial@2018000 {
					compatible = "fsl,imx6ul-uart",
						     "fsl,imx6q-uart";
					reg = <0x02018000 0x4000>;
					interrupts = <GIC_SPI 39 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_UART7_IPG>,
						 <&clks IMX6UL_CLK_UART7_SERIAL>;
					clock-names = "ipg", "per";
					status = "disabled";
				};

				uart1: serial@2020000 {
					compatible = "fsl,imx6ul-uart",
						     "fsl,imx6q-uart";
					reg = <0x02020000 0x4000>;
					interrupts = <GIC_SPI 26 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_UART1_IPG>,
						 <&clks IMX6UL_CLK_UART1_SERIAL>;
					clock-names = "ipg", "per";
					pinctrl-names = "default";
                    pinctrl-0 = <&pinctrl_uart1>;
                    status = "okay";
				};

				sai1: sai@2028000 {
					#sound-dai-cells = <0>;
					compatible = "fsl,imx6ul-sai", "fsl,imx6sx-sai";
					reg = <0x02028000 0x4000>;
					interrupts = <GIC_SPI 97 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_SAI1_IPG>,
						 <&clks IMX6UL_CLK_SAI1>,
						 <&clks IMX6UL_CLK_DUMMY>, <&clks IMX6UL_CLK_DUMMY>;
					clock-names = "bus", "mclk1", "mclk2", "mclk3";
					dmas = <&sdma 35 24 0>,
					       <&sdma 36 24 0>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				sai2: sai@202c000 {
					#sound-dai-cells = <0>;
					compatible = "fsl,imx6ul-sai", "fsl,imx6sx-sai";
					reg = <0x0202c000 0x4000>;
					interrupts = <GIC_SPI 98 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_SAI2_IPG>,
						 <&clks IMX6UL_CLK_SAI2>,
						 <&clks IMX6UL_CLK_DUMMY>, <&clks IMX6UL_CLK_DUMMY>;
					clock-names = "bus", "mclk1", "mclk2", "mclk3";
					dmas = <&sdma 37 24 0>,
					       <&sdma 38 24 0>;
					dma-names = "rx", "tx";
					pinctrl-names = "default";
					pinctrl-0 = <&pinctrl_sai2>;
					assigned-clocks = <&clks IMX6UL_CLK_SAI2_SEL>,
							<&clks IMX6UL_CLK_SAI2>;
					assigned-clock-parents = <&clks IMX6UL_CLK_PLL4_AUDIO_DIV>;
					assigned-clock-rates = <0>, <12288000>;
					fsl,sai-mclk-direction-output;
					status = "okay";
				};

				sai3: sai@2030000 {
					#sound-dai-cells = <0>;
					compatible = "fsl,imx6ul-sai", "fsl,imx6sx-sai";
					reg = <0x02030000 0x4000>;
					interrupts = <GIC_SPI 24 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_SAI3_IPG>,
						 <&clks IMX6UL_CLK_SAI3>,
						 <&clks IMX6UL_CLK_DUMMY>, <&clks IMX6UL_CLK_DUMMY>;
					clock-names = "bus", "mclk1", "mclk2", "mclk3";
					dmas = <&sdma 39 24 0>,
					       <&sdma 40 24 0>;
					dma-names = "rx", "tx";
					status = "disabled";
				};

				asrc: asrc@2034000 {
					compatible = "fsl,imx6ul-asrc", "fsl,imx53-asrc";
					reg = <0x2034000 0x4000>;
					interrupts = <GIC_SPI 50 IRQ_TYPE_LEVEL_HIGH>;
					clocks = <&clks IMX6UL_CLK_ASRC_IPG>,
						<&clks IMX6UL_CLK_ASRC_MEM>, <&clks 0>,
						<&clks 0>, <&clks 0>, <&clks 0>, <&clks 0>,
						<&clks 0>, <&clks 0>, <&clks 0>, <&clks 0>,
						<&clks 0>, <&clks 0>, <&clks 0>, <&clks 0>,
						<&clks IMX6UL_CLK_SPDIF>, <&clks 0>, <&clks 0>,
						<&clks IMX6UL_CLK_SPBA>;
					clock-names = "mem", "ipg", "asrck_0",
						"asrck_1", "asrck_2", "asrck_3", "asrck_4",
						"asrck_5", "asrck_6", "asrck_7", "asrck_8",
						"asrck_9", "asrck_a", "asrck_b", "asrck_c",
						"asrck_d", "asrck_e", "asrck_f", "spba";
					dmas = <&sdma 17 23 1>, <&sdma 18 23 1>, <&sdma 19 23 1>,
						<&sdma 20 23 1>, <&sdma 21 23 1>, <&sdma 22 23 1>;
					dma-names = "rxa", "rxb", "rxc",
						    "txa", "txb", "txc";
					fsl,asrc-rate = <48000>;
					fsl,asrc-width = <16>;
					status = "okay";
				};
			};

			pwm1: pwm@2080000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x02080000 0x4000>;
				interrupts = <GIC_SPI 83 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM1>,
					 <&clks IMX6UL_CLK_PWM1>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_pwm1>;
                status = "okay";
			};

			pwm2: pwm@2084000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x02084000 0x4000>;
				interrupts = <GIC_SPI 84 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM2>,
					 <&clks IMX6UL_CLK_PWM2>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm3: pwm@2088000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x02088000 0x4000>;
				interrupts = <GIC_SPI 85 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM3>,
					 <&clks IMX6UL_CLK_PWM3>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm4: pwm@208c000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x0208c000 0x4000>;
				interrupts = <GIC_SPI 86 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM4>,
					 <&clks IMX6UL_CLK_PWM4>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			reg_can_3v3: regulator-can-3v3 {
				compatible = "regulator-fixed";
				regulator-name = "can-3v3";
				regulator-min-microvolt = <3300000>;
				regulator-max-microvolt = <3300000>;
			};

			can1: can@2090000 {
				compatible = "fsl,imx6ul-flexcan", "fsl,imx6q-flexcan";
				reg = <0x02090000 0x4000>;
				interrupts = <GIC_SPI 110 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_CAN1_IPG>,
					 <&clks IMX6UL_CLK_CAN1_SERIAL>;
				clock-names = "ipg", "per";
				fsl,stop-mode = <&gpr 0x10 1>;
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_flexcan1>;
				xceiver-supply = <&reg_can_3v3>;
				status = "okay";
			};

			can2: can@2094000 {
				compatible = "fsl,imx6ul-flexcan", "fsl,imx6q-flexcan";
				reg = <0x02094000 0x4000>;
				interrupts = <GIC_SPI 111 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_CAN2_IPG>,
					 <&clks IMX6UL_CLK_CAN2_SERIAL>;
				clock-names = "ipg", "per";
				fsl,stop-mode = <&gpr 0x10 2>;
				status = "disabled";
			};

			gpt1: timer@2098000 {
				compatible = "fsl,imx6ul-gpt", "fsl,imx6sx-gpt";
				reg = <0x02098000 0x4000>;
				interrupts = <GIC_SPI 55 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPT1_BUS>,
					 <&clks IMX6UL_CLK_GPT1_SERIAL>;
				clock-names = "ipg", "per";
			};

			gpio1: gpio@209c000 {
				compatible = "fsl,imx6ul-gpio", "fsl,imx35-gpio";
				reg = <0x0209c000 0x4000>;
				interrupts = <GIC_SPI 66 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 67 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPIO1>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc  0 23 10>, <&iomuxc 10 17 6>,
					      <&iomuxc 16 33 16>;
			};

			gpio2: gpio@20a0000 {
				compatible = "fsl,imx6ul-gpio", "fsl,imx35-gpio";
				reg = <0x020a0000 0x4000>;
				interrupts = <GIC_SPI 68 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 69 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPIO2>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 49 16>, <&iomuxc 16 111 6>;
			};

			gpio3: gpio@20a4000 {
				compatible = "fsl,imx6ul-gpio", "fsl,imx35-gpio";
				reg = <0x020a4000 0x4000>;
				interrupts = <GIC_SPI 70 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 71 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPIO3>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 65 29>;
			};

			gpio4: gpio@20a8000 {
				compatible = "fsl,imx6ul-gpio", "fsl,imx35-gpio";
				reg = <0x020a8000 0x4000>;
				interrupts = <GIC_SPI 72 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 73 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPIO4>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 94 17>, <&iomuxc 17 117 12>;
			};

			gpio5: gpio@20ac000 {
				compatible = "fsl,imx6ul-gpio", "fsl,imx35-gpio";
				reg = <0x020ac000 0x4000>;
				interrupts = <GIC_SPI 74 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 75 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPIO5>;
				gpio-controller;
				#gpio-cells = <2>;
				interrupt-controller;
				#interrupt-cells = <2>;
				gpio-ranges = <&iomuxc 0 7 10>, <&iomuxc 10 5 2>;
			};

			fec2: ethernet@20b4000 {
				compatible = "fsl,imx6ul-fec", "fsl,imx6q-fec";
				reg = <0x020b4000 0x4000>;
				interrupt-names = "int0", "pps";
				interrupts = <GIC_SPI 120 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 121 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_ENET>,
					 <&clks IMX6UL_CLK_ENET_AHB>,
					 <&clks IMX6UL_CLK_ENET_PTP>,
					 <&clks IMX6UL_CLK_ENET2_REF_SEL>;
				clock-names = "ipg", "ahb", "ptp",
					      "enet_clk_ref";
				fsl,num-tx-queues = <1>;
				fsl,num-rx-queues = <1>;
				fsl,stop-mode = <&gpr 0x10 4>;
				fsl,magic-packet;
				nvmem-cells = <&fec2_mac_addr>;
				nvmem-cell-names = "mac-address";
				pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_enet2
                                &pinctrl_enet2_reset>;
                phy-reset-gpios = <&gpio5 8 GPIO_ACTIVE_LOW>;
                phy-reset-duration = <200>;
                phy-mode = "rmii";
                phy-handle = <&ethphy1>;
                phy-supply = <&reg_peri_3v3>;
                status = "okay";

                mdio {
                    #address-cells = <1>;
                    #size-cells = <0>;

                    ethphy0: ethernet-phy@2 {
                        compatible = "ethernet-phy-id0022.1560";
                        reg = <2>;
                        micrel,led-mode = <1>;
                        clocks = <&clks IMX6UL_CLK_ENET_REF>;
                        clock-names = "rmii-ref";

                    };

                    ethphy1: ethernet-phy@1 {
                        compatible = "ethernet-phy-id0022.1560";
                        reg = <1>;
                        micrel,led-mode = <1>;
                        clocks = <&clks IMX6UL_CLK_ENET2_REF>;
                        clock-names = "rmii-ref";
                    };
                };
			};

			kpp: keypad@20b8000 {
				compatible = "fsl,imx6ul-kpp", "fsl,imx21-kpp";
				reg = <0x020b8000 0x4000>;
				interrupts = <GIC_SPI 82 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_KPP>;
				status = "disabled";
			};

			wdog1: watchdog@20bc000 {
				compatible = "fsl,imx6ul-wdt", "fsl,imx21-wdt";
				reg = <0x020bc000 0x4000>;
				interrupts = <GIC_SPI 80 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_WDOG1>;
                pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_wdog>;
                fsl,ext-reset-output;
			};

			wdog2: watchdog@20c0000 {
				compatible = "fsl,imx6ul-wdt", "fsl,imx21-wdt";
				reg = <0x020c0000 0x4000>;
				interrupts = <GIC_SPI 81 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_WDOG2>;
				status = "disabled";
			};

			clks: clock-controller@20c4000 {
				compatible = "fsl,imx6ul-ccm";
				reg = <0x020c4000 0x4000>;
				interrupts = <GIC_SPI 87 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 88 IRQ_TYPE_LEVEL_HIGH>;
				#clock-cells = <1>;
				clocks = <&ckil>, <&osc>, <&ipp_di0>, <&ipp_di1>;
				clock-names = "ckil", "osc", "ipp_di0", "ipp_di1";
                assigned-clocks = <&clks IMX6UL_CLK_PLL3_PFD2>;
	            assigned-clock-rates = <320000000>;
			};

			anatop: anatop@20c8000 {
				compatible = "fsl,imx6ul-anatop", "fsl,imx6q-anatop",
					     "syscon", "simple-mfd";
				reg = <0x020c8000 0x1000>;
				interrupts = <GIC_SPI 49 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 54 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 127 IRQ_TYPE_LEVEL_HIGH>;

				reg_3p0: regulator-3p0 {
					compatible = "fsl,anatop-regulator";
					regulator-name = "vdd3p0";
					regulator-min-microvolt = <2625000>;
					regulator-max-microvolt = <3400000>;
					anatop-reg-offset = <0x120>;
					anatop-vol-bit-shift = <8>;
					anatop-vol-bit-width = <5>;
					anatop-min-bit-val = <0>;
					anatop-min-voltage = <2625000>;
					anatop-max-voltage = <3400000>;
					anatop-enable-bit = <0>;
				};

				reg_arm: regulator-vddcore {
					compatible = "fsl,anatop-regulator";
					regulator-name = "cpu";
					regulator-min-microvolt = <725000>;
					regulator-max-microvolt = <1450000>;
					regulator-always-on;
					anatop-reg-offset = <0x140>;
					anatop-vol-bit-shift = <0>;
					anatop-vol-bit-width = <5>;
					anatop-delay-reg-offset = <0x170>;
					anatop-delay-bit-shift = <24>;
					anatop-delay-bit-width = <2>;
					anatop-min-bit-val = <1>;
					anatop-min-voltage = <725000>;
					anatop-max-voltage = <1450000>;
				};

				reg_soc: regulator-vddsoc {
					compatible = "fsl,anatop-regulator";
					regulator-name = "vddsoc";
					regulator-min-microvolt = <725000>;
					regulator-max-microvolt = <1450000>;
					regulator-always-on;
					anatop-reg-offset = <0x140>;
					anatop-vol-bit-shift = <18>;
					anatop-vol-bit-width = <5>;
					anatop-delay-reg-offset = <0x170>;
					anatop-delay-bit-shift = <28>;
					anatop-delay-bit-width = <2>;
					anatop-min-bit-val = <1>;
					anatop-min-voltage = <725000>;
					anatop-max-voltage = <1450000>;
				};

				tempmon: tempmon {
					compatible = "fsl,imx6ul-tempmon", "fsl,imx6sx-tempmon";
					interrupt-parent = <&gpc>;
					interrupts = <GIC_SPI 49 IRQ_TYPE_LEVEL_HIGH>;
					fsl,tempmon = <&anatop>;
					nvmem-cells = <&tempmon_calib>, <&tempmon_temp_grade>;
					nvmem-cell-names = "calib", "temp_grade";
					clocks = <&clks IMX6UL_CLK_PLL3_USB_OTG>;
					#thermal-sensor-cells = <0>;
				};
			};

			usbphy1: usbphy@20c9000 {
				compatible = "fsl,imx6ul-usbphy", "fsl,imx23-usbphy";
				reg = <0x020c9000 0x1000>;
				interrupts = <GIC_SPI 44 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_USBPHY1>;
				phy-3p0-supply = <&reg_3p0>;
				fsl,anatop = <&anatop>;
                fsl,tx-d-cal = <106>;
			};

			usbphy2: usbphy@20ca000 {
				compatible = "fsl,imx6ul-usbphy", "fsl,imx23-usbphy";
				reg = <0x020ca000 0x1000>;
				interrupts = <GIC_SPI 45 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_USBPHY2>;
				phy-3p0-supply = <&reg_3p0>;
				fsl,anatop = <&anatop>;
                fsl,tx-d-cal = <106>;
			};

			snvs: snvs@20cc000 {
				compatible = "fsl,sec-v4.0-mon", "syscon", "simple-mfd";
				reg = <0x020cc000 0x4000>;

				snvs_rtc: snvs-rtc-lp {
					compatible = "fsl,sec-v4.0-mon-rtc-lp";
					regmap = <&snvs>;
					offset = <0x34>;
					interrupts = <GIC_SPI 19 IRQ_TYPE_LEVEL_HIGH>,
						     <GIC_SPI 20 IRQ_TYPE_LEVEL_HIGH>;
				};

				snvs_poweroff: snvs-poweroff {
					compatible = "syscon-poweroff";
					regmap = <&snvs>;
					offset = <0x38>;
					value = <0x60>;
					mask = <0x60>;
					status = "okay";
				};

				snvs_pwrkey: snvs-powerkey {
					compatible = "fsl,sec-v4.0-pwrkey";
					regmap = <&snvs>;
					interrupts = <GIC_SPI 4 IRQ_TYPE_LEVEL_HIGH>;
					linux,keycode = <KEY_POWER>;
					wakeup-source;
					status = "okay";
				};

				snvs_lpgpr: snvs-lpgpr {
					compatible = "fsl,imx6ul-snvs-lpgpr";
				};
			};

			epit1: epit@20d0000 {
				reg = <0x020d0000 0x4000>;
				interrupts = <GIC_SPI 56 IRQ_TYPE_LEVEL_HIGH>;
			};

			epit2: epit@20d4000 {
				reg = <0x020d4000 0x4000>;
				interrupts = <GIC_SPI 57 IRQ_TYPE_LEVEL_HIGH>;
			};

			src: reset-controller@20d8000 {
				compatible = "fsl,imx6ul-src", "fsl,imx51-src";
				reg = <0x020d8000 0x4000>;
				interrupts = <GIC_SPI 91 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 96 IRQ_TYPE_LEVEL_HIGH>;
				#reset-cells = <1>;
			};

			gpc: gpc@20dc000 {
				compatible = "fsl,imx6ul-gpc", "fsl,imx6q-gpc";
				reg = <0x020dc000 0x4000>;
				interrupt-controller;
				#interrupt-cells = <3>;
				interrupts = <GIC_SPI 89 IRQ_TYPE_LEVEL_HIGH>;
				interrupt-parent = <&intc>;
				clocks = <&clks IMX6UL_CLK_IPG>;
				clock-names = "ipg";

				pgc {
					#address-cells = <1>;
					#size-cells = <0>;

					power-domain@0 {
						reg = <0>;
						#power-domain-cells = <0>;
					};
				};
			};

			iomuxc: pinctrl@20e0000 {
				compatible = "fsl,imx6ul-iomuxc";
				reg = <0x020e0000 0x4000>;

                pinctrl-names = "default";

                pinctrl_camera_clock: cameraclockgrp {
                    fsl,pins = <
                        MX6UL_PAD_CSI_MCLK__CSI_MCLK		0x1b088
                    >;
                };

                pinctrl_led: ledgrp {
                    fsl,pins = <
                        MX6UL_PAD_GPIO1_IO03__GPIO1_IO03 0x10B0 /* LED0 */
                    >;
                };
                pinctrl_beep: beepgrp {
                    fsl,pins = <
                        MX6ULL_PAD_SNVS_TAMPER1__GPIO5_IO01 0x10B0 /* beep */ 
                    >;
                };
                pinctrl_key: keygrp {
                    fsl,pins = <
                        MX6UL_PAD_UART1_CTS_B__GPIO1_IO18 0xF080 /* KEY0 */
                    >;
                };
				pinctrl_uart3: uart3grp {
					fsl,pins = <
						MX6UL_PAD_UART3_TX_DATA__UART3_DCE_TX 0X1b0b1
						MX6UL_PAD_UART3_RX_DATA__UART3_DCE_RX 0X1b0b1
					>;
				};
				pinctrl_tsc: tscgrp {
					fsl,pins = <
						MX6UL_PAD_GPIO1_IO09__GPIO1_IO09 0x79 /* TSC_INT */
					>;
				};
                pinctrl_csi1: csi1grp {
                    fsl,pins = <
                        MX6UL_PAD_CSI_PIXCLK__CSI_PIXCLK	0x1b088
                        MX6UL_PAD_CSI_VSYNC__CSI_VSYNC		0x1b088
                        MX6UL_PAD_CSI_HSYNC__CSI_HSYNC		0x1b088
                        MX6UL_PAD_CSI_DATA00__CSI_DATA02	0x1b088
                        MX6UL_PAD_CSI_DATA01__CSI_DATA03	0x1b088
                        MX6UL_PAD_CSI_DATA02__CSI_DATA04	0x1b088
                        MX6UL_PAD_CSI_DATA03__CSI_DATA05	0x1b088
                        MX6UL_PAD_CSI_DATA04__CSI_DATA06	0x1b088
                        MX6UL_PAD_CSI_DATA05__CSI_DATA07	0x1b088
                        MX6UL_PAD_CSI_DATA06__CSI_DATA08	0x1b088
                        MX6UL_PAD_CSI_DATA07__CSI_DATA09	0x1b088
                    >;
                };

                pinctrl_enet1: enet1grp {
                    fsl,pins = <
                        MX6UL_PAD_ENET1_RX_EN__ENET1_RX_EN	0x1b0b0
                        MX6UL_PAD_ENET1_RX_ER__ENET1_RX_ER	0x1b0b0
                        MX6UL_PAD_ENET1_RX_DATA0__ENET1_RDATA00	0x1b0b0
                        MX6UL_PAD_ENET1_RX_DATA1__ENET1_RDATA01	0x1b0b0
                        MX6UL_PAD_ENET1_TX_EN__ENET1_TX_EN	0x1b0b0
                        MX6UL_PAD_ENET1_TX_DATA0__ENET1_TDATA00	0x1b0b0
                        MX6UL_PAD_ENET1_TX_DATA1__ENET1_TDATA01	0x1b0b0
                        MX6UL_PAD_ENET1_TX_CLK__ENET1_REF_CLK1	0x4001b031
                    >;
                };

                pinctrl_enet2: enet2grp {
                    fsl,pins = <
                        MX6UL_PAD_GPIO1_IO07__ENET2_MDC		0x1b0b0
                        MX6UL_PAD_GPIO1_IO06__ENET2_MDIO	0x1b0b0
                        MX6UL_PAD_ENET2_RX_EN__ENET2_RX_EN	0x1b0b0
                        MX6UL_PAD_ENET2_RX_ER__ENET2_RX_ER	0x1b0b0
                        MX6UL_PAD_ENET2_RX_DATA0__ENET2_RDATA00	0x1b0b0
                        MX6UL_PAD_ENET2_RX_DATA1__ENET2_RDATA01	0x1b0b0
                        MX6UL_PAD_ENET2_TX_EN__ENET2_TX_EN	0x1b0b0
                        MX6UL_PAD_ENET2_TX_DATA0__ENET2_TDATA00	0x1b0b0
                        MX6UL_PAD_ENET2_TX_DATA1__ENET2_TDATA01	0x1b0b0
                        MX6UL_PAD_ENET2_TX_CLK__ENET2_REF_CLK2	0x4001b031
                    >;
                };

                pinctrl_flexcan1: flexcan1grp {
                    fsl,pins = <
                        MX6UL_PAD_UART3_RTS_B__FLEXCAN1_RX	0x1b020
                        MX6UL_PAD_UART3_CTS_B__FLEXCAN1_TX	0x1b020
                    >;
                };

                pinctrl_i2c1: i2c1grp {
                    fsl,pins = <
                        MX6UL_PAD_UART4_TX_DATA__I2C1_SCL 0x4001b8b0
                        MX6UL_PAD_UART4_RX_DATA__I2C1_SDA 0x4001b8b0
                    >;
                };

                pinctrl_i2c2: i2c2grp {
                    fsl,pins = <
                        MX6UL_PAD_UART5_TX_DATA__I2C2_SCL 0x4001b8b0
                        MX6UL_PAD_UART5_RX_DATA__I2C2_SDA 0x4001b8b0
                    >;
                };

                pinctrl_ecspi3: icm20608 {
                    fsl,pins = <
                        MX6UL_PAD_UART2_TX_DATA__GPIO1_IO20 0x10b0 /* CS */
                        MX6UL_PAD_UART2_RX_DATA__ECSPI3_SCLK 0x10b1 /* SCLK */
                        MX6UL_PAD_UART2_RTS_B__ECSPI3_MISO 0x10b1 /* MISO */
                        MX6UL_PAD_UART2_CTS_B__ECSPI3_MOSI 0x10b1 /* MOSI */
                    >;
                };

                pinctrl_lcdif_dat: lcdifdatgrp {
                    fsl,pins = <
                        MX6UL_PAD_LCD_DATA00__LCDIF_DATA00  0x79
                        MX6UL_PAD_LCD_DATA01__LCDIF_DATA01  0x79
                        MX6UL_PAD_LCD_DATA02__LCDIF_DATA02  0x79
                        MX6UL_PAD_LCD_DATA03__LCDIF_DATA03  0x79
                        MX6UL_PAD_LCD_DATA04__LCDIF_DATA04  0x79
                        MX6UL_PAD_LCD_DATA05__LCDIF_DATA05  0x79
                        MX6UL_PAD_LCD_DATA06__LCDIF_DATA06  0x79
                        MX6UL_PAD_LCD_DATA07__LCDIF_DATA07  0x79
                        MX6UL_PAD_LCD_DATA08__LCDIF_DATA08  0x79
                        MX6UL_PAD_LCD_DATA09__LCDIF_DATA09  0x79
                        MX6UL_PAD_LCD_DATA10__LCDIF_DATA10  0x79
                        MX6UL_PAD_LCD_DATA11__LCDIF_DATA11  0x79
                        MX6UL_PAD_LCD_DATA12__LCDIF_DATA12  0x79
                        MX6UL_PAD_LCD_DATA13__LCDIF_DATA13  0x79
                        MX6UL_PAD_LCD_DATA14__LCDIF_DATA14  0x79
                        MX6UL_PAD_LCD_DATA15__LCDIF_DATA15  0x79
                        MX6UL_PAD_LCD_DATA16__LCDIF_DATA16  0x79
                        MX6UL_PAD_LCD_DATA17__LCDIF_DATA17  0x79
                        MX6UL_PAD_LCD_DATA18__LCDIF_DATA18  0x79
                        MX6UL_PAD_LCD_DATA19__LCDIF_DATA19  0x79
                        MX6UL_PAD_LCD_DATA20__LCDIF_DATA20  0x79
                        MX6UL_PAD_LCD_DATA21__LCDIF_DATA21  0x79
                        MX6UL_PAD_LCD_DATA22__LCDIF_DATA22  0x79
                        MX6UL_PAD_LCD_DATA23__LCDIF_DATA23  0x79
                    >;
                };

                pinctrl_lcdif_ctrl: lcdifctrlgrp {
                    fsl,pins = <
                        MX6UL_PAD_LCD_CLK__LCDIF_CLK	    0x79
                        MX6UL_PAD_LCD_ENABLE__LCDIF_ENABLE  0x79
                        MX6UL_PAD_LCD_HSYNC__LCDIF_HSYNC    0x79
                        MX6UL_PAD_LCD_VSYNC__LCDIF_VSYNC    0x79
                    >;
                };

                pinctrl_qspi: qspigrp {
                    fsl,pins = <
                        MX6UL_PAD_NAND_WP_B__QSPI_A_SCLK	0x70a1
                        MX6UL_PAD_NAND_READY_B__QSPI_A_DATA00	0x70a1
                        MX6UL_PAD_NAND_CE0_B__QSPI_A_DATA01	0x70a1
                        MX6UL_PAD_NAND_CE1_B__QSPI_A_DATA02	0x70a1
                        MX6UL_PAD_NAND_CLE__QSPI_A_DATA03	0x70a1
                        MX6UL_PAD_NAND_DQS__QSPI_A_SS0_B	0x70a1
                    >;
                };

                pinctrl_sai2: sai2grp {
                    fsl,pins = <
                        MX6UL_PAD_JTAG_TDI__SAI2_TX_BCLK	0x17088
                        MX6UL_PAD_JTAG_TDO__SAI2_TX_SYNC	0x17088
                        MX6UL_PAD_JTAG_TRST_B__SAI2_TX_DATA	0x11088
                        MX6UL_PAD_JTAG_TCK__SAI2_RX_DATA	0x11088
                        MX6UL_PAD_JTAG_TMS__SAI2_MCLK		0x17088
                    >;
                };
                pinctrl_peri_3v3: peri3v3grp {
                    fsl,pins = <
                        MX6UL_PAD_SNVS_TAMPER2__GPIO5_IO02	0x1b0b0
                    >;
                };

                pinctrl_pwm1: pwm1grp {
                    fsl,pins = <
                        MX6UL_PAD_GPIO1_IO08__PWM1_OUT   0x110b0
                    >;
                };

                pinctrl_sim2: sim2grp {
                    fsl,pins = <
                        MX6UL_PAD_CSI_DATA03__SIM2_PORT1_PD		0xb808
                        MX6UL_PAD_CSI_DATA04__SIM2_PORT1_CLK		0x31
                        MX6UL_PAD_CSI_DATA05__SIM2_PORT1_RST_B		0xb808
                        MX6UL_PAD_CSI_DATA06__SIM2_PORT1_SVEN		0xb808
                        MX6UL_PAD_CSI_DATA07__SIM2_PORT1_TRXD		0xb809
                        MX6UL_PAD_CSI_DATA02__GPIO4_IO23		0x3008
                    >;
                };

                pinctrl_spi4: spi4grp {
                    fsl,pins = <
                        MX6UL_PAD_BOOT_MODE0__GPIO5_IO10	0x70a1
                        MX6UL_PAD_BOOT_MODE1__GPIO5_IO11	0x70a1
                        MX6UL_PAD_SNVS_TAMPER7__GPIO5_IO07	0x70a1
                        MX6UL_PAD_SNVS_TAMPER8__GPIO5_IO08	0x80000000
                    >;
                };

                pinctrl_uart1: uart1grp {
                    fsl,pins = <
                        MX6UL_PAD_UART1_TX_DATA__UART1_DCE_TX 0x1b0b1
                        MX6UL_PAD_UART1_RX_DATA__UART1_DCE_RX 0x1b0b1
                    >;
                };

                pinctrl_usb_otg1: usbotg1grp {
                    fsl,pins = <
                        MX6UL_PAD_GPIO1_IO00__ANATOP_OTG1_ID	0x17059
                    >;
                };

                pinctrl_usdhc1: usdhc1grp {
                    fsl,pins = <
                        MX6UL_PAD_SD1_CMD__USDHC1_CMD     	0x17059
                        MX6UL_PAD_SD1_CLK__USDHC1_CLK     	0x10059
                        MX6UL_PAD_SD1_DATA0__USDHC1_DATA0 	0x17059
                        MX6UL_PAD_SD1_DATA1__USDHC1_DATA1 	0x17059
                        MX6UL_PAD_SD1_DATA2__USDHC1_DATA2 	0x17059
                        MX6UL_PAD_SD1_DATA3__USDHC1_DATA3 	0x17059
                        MX6UL_PAD_UART1_RTS_B__GPIO1_IO19       0x17059 /* SD1 CD */
                        MX6UL_PAD_GPIO1_IO05__USDHC1_VSELECT    0x17059 /* SD1 VSELECT */
                    >;
                };

                pinctrl_usdhc1_100mhz: usdhc1-100mhz-grp {
                    fsl,pins = <
                        MX6UL_PAD_SD1_CMD__USDHC1_CMD     0x170b9
                        MX6UL_PAD_SD1_CLK__USDHC1_CLK     0x100b9
                        MX6UL_PAD_SD1_DATA0__USDHC1_DATA0 0x170b9
                        MX6UL_PAD_SD1_DATA1__USDHC1_DATA1 0x170b9
                        MX6UL_PAD_SD1_DATA2__USDHC1_DATA2 0x170b9
                        MX6UL_PAD_SD1_DATA3__USDHC1_DATA3 0x170b9

                    >;
                };

                pinctrl_usdhc1_200mhz: usdhc1-200mhz-grp {
                    fsl,pins = <
                        MX6UL_PAD_SD1_CMD__USDHC1_CMD     0x170f9
                        MX6UL_PAD_SD1_CLK__USDHC1_CLK     0x100f9
                        MX6UL_PAD_SD1_DATA0__USDHC1_DATA0 0x170f9
                        MX6UL_PAD_SD1_DATA1__USDHC1_DATA1 0x170f9
                        MX6UL_PAD_SD1_DATA2__USDHC1_DATA2 0x170f9
                        MX6UL_PAD_SD1_DATA3__USDHC1_DATA3 0x170f9
                    >;
                };

                pinctrl_usdhc2: usdhc2grp {
                    fsl,pins = <
                        MX6UL_PAD_NAND_RE_B__USDHC2_CLK     0x10079
                        MX6UL_PAD_NAND_WE_B__USDHC2_CMD     0x17079
                        MX6UL_PAD_NAND_DATA00__USDHC2_DATA0 0x17079
                        MX6UL_PAD_NAND_DATA01__USDHC2_DATA1 0x17079
                        MX6UL_PAD_NAND_DATA02__USDHC2_DATA2 0x17079
                        MX6UL_PAD_NAND_DATA03__USDHC2_DATA3 0x17079
                        MX6UL_PAD_NAND_DATA04__USDHC2_DATA4 0x17079
                        MX6UL_PAD_NAND_DATA05__USDHC2_DATA5 0x17079
                        MX6UL_PAD_NAND_DATA06__USDHC2_DATA6 0x17079
                        MX6UL_PAD_NAND_DATA07__USDHC2_DATA7 0x17079
                        MX6UL_PAD_NAND_ALE__USDHC2_RESET_B  0x17079  /* eMMC RESET */
                    >;
                };

                pinctrl_usdhc2_100mhz: usdhc2-100mhz-grp {
                    fsl,pins = <
                        MX6UL_PAD_NAND_RE_B__USDHC2_CLK     0x100b9
                        MX6UL_PAD_NAND_WE_B__USDHC2_CMD     0x170b9
                        MX6UL_PAD_NAND_DATA00__USDHC2_DATA0 0x170b9
                        MX6UL_PAD_NAND_DATA01__USDHC2_DATA1 0x170b9
                        MX6UL_PAD_NAND_DATA02__USDHC2_DATA2 0x170b9
                        MX6UL_PAD_NAND_DATA03__USDHC2_DATA3 0x170b9
                        MX6UL_PAD_NAND_DATA04__USDHC2_DATA4 0x170b9
                        MX6UL_PAD_NAND_DATA05__USDHC2_DATA5 0x170b9
                        MX6UL_PAD_NAND_DATA06__USDHC2_DATA6 0x170b9
                        MX6UL_PAD_NAND_DATA07__USDHC2_DATA7 0x170b9
                        MX6UL_PAD_NAND_ALE__USDHC2_RESET_B  0x170b9
                    >;
                };

                pinctrl_usdhc2_200mhz: usdhc2-200mhz-grp {
                    fsl,pins = <
                        MX6UL_PAD_NAND_RE_B__USDHC2_CLK     0x100f9
                        MX6UL_PAD_NAND_WE_B__USDHC2_CMD     0x170f9
                        MX6UL_PAD_NAND_DATA00__USDHC2_DATA0 0x170f9
                        MX6UL_PAD_NAND_DATA01__USDHC2_DATA1 0x170f9
                        MX6UL_PAD_NAND_DATA02__USDHC2_DATA2 0x170f9
                        MX6UL_PAD_NAND_DATA03__USDHC2_DATA3 0x170f9
                        MX6UL_PAD_NAND_DATA04__USDHC2_DATA4 0x170f9
                        MX6UL_PAD_NAND_DATA05__USDHC2_DATA5 0x170f9
                        MX6UL_PAD_NAND_DATA06__USDHC2_DATA6 0x170f9
                        MX6UL_PAD_NAND_DATA07__USDHC2_DATA7 0x170f9
                        MX6UL_PAD_NAND_ALE__USDHC2_RESET_B  0x170f9
                    >;
                };

                pinctrl_wdog: wdoggrp {
                    fsl,pins = <
                        MX6UL_PAD_LCD_RESET__WDOG1_WDOG_ANY    0x30b0
                    >;
                };
			};

			gpr: iomuxc-gpr@20e4000 {
				compatible = "fsl,imx6ul-iomuxc-gpr",
					     "fsl,imx6q-iomuxc-gpr", "syscon";
				reg = <0x020e4000 0x4000>;
			};

			gpt2: timer@20e8000 {
				compatible = "fsl,imx6ul-gpt", "fsl,imx6sx-gpt";
				reg = <0x020e8000 0x4000>;
				interrupts = <GIC_SPI 109 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_GPT2_BUS>,
					 <&clks IMX6UL_CLK_GPT2_SERIAL>;
				clock-names = "ipg", "per";
				status = "disabled";
			};

			sdma: dma-controller@20ec000 {
				compatible = "fsl,imx6ul-sdma", "fsl,imx6q-sdma",
					     "fsl,imx35-sdma";
				reg = <0x020ec000 0x4000>;
				interrupts = <GIC_SPI 2 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_IPG>,
					 <&clks IMX6UL_CLK_SDMA>;
				clock-names = "ipg", "ahb";
				#dma-cells = <3>;
				fsl,sdma-ram-script-name = "imx/sdma/sdma-imx6q.bin";
			};

			pwm5: pwm@20f0000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x020f0000 0x4000>;
				interrupts = <GIC_SPI 114 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM5>,
					 <&clks IMX6UL_CLK_PWM5>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm6: pwm@20f4000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x020f4000 0x4000>;
				interrupts = <GIC_SPI 115 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM6>,
					 <&clks IMX6UL_CLK_PWM6>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm7: pwm@20f8000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x020f8000 0x4000>;
				interrupts = <GIC_SPI 116 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM7>,
					 <&clks IMX6UL_CLK_PWM7>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};

			pwm8: pwm@20fc000 {
				compatible = "fsl,imx6ul-pwm", "fsl,imx27-pwm";
				reg = <0x020fc000 0x4000>;
				interrupts = <GIC_SPI 117 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PWM8>,
					 <&clks IMX6UL_CLK_PWM8>;
				clock-names = "ipg", "per";
				#pwm-cells = <3>;
				status = "disabled";
			};
		};

		aips2: bus@2100000 {
			compatible = "fsl,aips-bus", "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x02100000 0x100000>;
			ranges;

			usbotg1: usb@2184000 {
				compatible = "fsl,imx6ul-usb", "fsl,imx27-usb";
				reg = <0x02184000 0x200>;
				interrupts = <GIC_SPI 43 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_USBOH3>;
				fsl,usbphy = <&usbphy1>;
				fsl,usbmisc = <&usbmisc 0>;
				ahb-burst-config = <0x0>;
				tx-burst-size-dword = <0x10>;
				rx-burst-size-dword = <0x10>;
				dr_mode = "otg";
                pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_usb_otg1>;
                status = "okay";
			};

			usbotg2: usb@2184200 {
				compatible = "fsl,imx6ul-usb", "fsl,imx27-usb";
				reg = <0x02184200 0x200>;
				interrupts = <GIC_SPI 42 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_USBOH3>;
				fsl,usbphy = <&usbphy2>;
				fsl,usbmisc = <&usbmisc 1>;
				ahb-burst-config = <0x0>;
				tx-burst-size-dword = <0x10>;
				rx-burst-size-dword = <0x10>;
				dr_mode = "host";
                disable-over-current;
                status = "okay";
			};

			usbmisc: usbmisc@2184800 {
				#index-cells = <1>;
				compatible = "fsl,imx6ul-usbmisc", "fsl,imx6q-usbmisc";
				reg = <0x02184800 0x200>;
			};

			fec1: ethernet@2188000 {
				compatible = "fsl,imx6ul-fec", "fsl,imx6q-fec";
				reg = <0x02188000 0x4000>;
				interrupt-names = "int0", "pps";
				interrupts = <GIC_SPI 118 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 119 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_ENET>,
					 <&clks IMX6UL_CLK_ENET_AHB>,
					 <&clks IMX6UL_CLK_ENET_PTP>,
					 <&clks IMX6UL_CLK_ENET1_REF_SEL>;
				clock-names = "ipg", "ahb", "ptp",
					      "enet_clk_ref";
				fsl,num-tx-queues = <1>;
				fsl,num-rx-queues = <1>;
				fsl,stop-mode = <&gpr 0x10 3>;
				fsl,magic-packet;
				nvmem-cells = <&fec1_mac_addr>;
				nvmem-cell-names = "mac-address";
				pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_enet1
                                &pinctrl_enet1_reset>;
                phy-reset-gpios = <&gpio5 7 GPIO_ACTIVE_LOW>;
                phy-reset-duration = <200>;
                phy-mode = "rmii";
                phy-handle = <&ethphy0>;
                phy-supply = <&reg_peri_3v3>;
                status = "okay";
			};

			usdhc1: mmc@2190000 {
				compatible = "fsl,imx6ull-usdhc", "fsl,imx6sx-usdhc";
				reg = <0x02190000 0x4000>;
				interrupts = <GIC_SPI 22 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_USDHC1>,
					 <&clks IMX6UL_CLK_USDHC1>,
					 <&clks IMX6UL_CLK_USDHC1>;
				clock-names = "ipg", "ahb", "per";
				fsl,tuning-step = <2>;
				fsl,tuning-start-tap = <20>;
				bus-width = <4>;
				pinctrl-names = "default", "state_100mhz", "state_200mhz";
                pinctrl-0 = <&pinctrl_usdhc1>;
                pinctrl-1 = <&pinctrl_usdhc1_100mhz>;
                pinctrl-2 = <&pinctrl_usdhc1_200mhz>;
                cd-gpios = <&gpio1 19 GPIO_ACTIVE_LOW>;
                keep-power-in-suspend;
                wakeup-source;
                vmmc-supply = <&reg_sd1_vmmc>;
                status = "okay";
			};

			usdhc2: mmc@2194000 {
				compatible = "fsl,imx6ull-usdhc", "fsl,imx6sx-usdhc";
				reg = <0x02194000 0x4000>;
				interrupts = <GIC_SPI 23 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_USDHC2>,
					 <&clks IMX6UL_CLK_USDHC2>,
					 <&clks IMX6UL_CLK_USDHC2>;
				clock-names = "ipg", "ahb", "per";
				fsl,tuning-step = <2>;
				fsl,tuning-start-tap = <20>;
				pinctrl-names = "default", "state_100mhz", "state_200mhz";
                pinctrl-0 = <&pinctrl_usdhc2>;
                pinctrl-1 = <&pinctrl_usdhc2_100mhz>;
                pinctrl-2 = <&pinctrl_usdhc2_200mhz>;
                bus-width = <8>;
                no-1-8-v;
                broken-cd;
                keep-power-in-suspend;
                wakeup-source;
                status = "okay";
			};

			adc1: adc@2198000 {
				compatible = "fsl,imx6ul-adc", "fsl,vf610-adc";
				reg = <0x02198000 0x4000>;
				interrupts = <GIC_SPI 100 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_ADC1>;
				clock-names = "adc";
				fsl,adck-max-frequency = <30000000>, <40000000>,
							 <20000000>;
				status = "disabled";
			};

			i2c1: i2c@21a0000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx6ul-i2c", "fsl,imx21-i2c";
				reg = <0x021a0000 0x4000>;
				interrupts = <GIC_SPI 36 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_I2C1>;
				clock-frequency = <100000>;
                pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_i2c1>;
                status = "okay";
                
				codec: wm8960@1a {
					#sound-dai-cells = <0>;
					compatible = "wlf,wm8960";
					reg = <0x1a>;
					wlf,shared-lrclk;
					wlf,hp-cfg = <3 2 3>;
					wlf,gpio-cfg = <1 3>;
					clocks = <&clks IMX6UL_CLK_SAI2>;
					clock-names = "mclk";
				};

                ap3216c@1e {
                    compatible = "zexuan,ap3216c";
                    reg = <0x1e>;
                };
			};

			i2c2: i2c@21a4000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx6ul-i2c", "fsl,imx21-i2c";
				reg = <0x021a4000 0x4000>;
				interrupts = <GIC_SPI 37 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_I2C2>;
                clock-frequency = <100000>;
                pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_i2c2>;
				status = "okay";

				gt9147:gt9147@14 {
					compatible = "goodix,gt9147", "goodix,gt9xx";
					reg = <0x14>;
					pinctrl-names = "default";
					pinctrl-0 = <&pinctrl_tsc
							&pinctrl_tsc_reset>;
					interrupt-parent = <&gpio1>;
					interrupts = <9 0>;
					reset-gpios = <&gpio5 9 GPIO_ACTIVE_LOW>;
					interrupt-gpios = <&gpio1 9 GPIO_ACTIVE_LOW>;
					status = "okay"; 
				};

			};

			i2c3: i2c@21a8000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx6ul-i2c", "fsl,imx21-i2c";
				reg = <0x021a8000 0x4000>;
				interrupts = <GIC_SPI 38 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_I2C3>;
				status = "disabled";
			};

			memory-controller@21b0000 {
				compatible = "fsl,imx6ul-mmdc", "fsl,imx6q-mmdc";
				reg = <0x021b0000 0x4000>;
				clocks = <&clks IMX6UL_CLK_MMDC_P0_IPG>;
			};

			weim: memory-controller@21b8000 {
				#address-cells = <2>;
				#size-cells = <1>;
				compatible = "fsl,imx6ul-weim", "fsl,imx6q-weim";
				reg = <0x021b8000 0x4000>;
				interrupts = <GIC_SPI 14 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_EIM>;
				fsl,weim-cs-gpr = <&gpr>;
				status = "disabled";
			};

			ocotp: efuse@21bc000 {
				#address-cells = <1>;
				#size-cells = <1>;
				compatible = "fsl,imx6ull-ocotp", "syscon";
				reg = <0x021bc000 0x4000>;
				clocks = <&clks IMX6UL_CLK_OCOTP>;

				tempmon_calib: calib@38 {
					reg = <0x38 4>;
				};

				tempmon_temp_grade: temp-grade@20 {
					reg = <0x20 4>;
				};

				cpu_speed_grade: speed-grade@10 {
					reg = <0x10 4>;
				};

				fec1_mac_addr: mac-addr@88 {
					reg = <0x88 6>;
				};

				fec2_mac_addr: mac-addr@8e {
					reg = <0x8e 6>;
				};
			};

			lcdif: lcdif@21c8000 {
				compatible = "fsl,imx6ul-lcdif", "fsl,imx6sx-lcdif";
				reg = <0x021c8000 0x4000>;
				interrupts = <GIC_SPI 5 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_LCDIF_PIX>,
					 <&clks IMX6UL_CLK_LCDIF_APB>,
					 <&clks IMX6UL_CLK_DUMMY>;
				clock-names = "pix", "axi", "disp_axi";
				assigned-clocks = <&clks IMX6UL_CLK_LCDIF_PRE_SEL>;
                assigned-clock-parents = <&clks IMX6UL_CLK_PLL5_VIDEO_DIV>;
                pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_lcdif_dat
                        &pinctrl_lcdif_ctrl>;
                status = "okay";

                display-timings {
                    native-mode = <&timing0>;
                    timing0: timing0 {
                        bits-per-pixel = <24>;
                        bus-width = <24>;
                    };
                };

                port {
                    display_out: endpoint {
                        remote-endpoint = <&panel_in>;
                    };
                };
			};

			pxp: pxp@21cc000 {
				compatible = "fsl,imx6ull-pxp";
				reg = <0x021cc000 0x4000>;
				interrupts = <GIC_SPI 8 IRQ_TYPE_LEVEL_HIGH>,
		                    <GIC_SPI 18 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_PXP>;
				clock-names = "axi";
			};

			qspi: spi@21e0000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx6ul-qspi";
				reg = <0x021e0000 0x4000>, <0x60000000 0x10000000>;
				reg-names = "QuadSPI", "QuadSPI-memory";
				interrupts = <GIC_SPI 107 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_QSPI>,
					 <&clks IMX6UL_CLK_QSPI>;
				clock-names = "qspi_en", "qspi";
				pinctrl-names = "default";
                pinctrl-0 = <&pinctrl_qspi>;
                status = "okay";

                flash0: flash@0 {
                    #address-cells = <1>;
                    #size-cells = <1>;
                    compatible = "micron,n25q256a", "jedec,spi-nor";
                    spi-max-frequency = <29000000>;
                    spi-rx-bus-width = <4>;
                    spi-tx-bus-width = <1>;
                    reg = <0>;
                };
			};

			wdog3: watchdog@21e4000 {
				compatible = "fsl,imx6ul-wdt", "fsl,imx21-wdt";
				reg = <0x021e4000 0x4000>;
				interrupts = <GIC_SPI 11 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_WDOG3>;
				status = "disabled";
			};

			uart3: serial@21ec000 {
				compatible = "fsl,imx6ul-uart",
					     "fsl,imx6q-uart";
				reg = <0x021ec000 0x4000>;
				interrupts = <GIC_SPI 28 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_UART3_IPG>,
					 <&clks IMX6UL_CLK_UART3_SERIAL>;
				clock-names = "ipg", "per";
				pinctrl-names = "default";
				pinctrl-0 = <&pinctrl_uart3>;
				status = "okay";
			};

			uart4: serial@21f0000 {
				compatible = "fsl,imx6ul-uart",
					     "fsl,imx6q-uart";
				reg = <0x021f0000 0x4000>;
				interrupts = <GIC_SPI 29 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_UART4_IPG>,
					 <&clks IMX6UL_CLK_UART4_SERIAL>;
				clock-names = "ipg", "per";
				status = "disabled";
			};

			uart5: serial@21f4000 {
				compatible = "fsl,imx6ul-uart",
					     "fsl,imx6q-uart";
				reg = <0x021f4000 0x4000>;
				interrupts = <GIC_SPI 30 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_UART5_IPG>,
					 <&clks IMX6UL_CLK_UART5_SERIAL>;
				clock-names = "ipg", "per";
				status = "disabled";
			};

			i2c4: i2c@21f8000 {
				#address-cells = <1>;
				#size-cells = <0>;
				compatible = "fsl,imx6ul-i2c", "fsl,imx21-i2c";
				reg = <0x021f8000 0x4000>;
				interrupts = <GIC_SPI 35 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_I2C4>;
				status = "disabled";
			};

			uart6: serial@21fc000 {
				compatible = "fsl,imx6ul-uart",
					     "fsl,imx6q-uart";
				reg = <0x021fc000 0x4000>;
				interrupts = <GIC_SPI 17 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_UART6_IPG>,
					 <&clks IMX6UL_CLK_UART6_SERIAL>;
				clock-names = "ipg", "per";
				status = "disabled";
			};
		};
        aips3: bus@2200000 {
			compatible = "fsl,aips-bus", "simple-bus";
			#address-cells = <1>;
			#size-cells = <1>;
			reg = <0x02200000 0x100000>;
			ranges;

			dcp: crypto@2280000 {
				compatible = "fsl,imx6ull-dcp", "fsl,imx28-dcp";
				reg = <0x02280000 0x4000>;
				interrupts = <GIC_SPI 46 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 47 IRQ_TYPE_LEVEL_HIGH>,
					     <GIC_SPI 48 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6ULL_CLK_DCP_CLK>;
				clock-names = "dcp";
			};

			rngb: rng@2284000 {
				compatible = "fsl,imx6ull-rngb", "fsl,imx25-rngb";
				reg = <0x02284000 0x4000>;
				interrupts = <GIC_SPI 6 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_DUMMY>;
			};

			iomuxc_snvs: pinctrl@2290000 {
				compatible = "fsl,imx6ull-iomuxc-snvs";
				reg = <0x02290000 0x4000>;
                pinctrl_enet1_reset: enet1resetgrp {
                    fsl,pins = <
                        MX6ULL_PAD_SNVS_TAMPER7__GPIO5_IO07 0x10B0
                    >;
                };

                pinctrl_enet2_reset: enet2resetgrp {
                    fsl,pins = <
                        MX6ULL_PAD_SNVS_TAMPER8__GPIO5_IO08 0x10B0
                    >;
                };
				pinctrl_tsc_reset: tsc_reset {
					fsl,pins = <
						MX6ULL_PAD_SNVS_TAMPER9__GPIO5_IO09 0x10B0
					>;
				};
			};

			uart8: serial@2288000 {
				compatible = "fsl,imx6ul-uart",
					     "fsl,imx6q-uart";
				reg = <0x02288000 0x4000>;
				interrupts = <GIC_SPI 40 IRQ_TYPE_LEVEL_HIGH>;
				clocks = <&clks IMX6UL_CLK_UART8_IPG>,
					 <&clks IMX6UL_CLK_UART8_SERIAL>;
				clock-names = "ipg", "per";
				status = "disabled";
			};
		};
	};
};
