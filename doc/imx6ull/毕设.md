#include <QWidget>
#include <QTcpSocket>

namespace Ui {
class Client;
}

class Client : public QWidget
{
    Q_OBJECT

public:
    explicit Client(QWidget *parent = 0);
    ~Client();

private slots:
    void on_connetBtn_clicked();

    void receiveMessage();

    void on_sendBtn_clicked();

private:
    Ui::Client *ui;

    QTcpSocket * socket; //必须new才能实例化
};

Client::Client(QWidget *parent) :
    QWidget(parent),
    ui(new Ui::Client)
{
    ui->setupUi(this);

    setWindowTitle("TCP客户端");
}

Client::~Client()
{
    delete ui;
}

void Client::on_connetBtn_clicked()
{
    socket = new QTcpSocket(this);

    //连接服务器 IP+端口
    socket->connectToHost(ui->IPlineEdit->text(),ui->portlineEdit->text().toInt());

    //接收服务端发来的消息
    connect(socket,&QTcpSocket::readyRead,this,[=](){
        this->receiveMessage();
    });
}

//接收服务端来的信息
void Client::receiveMessage()
{
    QByteArray receivedata = socket->readAll(); //接受的是字符流
    QString str = receivedata.data(); //字符流转换为普通文本
    ui->receivetextEdit->setText(str);
}

//客户端发送信息出去
void Client::on_sendBtn_clicked()
{
    QString sendMessage = ui->sendtextEdit->toPlainText();//普通文本

    socket->write(sendMessage.toUtf8()); //toUtf8()中文不乱码
}




第一阶段：
实现基本的Dock栏
完成页面加载机制
添加简单的应用示例
第二阶段：
添加页面管理功能
实现应用启动器
优化交互动画
第三阶段：
添加系统功能
实现多任务管理
优化性能
第四阶段：
添加高级特性
完善用户体验
进行性能优化