#include <iostream>
#include <string>
#include <map>
#include <algorithm>
#include <cctype>

// 反射机制核心类
class Class {
public:
    typedef void* pObj;
    typedef void* (*PFnCreateObj)(void);
    typedef std::map<std::string, PFnCreateObj> MpFnObj;

public:
    inline static pObj forName(const std::string& className) {
        pObj tmp = find(className); // 先在区分大小写集合查询
        if(tmp) return tmp;
        // 没找到则到不区分大小写集合内查询
        return findIgnoreCase(className);
    }

    inline static Class& getInstance() {
        static Class classFactory;
        return classFactory;
    }

    // 显示所有注册的类
    static void showRegisteredClasses() {
        std::cout << "已注册的类:" << std::endl;
        for (const auto& pair : getInstance().mpClassFn) {
            std::cout << "- " << pair.first << std::endl;
        }
    }

private:
    MpFnObj mpClassFn;

    // 查找类（区分大小写）
    static pObj find(const std::string& className) {
        auto& instance = getInstance();
        auto it = instance.mpClassFn.find(className);
        if (it != instance.mpClassFn.end()) {
            return it->second();
        }
        return nullptr;
    }

    // 查找类（不区分大小写）
    static pObj findIgnoreCase(const std::string& className) {
        auto& instance = getInstance();
        std::string lowerClassName = className;
        std::transform(lowerClassName.begin(), lowerClassName.end(),
                      lowerClassName.begin(), ::tolower);

        for (const auto& pair : instance.mpClassFn) {
            std::string lowerRegisteredName = pair.first;
            std::transform(lowerRegisteredName.begin(), lowerRegisteredName.end(),
                          lowerRegisteredName.begin(), ::tolower);

            if (lowerRegisteredName == lowerClassName) {
                return pair.second();
            }
        }
        return nullptr;
    }

public:
    // 辅助注册类
    class Register {
    public:
        Register(const std::string& className, PFnCreateObj ptrCreateFn) {
            getInstance().mpClassFn[className] = ptrCreateFn;
        }
    };
};

// 反射类注册宏
#define REFLECT_CLASS_REGISTER(className) \
    namespace reflect { \
        className* g_objectRefClassCreator##className() { return new className; } \
        Class::Register g_creatorRefClassRegister##className(#className, (Class::PFnCreateObj)g_objectRefClassCreator##className); \
    }

// 文件传输基类
class FileTransferBase {
public:
    virtual ~FileTransferBase() = default;
    virtual void transfer(const std::string& source, const std::string& target) = 0;
    virtual std::string getType() const = 0;
    virtual void showInfo() const = 0;
};

// SFTP传输实现
class SftpTransfer : public FileTransferBase {
public:
    void transfer(const std::string& source, const std::string& target) override {
        std::cout << "[SFTP] 开始传输文件..." << std::endl;
        std::cout << "[SFTP] 源文件: " << source << std::endl;
        std::cout << "[SFTP] 目标文件: " << target << std::endl;
        std::cout << "[SFTP] 使用SSH协议进行安全传输" << std::endl;
        std::cout << "[SFTP] 传输完成!" << std::endl;
    }

    std::string getType() const override {
        return "SFTP";
    }

    void showInfo() const override {
        std::cout << "SFTP传输 - 基于SSH的安全文件传输协议" << std::endl;
    }
};

// SCP传输实现
class ScpTransfer : public FileTransferBase {
public:
    void transfer(const std::string& source, const std::string& target) override {
        std::cout << "[SCP] 开始传输文件..." << std::endl;
        std::cout << "[SCP] 源文件: " << source << std::endl;
        std::cout << "[SCP] 目标文件: " << target << std::endl;
        std::cout << "[SCP] 使用SCP命令进行传输" << std::endl;
        std::cout << "[SCP] 传输完成!" << std::endl;
    }

    std::string getType() const override {
        return "SCP";
    }

    void showInfo() const override {
        std::cout << "SCP传输 - 基于SSH的安全复制协议" << std::endl;
    }
};

// Java中转传输实现
class JavaTransfer : public FileTransferBase {
public:
    void transfer(const std::string& source, const std::string& target) override {
        std::cout << "[Java中转] 开始传输文件..." << std::endl;
        std::cout << "[Java中转] 源文件: " << source << std::endl;
        std::cout << "[Java中转] 目标文件: " << target << std::endl;
        std::cout << "[Java中转] 通过Java应用服务器中转传输" << std::endl;
        std::cout << "[Java中转] 传输完成!" << std::endl;
    }

    std::string getType() const override {
        return "Java中转";
    }

    void showInfo() const override {
        std::cout << "Java中转传输 - 通过Java应用服务器进行文件中转" << std::endl;
    }
};

// 注册所有传输类到反射系统
REFLECT_CLASS_REGISTER(SftpTransfer)
REFLECT_CLASS_REGISTER(ScpTransfer)
REFLECT_CLASS_REGISTER(JavaTransfer)

// 配置解析器
class ConfigParser {
private:
    std::map<std::string, std::string> configs;

public:
    void parseXMLConfig() {
        // 模拟解析XML配置
        std::cout << "=== 解析XML配置 ===" << std::endl;
        std::cout << "XML配置内容:" << std::endl;
        std::cout << R"(<?xml version="1.0" encoding="UTF-8"?>
<config key="sys">
    <item key="file.trans.type" value="1" desc="文件传输方式：1、sftp,2、scp,3、java中转">
        <subItem key="encrypt" value="1" desc="加密类型：1非对称加密，2对称加密，3、非对称+对称" />
    </item>
</config>)" << std::endl;

        // 简化的配置解析（实际项目中应使用XML解析库）
        configs["file.trans.type"] = "1";  // 从XML中解析得到
        configs["encrypt"] = "1";          // 从XML中解析得到

        std::cout << "解析结果:" << std::endl;
        std::cout << "- file.trans.type = " << configs["file.trans.type"]
                  << " (" << getTransferTypeDesc(configs["file.trans.type"]) << ")" << std::endl;
        std::cout << "- encrypt = " << configs["encrypt"]
                  << " (" << getEncryptTypeDesc(configs["encrypt"]) << ")" << std::endl;
        std::cout << std::endl;
    }

    std::string getConfig(const std::string& key) const {
        auto it = configs.find(key);
        return it != configs.end() ? it->second : "";
    }

    std::string getTransferClassName(const std::string& type) const {
        if (type == "1") return "SftpTransfer";
        if (type == "2") return "ScpTransfer";
        if (type == "3") return "JavaTransfer";
        return "";
    }

private:
    std::string getTransferTypeDesc(const std::string& type) const {
        if (type == "1") return "SFTP";
        if (type == "2") return "SCP";
        if (type == "3") return "Java中转";
        return "未知";
    }

    std::string getEncryptTypeDesc(const std::string& type) const {
        if (type == "1") return "非对称加密";
        if (type == "2") return "对称加密";
        if (type == "3") return "非对称+对称";
        return "未知";
    }
};

// 文件传输业务框架
class FileTransferFramework {
private:
    ConfigParser parser;
    FileTransferBase* currentTransfer;

public:
    FileTransferFramework() : currentTransfer(nullptr) {}

    ~FileTransferFramework() {
        if (currentTransfer) {
            delete currentTransfer;
        }
    }

    void initialize() {
        std::cout << "=== 文件传输框架初始化 ===" << std::endl;

        // 显示已注册的类
        Class::showRegisteredClasses();
        std::cout << std::endl;

        // 解析配置
        parser.parseXMLConfig();

        // 根据配置使用反射创建传输对象
        std::string transferType = parser.getConfig("file.trans.type");
        std::string className = parser.getTransferClassName(transferType);

        std::cout << "=== 使用反射机制创建对象 ===" << std::endl;
        std::cout << "根据配置 file.trans.type=" << transferType
                  << " 创建类: " << className << std::endl;

        // 使用反射机制动态创建对象
        void* obj = Class::forName(className);
        if (obj) {
            currentTransfer = static_cast<FileTransferBase*>(obj);
            std::cout << "反射创建对象成功: " << currentTransfer->getType() << std::endl;
            currentTransfer->showInfo();
        } else {
            std::cout << "反射创建对象失败: 未找到类 " << className << std::endl;
        }
        std::cout << std::endl;
    }

    void demonstrateTransfer() {
        std::cout << "=== 演示文件传输功能 ===" << std::endl;

        if (currentTransfer) {
            // 演示文件传输
            currentTransfer->transfer("/local/document.pdf", "/remote/backup/document.pdf");
            std::cout << std::endl;

            // 再次演示不同文件的传输
            currentTransfer->transfer("/home/<USER>/data.txt", "/server/shared/data.txt");
        } else {
            std::cout << "传输对象未初始化，无法执行传输操作" << std::endl;
        }
        std::cout << std::endl;
    }

    void demonstrateReflection() {
        std::cout << "=== 演示反射机制的灵活性 ===" << std::endl;

        // 演示动态创建不同类型的传输对象
        std::string testClasses[] = {"SftpTransfer", "ScpTransfer", "JavaTransfer"};

        for (const std::string& className : testClasses) {
            std::cout << "尝试创建: " << className << std::endl;
            void* obj = Class::forName(className);
            if (obj) {
                FileTransferBase* transfer = static_cast<FileTransferBase*>(obj);
                std::cout << "  创建成功: " << transfer->getType() << std::endl;
                transfer->showInfo();
                delete transfer;
            } else {
                std::cout << "  创建失败" << std::endl;
            }
            std::cout << std::endl;
        }
    }
};

int main() {
    std::cout << "基于反射机制的文件传输业务框架" << std::endl;
    std::cout << "======================================" << std::endl;
    std::cout << std::endl;

    FileTransferFramework framework;

    // 初始化框架
    framework.initialize();

    // 演示文件传输功能
    framework.demonstrateTransfer();

    // 演示反射机制的灵活性
    framework.demonstrateReflection();

    std::cout << "框架演示完成!" << std::endl;

    return 0;
}