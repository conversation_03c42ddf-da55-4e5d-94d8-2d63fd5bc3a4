#include <gtest/gtest.h>
#include <string>
#include <vector>
#include <cstring>
#include <iostream>

#define _ZERO_MEM(ptr, size) memset(ptr, 0, size)
#define FILE_PATH_OPT_CHAR '/'
#define MAX_FILE_NAME_LEN 255

// 模拟字符串分割函数
void __sm_split(const char* str, char delimiter, std::vector<std::string>& result)
{
    std::string s(str);
    std::string token;
    size_t pos = 0;
    
    result.clear();
    while ((pos = s.find(delimiter)) != std::string::npos) {
        token = s.substr(0, pos);
        if (!token.empty()) {
            result.push_back(token);
        }
        s.erase(0, pos + 1);
    }
    if (!s.empty()) {
        result.push_back(s);
    }
}

// 模拟FILE_PROPERTY_INF结构体
struct MockFileInfo
{
    char chName[MAX_FILE_NAME_LEN];
    char cReserve1[MAX_FILE_NAME_LEN];
    
    MockFileInfo()
    {
        _ZERO_MEM(chName, MAX_FILE_NAME_LEN);
        _ZERO_MEM(cReserve1, MAX_FILE_NAME_LEN);
    }
};

// 测试函数：模拟选中代码的文件路径处理逻辑
std::string ProcessFilePath(const std::string& fullPath, const std::string& relativePath, const std::string& findName)
{
    MockFileInfo fileInfo;
    char cFileName[255] = "";
    char cTemp[500] = "";
    
    // 复制完整路径到结构体
    strcpy(fileInfo.chName, fullPath.c_str());
    
    std::cout << "=== 处理文件路径 ===" << std::endl;
    std::cout << "输入完整路径: " << fullPath << std::endl;
    std::cout << "查找名称: " << findName << std::endl;
    std::cout << "相对路径: " << relativePath << std::endl;
    
    // 1. 从完整路径中提取文件名
    std::vector<std::string> vStr;
    _ZERO_MEM(cFileName, 255);
    __sm_split(fileInfo.chName, FILE_PATH_OPT_CHAR, vStr);
    
    std::cout << "路径分割结果: ";
    for (const auto& part : vStr) {
        std::cout << "[" << part << "] ";
    }
    std::cout << std::endl;
    
    if (!vStr.empty()) {
        memcpy(cFileName, vStr[vStr.size()-1].c_str(), vStr[vStr.size()-1].length());
    }
    
    std::cout << "提取的文件名: " << cFileName << std::endl;
    
    // 2. 清空原路径，只保留文件名
    _ZERO_MEM(fileInfo.chName, strlen(fileInfo.chName));
    memcpy(fileInfo.chName, cFileName, strlen(cFileName));
    
    std::cout << "处理后的chName: " << fileInfo.chName << std::endl;
    
    // 3. 设置相对路径到cReserve1
    _ZERO_MEM(fileInfo.cReserve1, strlen(fileInfo.cReserve1));
    memcpy(fileInfo.cReserve1, relativePath.c_str(), relativePath.length());
    
    std::cout << "设置的相对路径: " << fileInfo.cReserve1 << std::endl;
    
    // 4. 将相对路径和文件名合在一起
    _ZERO_MEM(cTemp, 500);
    sprintf(cTemp, "%s%s", fileInfo.cReserve1, fileInfo.chName);
    _ZERO_MEM(fileInfo.chName, MAX_FILE_NAME_LEN);
    memcpy(fileInfo.chName, cTemp, strlen(cTemp));
    
    std::cout << "最终合并结果: " << fileInfo.chName << std::endl;
    std::cout << "===================" << std::endl << std::endl;
    
    return std::string(fileInfo.chName);
}

class FilePathProcessingTest : public ::testing::Test
{
protected:
    void SetUp() override {}
    void TearDown() override {}
};

// 测试用例1：基本文件路径处理
TEST_F(FilePathProcessingTest, BasicFilePathProcessing)
{
    std::string fullPath = "/tmp/test_files/SettingUp/1/setting.txt";
    std::string relativePath = "/SettingUp/test";
    std::string findName = "/test";
    
    std::string result = ProcessFilePath(fullPath, relativePath, findName);
    
    // 验证结果：相对路径 + 文件名
    EXPECT_EQ(result, "/SettingUp/testsetting.txt");
}

// 测试用例2：深层目录结构
TEST_F(FilePathProcessingTest, DeepDirectoryStructure)
{
    std::string fullPath = "/var/nxdown/SettingUp/2/subdir/config/app.xml";
    std::string relativePath = "/SettingUp/";
    std::string findName = "/";
    
    std::string result = ProcessFilePath(fullPath, relativePath, findName);
    
    // 验证结果：只提取最后的文件名
    EXPECT_EQ(result, "/SettingUp/app.xml");
}

// 测试用例3：单级文件名
TEST_F(FilePathProcessingTest, SingleLevelFileName)
{
    std::string fullPath = "setting.txt";  // 没有路径分隔符
    std::string relativePath = "/SettingUp/test";
    std::string findName = "/test";
    
    std::string result = ProcessFilePath(fullPath, relativePath, findName);
    
    // 验证结果：整个字符串就是文件名
    EXPECT_EQ(result, "/SettingUp/testsetting.txt");
}

// 测试用例4：空文件名处理
TEST_F(FilePathProcessingTest, EmptyFileName)
{
    std::string fullPath = "/tmp/test_files/SettingUp/1/";  // 以斜杠结尾
    std::string relativePath = "/SettingUp/test";
    std::string findName = "/test";
    
    std::string result = ProcessFilePath(fullPath, relativePath, findName);
    
    // 验证结果：空文件名的情况
    EXPECT_EQ(result, "/SettingUp/test");
}

// 测试用例5：不同地址的相对路径
TEST_F(FilePathProcessingTest, DifferentAddressRelativePath)
{
    std::string fullPath = "/tmp/test_files/SettingUp/255/important.cfg";
    std::string relativePath = "/SettingUp/addr255";
    std::string findName = "/addr255";
    
    std::string result = ProcessFilePath(fullPath, relativePath, findName);
    
    EXPECT_EQ(result, "/SettingUp/addr255important.cfg");
}

// 测试用例6：复杂文件名
TEST_F(FilePathProcessingTest, ComplexFileName)
{
    std::string fullPath = "/home/<USER>/data/SettingUp/10/backup/system_config_v2.1.xml";
    std::string relativePath = "/SettingUp/backup";
    std::string findName = "/backup";
    
    std::string result = ProcessFilePath(fullPath, relativePath, findName);
    
    EXPECT_EQ(result, "/SettingUp/backupsystem_config_v2.1.xml");
}

// 测试用例7：验证相对路径的构建逻辑
TEST_F(FilePathProcessingTest, RelativePathConstruction)
{
    // 模拟原始代码中的相对路径构建
    std::string cFindName = "/test";
    char cFilePath[500] = "";
    char cTemp[500] = "";
    
    // 原始代码：sprintf(cTemp,"%s%s","/SettingUp",cFindName);
    sprintf(cTemp, "%s%s", "/SettingUp", cFindName.c_str());
    strcpy(cFilePath, cTemp);
    int nFilePathLen = strlen(cFilePath);
    
    std::cout << "构建的相对路径: " << cFilePath << std::endl;
    std::cout << "相对路径长度: " << nFilePathLen << std::endl;
    
    EXPECT_STREQ(cFilePath, "/SettingUp/test");
    EXPECT_EQ(nFilePathLen, 14);
}

// 测试用例8：验证memcpy的行为
TEST_F(FilePathProcessingTest, MemcpyBehavior)
{
    char cReserve1[MAX_FILE_NAME_LEN] = "";
    std::string relativePath = "/SettingUp/test";
    
    // 模拟原始代码：memcpy(ite->cReserve1,&cFilePath,nFilePathLen);
    // 注意：原始代码使用了&cFilePath，这可能是个bug，应该是cFilePath
    memcpy(cReserve1, relativePath.c_str(), relativePath.length());
    
    std::cout << "memcpy后的结果: " << cReserve1 << std::endl;
    
    EXPECT_STREQ(cReserve1, "/SettingUp/test");
}

int main(int argc, char** argv)
{
    ::testing::InitGoogleTest(&argc, argv);
    
    std::cout << "=== 测试文件路径处理逻辑 ===" << std::endl;
    std::cout << "验证从完整路径提取文件名，并与相对路径合并的逻辑" << std::endl;
    std::cout << "=========================================" << std::endl;
    
    return RUN_ALL_TESTS();
}
