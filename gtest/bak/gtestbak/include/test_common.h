#pragma once

#include <gtest/gtest.h>
#include <gmock/gmock.h>
#include <string>
#include <vector>
#include <cstring>
#include <cstdint>
#include <algorithm>
#include <cctype>

// 基础类型定义
typedef uint8_t u_int8;
typedef uint16_t u_int16;
typedef uint32_t u_int32;
typedef int32_t int32;

// 参数修饰符
#define IN
#define OUT

// 错误码定义
#define EC_PRO_CVT_FAIL -1
#define EC_PRO_CVT_NOSUPPORT -2

// 事件类型定义
#define NX_IED_EVENT_FILE_REPORT 1001

// 时间结构体
typedef struct _ASDU_TIME
{
    u_int32 nInfoHappenUtc;    // 事件发生UTC时间
    u_int16 nInfoHappenMs;     // 事件发生毫秒
    u_int32 nInfoRcvUtc;       // 事件接收UTC时间
    u_int16 nInfoRcvMs;        // 事件接收毫秒
    
    _ASDU_TIME()
    {
        nInfoHappenUtc = 0;
        nInfoHappenMs = 0;
        nInfoRcvUtc = 0;
        nInfoRcvMs = 0;
    }
} ASDU_TIME;

// 地址信息结构体
typedef struct _ASDU_ADDR
{
    u_int16 nSubstationAdd;    // 变电站地址
    u_int16 nAddr;             // 设备地址
    u_int8  nCpu;              // CPU号
    u_int8  nZone;             // 定值区号
    
    _ASDU_ADDR()
    {
        nSubstationAdd = 0;
        nAddr = 0;
        nCpu = 0;
        nZone = 0;
    }
} ASDU_ADDR;

// 信息体结构体
typedef struct _ASDU_INFO_OBJ
{
    u_int8 nFun;    // 功能类型
    u_int8 nInf;    // 信息序号
    u_int8 nDpi;    // DPI值
    
    _ASDU_INFO_OBJ()
    {
        nFun = 0;
        nInf = 0;
        nDpi = 0;
    }
} ASDU_INFO_OBJ;

// 协议帧体结构
typedef struct _PRO_FRAME_BODY
{
    u_int8  nType;              // 类型标识
    u_int8  nVsq;               // 可变结构限定词
    u_int8  nCot;               // 传送原因
    u_int16 nSubstationAdd;     // 变电站地址
    u_int16 nAddr;              // 设备地址
    u_int8  nCpu;               // CPU号
    u_int8  nZone;              // 定值区号
    u_int8  nFun;               // 功能类型
    u_int8  nInf;               // 信息序号
    u_int8  nRii;               // 返回信息标识符
    std::vector<u_int8> vVarData; // 可变数据
    
    _PRO_FRAME_BODY()
    {
        nType = 0;
        nVsq = 0;
        nCot = 0;
        nSubstationAdd = 0;
        nAddr = 0;
        nCpu = 0;
        nZone = 0;
        nFun = 0;
        nInf = 0;
        nRii = 0;
    }
} PRO_FRAME_BODY;

typedef std::vector<PRO_FRAME_BODY> PRO_FRAME_BODY_LIST;

// 文件属性信息结构体
typedef struct _FILE_PROPERTY_INF
{
    std::string strFileName;    // 文件名
    u_int32     nFileSize;      // 文件大小
    u_int32     nCreateTime;    // 创建时间
    u_int32     nModifyTime;    // 修改时间
    
    _FILE_PROPERTY_INF()
    {
        nFileSize = 0;
        nCreateTime = 0;
        nModifyTime = 0;
    }
} FILE_PROPERTY_INF;

typedef std::vector<FILE_PROPERTY_INF> FILE_PROPERTY_INF_LIST;

// 基本配置结构体
typedef struct _BASIC_CFG_TB
{
    std::string str_file_path;  // 文件路径
    
    _BASIC_CFG_TB()
    {
        str_file_path = "/tmp/test_files";
    }
} BASIC_CFG_TB;

// 变电站配置结构体
typedef struct _SUBSTATION_TB
{
    u_int16 n_outaddr103;       // 103规约输出地址
    
    _SUBSTATION_TB()
    {
        n_outaddr103 = 1;
    }
} SUBSTATION_TB;

// IED配置结构体
typedef struct _IED_TB
{
    u_int16 n_outaddr103;       // 103规约输出地址
    
    _IED_TB()
    {
        n_outaddr103 = 1;
    }
} IED_TB;

// NX事件消息子字段
typedef struct _NX_EVENT_MSG_SUBFIELD
{
    char c_field_name[256];     // 字段名
    
    _NX_EVENT_MSG_SUBFIELD()
    {
        memset(c_field_name, 0, sizeof(c_field_name));
    }
} NX_EVENT_MSG_SUBFIELD;

typedef std::vector<NX_EVENT_MSG_SUBFIELD> NX_EVENT_MSG_SUBFILED_LIST;

// NX事件消息结构体
typedef struct _NX_EVENT_MESSAGE
{
    u_int32 n_msg_type;         // 消息类型
    u_int32 n_event_obj;        // 事件对象(IED编号)
    u_int32 n_send_utctm;       // 发送UTC时间
    NX_EVENT_MSG_SUBFILED_LIST list_subfields; // 子字段列表
    
    _NX_EVENT_MESSAGE()
    {
        n_msg_type = 0;
        n_event_obj = 0;
        n_send_utctm = 0;
    }
} NX_EVENT_MESSAGE;

// ASDU106信息结构体
typedef struct _ASDU106_INFO
{
    ASDU_ADDR         Addr;             // 地址信息
    ASDU_INFO_OBJ     InfoObj;          // 信息体
    u_int8            nFileType;        // 文件类型（1-定值文件）
    ASDU_TIME         InfoTime;         // 时间
    std::string       strWavFileName;   // 文件名
    std::string       strReserve;       // 备用字符
    
    _ASDU106_INFO()
    {
        nFileType = 1; // 默认为定值文件
    }
} ASDU106_INFO;

// 日志记录类Mock
class MockLogRecord
{
public:
    MOCK_METHOD(void, RecordTraceLog, (const char* msg, const char* className), ());
    MOCK_METHOD(void, RecordErrorLog, (const char* msg, const char* className), ());
};

// 模型查询接口Mock
class MockModelSeek
{
public:
    MOCK_METHOD(bool, GetBasicCfg, (BASIC_CFG_TB& cfg), ());
    MOCK_METHOD(const SUBSTATION_TB*, GetSubStationBasicCfg, (), ());
    MOCK_METHOD(const IED_TB*, GetIedBasicCfg, (u_int32 iedId), ());
};

// 时间转换类Mock
class MockTimeConvert
{
public:
    MockTimeConvert(u_int32 utc, u_int16 ms) : m_utc(utc), m_ms(ms) {}
    
    MOCK_METHOD(void, GetCP56TIMe, (std::string& timeStr), ());
    
private:
    u_int32 m_utc;
    u_int16 m_ms;
};

// 测试工具函数
class TestUtils
{
public:
    // 创建测试用的PRO_FRAME_BODY
    static PRO_FRAME_BODY CreateTestFrameBody(u_int16 addr = 1, const std::string& fileName = "test.txt");
    
    // 创建测试用的NX_EVENT_MESSAGE
    static NX_EVENT_MESSAGE CreateTestEventMessage(u_int32 msgType = NX_IED_EVENT_FILE_REPORT, 
                                                   u_int32 iedId = 1, 
                                                   const std::string& fileName = "test.txt");
    
    // 创建测试用的ASDU106_INFO
    static ASDU106_INFO CreateTestAsdu106Info(const std::string& fileName = "test.txt");
    
    // 验证PRO_FRAME_BODY的基本字段
    static void VerifyFrameBodyBasics(const PRO_FRAME_BODY& body, u_int8 expectedType);
    
    // 创建测试文件目录结构
    static void CreateTestFileStructure(const std::string& basePath);
    
    // 清理测试文件
    static void CleanupTestFiles(const std::string& basePath);
};

// 字符串工具函数
inline char* _strstr_nocase(char* str1, const char* str2)
{
    if (!str1 || !str2) return nullptr;
    
    std::string s1(str1);
    std::string s2(str2);
    
    // 转换为小写进行比较
    std::transform(s1.begin(), s1.end(), s1.begin(), ::tolower);
    std::transform(s2.begin(), s2.end(), s2.begin(), ::tolower);
    
    size_t pos = s1.find(s2);
    if (pos != std::string::npos) {
        return str1 + pos;
    }
    return nullptr;
}

// 文件系统工具函数
inline int sy_format_file_path(const char* path, char* formatted_path)
{
    if (!path || !formatted_path) return -1;
    strcpy(formatted_path, path);
    return 0;
}

inline int sy_get_file_property(const char* filename, FILE_PROPERTY_INF* fileInfo)
{
    if (!filename || !fileInfo) return -1;
    
    // 模拟文件存在
    fileInfo->strFileName = filename;
    fileInfo->nFileSize = 1024;
    fileInfo->nCreateTime = 1640995200; // 2022-01-01 00:00:00
    fileInfo->nModifyTime = 1640995200;
    
    return 0;
}

inline int sy_get_file_name(const char* fullPath, char* path, char* name, char* ext)
{
    if (!fullPath) return -1;
    
    std::string full(fullPath);
    size_t lastSlash = full.find_last_of('/');
    size_t lastDot = full.find_last_of('.');
    
    if (path) {
        if (lastSlash != std::string::npos) {
            strcpy(path, full.substr(0, lastSlash).c_str());
        } else {
            strcpy(path, "");
        }
    }
    
    if (name && ext) {
        std::string filename = (lastSlash != std::string::npos) ? 
                              full.substr(lastSlash + 1) : full;
        
        if (lastDot != std::string::npos && lastDot > lastSlash) {
            strcpy(name, filename.substr(0, lastDot - (lastSlash + 1)).c_str());
            strcpy(ext, filename.substr(lastDot + 1).c_str());
        } else {
            strcpy(name, filename.c_str());
            strcpy(ext, "");
        }
    }
    
    return 0;
}
