#include <gtest/gtest.h>
#include <iostream>

int main(int argc, char** argv)
{
    // 初始化Google Test框架
    ::testing::InitGoogleTest(&argc, argv);
    
    std::cout << "=== 开始运行NX EC Pro GW104单元测试 ===" << std::endl;
    std::cout << "测试范围：" << std::endl;
    std::cout << "1. TNXEcProAsdu101GWS - DirectResFromLocal和__QueryGeneralFilesList_SettingUp方法" << std::endl;
    std::cout << "2. TNXEcProAsdu103GWS - DirectResFromLocal和_GeneralFileHandle_SettingUp方法" << std::endl;
    std::cout << "3. TNXEcProAsdu106GWS - ConvertEventMsgToPro、CvtEventFileReport和FormatAsdu106Body方法" << std::endl;
    std::cout << "=========================================" << std::endl;
    
    // 运行所有测试
    int result = RUN_ALL_TESTS();
    
    if (result == 0) {
        std::cout << "=== 所有测试通过！ ===" << std::endl;
    } else {
        std::cout << "=== 部分测试失败，请检查输出 ===" << std::endl;
    }
    
    return result;
}
