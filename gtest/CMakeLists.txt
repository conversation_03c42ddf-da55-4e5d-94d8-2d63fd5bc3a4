cmake_minimum_required(VERSION 4.0)

set_property(GLOBAL PROPERTY RULE_LAUNCH_COMPILE ccache)
set_property(GLOBAL PROPERTY RULE_LAUNCH_LINK ccache)

project(gtest)

set(HOME ${PROJECT_SOURCE_DIR})

function(include_sub_directories_recursively root_dir)
    if (IS_DIRECTORY ${root_dir})
        include_directories(${root_dir})
    else()
        message(WARNING "Directory not found: ${root_dir}")
    endif()

    file(GLOB ALL_SUB RELATIVE ${root_dir} "${root_dir}/*")
    list(SORT ALL_SUB)

    foreach(sub ${ALL_SUB})
        if (IS_DIRECTORY "${root_dir}/${sub}")
            include_sub_directories_recursively("${root_dir}/${sub}")
        endif()
    endforeach()
endfunction()

include_sub_directories_recursively(${CMAKE_SOURCE_DIR}/include)

file(GLOB_RECURSE SRC "${CMAKE_CURRENT_SOURCE_DIR}/src/*.cpp")

set(EXECUTABLE_OUTPUT_PATH ${HOME}/bin)

add_executable(${PROJECT_NAME} ${SRC})
find_package(GTest REQUIRED)

target_link_libraries(${PROJECT_NAME} 
    gtest::gtest
)
