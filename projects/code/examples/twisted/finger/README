The Finger protocol, RFC 1288.

"Finger is based on the Transmission Control Protocol, using TCP port
79 decimal (117 octal).  The local host opens a TCP connection to a
remote host on the Finger port.  An RUIP becomes available on the
remote end of the connection to process the request.  The local host
sends the RUIP a one line query based upon the Finger query
specification, and waits for the RUIP to respond.  The RUIP receives
and processes the query, returns an answer, then initiates the close
of the connection.  The local host receives the answer and the close
signal, then proceeds closing its end of the connection."

