#pragma once

#include <taskflow/taskflow.hpp>
#include <taskflow/algorithm/for_each.hpp>
#include <memory>
#include <functional>
#include <string>
#include <atomic>
#include <vector>
#include "fmtlog/fmtlog.h"
#include "zexuan.hpp"

namespace zexuan {
namespace core {

/**
 * @brief Enhanced base class for taskflow-based task management
 * 
 * This class provides a foundation for building task workflows using taskflow.
 * Derived classes should implement specific task logic.
 */
template<typename ItemType>
class BaseTaskflow {
public:
    explicit BaseTaskflow(tf::Executor& executor) 
        : executor_(executor)
        , success_count_(0)
        , error_count_(0) {}
    
    virtual ~BaseTaskflow() = default;

    // Prevent copying
    BaseTaskflow(const BaseTaskflow&) = delete;
    BaseTaskflow& operator=(const BaseTaskflow&) = delete;

    /**
     * @brief Run the workflow
     */
    void run() {
        std::string thread_id = zexuan::get_thread_id_str();
        logi("[主线程 {}] 开始执行", thread_id);

        auto items = get_items_to_process();
        if (items.empty()) {
            logi("[主线程 {}] 没有需要处理的{}", 
                thread_id, get_item_type_name());
            return;
        }

        // 清除之前的任务（如果有）
        taskflow_.clear();
        
        // 构建任务流
        build_taskflow(items);
        
        logi("[主线程 {}] 开始执行任务流", thread_id);
        executor_.run(taskflow_).wait();
        
        print_summary();
    }

protected:
    /**
     * @brief Create a new task in the workflow
     */
    tf::Task create_task(const std::string& name, std::function<void()> callback) {
        return taskflow_.emplace([callback, name](tf::Subflow& subflow) {
            callback();
        }).name(name);
    }

    // 默认实现使用 for_each
    virtual void build_taskflow(const std::vector<ItemType>& items) {
        taskflow_.for_each(
            items.begin(), 
            items.end(), 
            [this] (const ItemType& item) {
                process_item(item);
            }
        ).name("处理" + get_item_type_name());
    }

    // 子类需要实现的接口
    virtual std::vector<ItemType> get_items_to_process() = 0;
    virtual void process_item(const ItemType& item) = 0;
    virtual std::string get_item_type_name() const = 0;

    virtual void print_summary() {
        std::string thread_id = zexuan::get_thread_id_str();
        logi("[主线程 {}] 操作完成:", thread_id);
        logi("[主线程 {}] 成功处理: {} 个{}", 
            thread_id, success_count_.load(), get_item_type_name());
        if (error_count_ > 0) {
            loge("[主线程 {}] 处理失败: {} 个{}", 
                thread_id, error_count_.load(), get_item_type_name());
        }
    }

    // 让子类可以访问这些计数器
    std::atomic<int> success_count_;
    std::atomic<int> error_count_;
    tf::Executor& executor_;
    tf::Taskflow taskflow_;
};

} // namespace core
} // namespace zexuan 