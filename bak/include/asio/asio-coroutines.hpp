#pragma once

#include "asio.hpp"
#include <boost/asio/co_spawn.hpp>
#include <boost/asio/detached.hpp>
#include <boost/asio/experimental/awaitable_operators.hpp>
#include <boost/asio/use_awaitable.hpp>
#include <boost/asio/redirect_error.hpp>
#include <boost/system/error_code.hpp>
#include <coroutine>
#include <exception>
#include <functional>
#include <future>
#include <memory>
#include <type_traits>

namespace zexuan {

using boost::asio::awaitable;
using boost::asio::co_spawn;
using boost::asio::detached;
using boost::asio::use_awaitable;
using boost::asio::experimental::awaitable_operators::operator&&;
using boost::asio::experimental::awaitable_operators::operator||;

namespace this_coro = boost::asio::this_coro;

/**
 * @brief 协程工具类，提供协程相关的辅助功能
 */
class AsioCoroutines {
public:
    /**
     * @brief 在线程池中启动一个协程
     * @tparam F 协程函数类型
     * @param f 协程函数
     * @return std::future 用于等待协程完成
     */
    template<typename F>
    static auto spawn(F&& f) {
        using ReturnType = typename std::invoke_result_t<F>::value_type;
        auto promise = std::make_shared<std::promise<ReturnType>>();
        auto future = promise->get_future();

        co_spawn(getAsioPool().getContext(), 
            [f = std::forward<F>(f), promise]() mutable -> awaitable<void> {
                try {
                    if constexpr (std::is_void_v<ReturnType>) {
                        co_await f();
                        promise->set_value();
                    } else {
                        promise->set_value(co_await f());
                    }
                } catch (...) {
                    promise->set_exception(std::current_exception());
                }
            }, detached);

        return future;
    }

    /**
     * @brief 创建一个延迟执行的协程任务
     * @tparam F 协程函数类型
     * @param delay 延迟时间
     * @param f 协程函数
     * @return std::future 用于等待协程完成
     */
    template<typename Rep, typename Period, typename F>
    static auto spawnAfter(const std::chrono::duration<Rep, Period>& delay, F&& f) {
        using ReturnType = typename std::invoke_result_t<F>::value_type;
        auto promise = std::make_shared<std::promise<ReturnType>>();
        auto future = promise->get_future();

        co_spawn(getAsioPool().getContext(),
            [delay, f = std::forward<F>(f), promise]() mutable -> awaitable<void> {
                try {
                    boost::asio::steady_timer timer(co_await this_coro::executor);
                    timer.expires_after(delay);
                    co_await timer.async_wait(use_awaitable);

                    if constexpr (std::is_void_v<ReturnType>) {
                        co_await f();
                        promise->set_value();
                    } else {
                        promise->set_value(co_await f());
                    }
                } catch (...) {
                    promise->set_exception(std::current_exception());
                }
            }, detached);

        return future;
    }

    /**
     * @brief 创建一个周期性执行的协程任务
     * @tparam F 协程函数类型
     * @param interval 执行间隔
     * @param f 协程函数
     * @return std::shared_ptr<bool> 用于控制任务停止
     */
    template<typename Rep, typename Period, typename F>
    static auto spawnEvery(const std::chrono::duration<Rep, Period>& interval, F&& f) {
        auto running = std::make_shared<bool>(true);

        co_spawn(getAsioPool().getContext(),
            [interval, f = std::forward<F>(f), running]() mutable -> awaitable<void> {
                try {
                    boost::asio::steady_timer timer(co_await this_coro::executor);
                    while (*running) {
                        timer.expires_after(interval);
                        co_await timer.async_wait(use_awaitable);
                        if (*running) {
                            co_await f();
                        }
                    }
                } catch (...) {
                    // 处理异常
                }
            }, detached);

        return running;
    }

    /**
     * @brief 等待多个协程完成
     * @tparam Awaitables 协程类型
     * @param aws 协程对象
     * @return awaitable<std::tuple<...>> 所有协程的结果
     */
    template<typename... Awaitables>
    static auto whenAll(Awaitables&&... aws) {
        return (std::forward<Awaitables>(aws) && ...);
    }

    /**
     * @brief 等待任意一个协程完成
     * @tparam Awaitables 协程类型
     * @param aws 协程对象
     * @return awaitable<std::variant<...>> 第一个完成的协程的结果
     */
    template<typename... Awaitables>
    static auto whenAny(Awaitables&&... aws) {
        return (std::forward<Awaitables>(aws) || ...);
    }

    /**
     * @brief 创建一个超时协程
     * @tparam Rep 时间表示类型
     * @tparam Period 时间周期类型
     * @return awaitable<void> 超时协程
     */
    template<typename Rep, typename Period>
    static auto timeout(const std::chrono::duration<Rep, Period>& duration) {
        return [duration]() -> awaitable<void> {
            boost::asio::steady_timer timer(co_await this_coro::executor);
            timer.expires_after(duration);
            co_await timer.async_wait(use_awaitable);
        };
    }

private:
    // 禁止实例化
    AsioCoroutines() = delete;
};

} // namespace zexuan
