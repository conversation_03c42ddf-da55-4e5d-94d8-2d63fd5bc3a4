#pragma once

#include "singleton.hpp"
#include "thread_pool.hpp"
#include <boost/asio.hpp>
#include <memory>
#include <functional>
#include <future>
#include <chrono>
#include <type_traits>
#include <vector>

namespace zexuan {

/**
 * @brief ASIO线程池封装，提供异步IO和定时任务支持
 * 使用单例模式确保全局唯一实例
 */
class AsioThreadPool : public singleton<AsioThreadPool> {
public:
    using io_context = boost::asio::io_context;
    using work_guard = boost::asio::executor_work_guard<io_context::executor_type>;
    using error_code = boost::system::error_code;
    using thread_pool_type = thread_pool<tp::none>;  // 使用无特性的线程池
    
    /**
     * @brief 获取IO上下文
     * @return io_context& IO上下文的引用
     */
    [[nodiscard]] io_context& getContext() noexcept { 
        return *io_context_; 
    }

    /**
     * @brief 提交异步任务
     * @param f 要执行的函数
     * @param args 函数参数
     * @return std::future 用于获取任务结果
     */
    template<typename F, typename... Args>
    [[nodiscard]] auto post(F&& f, Args&&... args) 
        -> std::future<std::invoke_result_t<F, Args...>> 
    {
        using ReturnType = std::invoke_result_t<F, Args...>;
        auto task = std::make_shared<std::packaged_task<ReturnType()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );
        
        auto future = task->get_future();
        
        boost::asio::post(getContext(),
            [task]() {
                (*task)();
            });
            
        return future;
    }

    /**
     * @brief 提交定时任务
     * @param delay 延迟时间
     * @param f 要执行的函数
     * @param args 函数参数
     * @return std::future 用于获取任务结果
     */
    template<typename Rep, typename Period, typename F, typename... Args>
    [[nodiscard]] auto postAfter(const std::chrono::duration<Rep, Period>& delay, F&& f, Args&&... args) 
        -> std::future<std::invoke_result_t<F, Args...>>
    {
        using ReturnType = std::invoke_result_t<F, Args...>;
        auto task = std::make_shared<std::packaged_task<ReturnType()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );
        
        auto future = task->get_future();
        auto timer = std::make_shared<boost::asio::steady_timer>(
            getContext(), delay
        );
        
        timer->async_wait([task, timer](const error_code& ec) {
            if (!ec) {
                (*task)();
            }
        });
        
        return future;
    }

    /**
     * @brief 提交周期性任务
     * @param interval 任务执行间隔
     * @param f 要执行的函数
     * @param args 函数参数
     * @return std::shared_ptr<boost::asio::steady_timer> 定时器指针，可用于取消任务
     */
    template<typename Rep, typename Period, typename F, typename... Args>
    [[nodiscard]] std::shared_ptr<boost::asio::steady_timer> 
    postEvery(const std::chrono::duration<Rep, Period>& interval, F&& f, Args&&... args)
    {
        auto timer = std::make_shared<boost::asio::steady_timer>(getContext());
        auto task = std::make_shared<std::function<void()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );

        std::weak_ptr<boost::asio::steady_timer> weak_timer = timer;
        
        std::function<void(const error_code&)> handler = 
            [task, interval, weak_timer, handler](const error_code& ec) mutable {
                if (ec) return;
                
                (*task)();
                
                if (auto timer = weak_timer.lock()) {
                    timer->expires_after(interval);
                    timer->async_wait(handler);
                }
            };

        timer->expires_after(interval);
        timer->async_wait(handler);
        
        return timer;
    }

    /**
     * @brief 取消定时器任务
     * @param timer 要取消的定时器
     */
    void cancelTimer(const std::shared_ptr<boost::asio::steady_timer>& timer) {
        if (timer) {
            error_code ec;
            timer->cancel(ec);
        }
    }

    /**
     * @brief 停止线程池
     */
    void stop() noexcept {
        if (running_.exchange(false)) {
            work_guard_.reset();
            io_context_->stop();
            if (thread_pool_) {
                thread_pool_->wait();
            }
        }
    }

    /**
     * @brief 检查线程池是否正在运行
     * @return bool 是否正在运行
     */
    [[nodiscard]] bool isRunning() const noexcept {
        return running_;
    }

private:
    AsioThreadPool() 
        : io_context_(std::make_unique<io_context>()),
          work_guard_(boost::asio::make_work_guard(*io_context_)),
          thread_pool_(std::make_unique<thread_pool_type>(std::thread::hardware_concurrency())),
          running_(true)
    {
        // 启动工作线程并保存futures以确保任务完成
        worker_futures_.reserve(std::thread::hardware_concurrency());
        for (size_t i = 0; i < std::thread::hardware_concurrency(); ++i) {
            worker_futures_.push_back(
                thread_pool_->submit_task([this] {
                    error_code ec;
                    io_context_->run(ec);
                })
            );
        }
    }

    ~AsioThreadPool() {
        stop();
        // 等待所有工作线程完成
        for (auto& future : worker_futures_) {
            future.wait();
        }
    }

private:
    std::unique_ptr<io_context> io_context_;
    work_guard work_guard_;
    std::unique_ptr<thread_pool_type> thread_pool_;
    std::atomic<bool> running_;
    std::vector<std::future<void>> worker_futures_;  // 存储工作线程的futures
};

/**
 * @brief 获取全局AsioThreadPool实例
 * @return AsioThreadPool& 线程池实例的引用
 */
inline AsioThreadPool& getAsioPool() {
    return AsioThreadPool::getInstance();
}

} // namespace zexuan
