#pragma once

#include "asio-coroutines.hpp"
#include <boost/asio/ip/tcp.hpp>
#include <string>
#include <memory>
#include <functional>

namespace zexuan {

using boost::asio::ip::tcp;

class TcpServerCoro {
public:
    using MessageHandler = std::function<awaitable<void>(const std::string&)>;

    explicit TcpServerCoro(uint16_t port, 
        MessageHandler handler = nullptr)
        : port_(port)
        , handler_(std::move(handler))
    {
        if (!handler_) {
            // 默认的 echo 处理函数
            handler_ = [](const std::string& msg) -> awaitable<void> {
                std::cout << "Received: " << msg << std::endl;
                co_return;
            };
        }
    }

    // 启动服务器
    std::future<void> start() {
        return AsioCoroutines::spawn([this]() -> awaitable<void> {
            auto executor = co_await this_coro::executor;
            tcp::acceptor acceptor(executor, {tcp::v4(), port_});
            
            std::cout << "Coroutine Server started on port " << port_ << std::endl;

            while(true) {
                try {
                    // 接受新连接
                    tcp::socket socket = co_await acceptor.async_accept(use_awaitable);
                    auto endpoint = socket.remote_endpoint();
                    std::cout << "New connection from: " 
                             << endpoint.address().to_string() 
                             << ":" << endpoint.port() << std::endl;
                    
                    // 为每个连接启动一个处理协程
                    AsioCoroutines::spawn([this, socket = std::move(socket), 
                        endpoint]() mutable {
                        return handle_connection(std::move(socket), endpoint);
                    });
                } catch (const std::exception& e) {
                    std::cerr << "Accept error: " << e.what() << std::endl;
                }
            }
        });
    }

private:
    // 处理单个连接
    awaitable<void> handle_connection(tcp::socket socket, 
        const tcp::endpoint& endpoint) {
        try {
            char data[1024];
            for (;;) {
                // 读取数据
                std::size_t n = co_await socket.async_read_some(
                    boost::asio::buffer(data), use_awaitable);
                
                if (n == 0) {
                    std::cout << "Client " << endpoint.address().to_string() 
                             << ":" << endpoint.port() << " closed connection" 
                             << std::endl;
                    break;
                }

                // 调用处理函数
                co_await handler_(std::string(data, n));

                // 回显数据
                co_await async_write(socket, 
                    boost::asio::buffer(data, n), 
                    use_awaitable);
            }
        } catch (const boost::system::system_error& e) {
            // 处理常见的网络错误
            if (e.code() == boost::asio::error::eof ||
                e.code() == boost::asio::error::connection_reset) {
                std::cout << "Client " << endpoint.address().to_string() 
                         << ":" << endpoint.port() << " disconnected" 
                         << std::endl;
            } else {
                std::cerr << "Connection error with " 
                         << endpoint.address().to_string()
                         << ":" << endpoint.port() 
                         << " - " << e.what() << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "Error handling connection from "
                     << endpoint.address().to_string()
                     << ":" << endpoint.port()
                     << " - " << e.what() << std::endl;
        }

        // 确保连接关闭
        try {
            socket.shutdown(tcp::socket::shutdown_both);
            socket.close();
        } catch (...) {}
    }

    uint16_t port_;
    MessageHandler handler_;
};

} // namespace zexuan
