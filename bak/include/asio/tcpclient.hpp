#ifndef ZEXUAN_NETWORK_TCPCLIENT_HPP
#define ZEXUAN_NETWORK_TCPCLIENT_HPP

#include <boost/asio.hpp>
#include <deque>
#include <string>
#include <functional>

using boost::asio::ip::tcp;

class TcpClient {
public:
    // 定义消息处理回调函数类型
    using MessageHandler = std::function<void(const std::string&)>;

    explicit TcpClient(boost::asio::io_context& io_context);

    void connect(const std::string& host, const std::string& port);
    void write(const std::string& msg);
    void close();

    // 设置消息处理回调
    void set_message_handler(MessageHandler handler) {
        message_handler_ = std::move(handler);
    }

private:
    void do_connect(const tcp::resolver::results_type& endpoints);
    void do_read();
    void do_write();

    boost::asio::io_context& io_context_;
    tcp::socket socket_;
    tcp::resolver resolver_;
    std::vector<char> read_msg_;
    std::deque<std::string> write_msgs_;
    MessageHandler message_handler_;  // 消息处理回调
};

#endif // ZEXUAN_NETWORK_TCPCLIENT_HPP
