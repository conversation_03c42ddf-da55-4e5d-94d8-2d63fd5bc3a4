#ifndef ZEXUAN_NETWORK_TCPSERVER_HPP
#define ZEXUAN_NETWORK_TCPSERVER_HPP

#include <boost/asio.hpp>
#include <memory>
#include <vector>

using boost::asio::ip::tcp;

class TcpSession : public std::enable_shared_from_this<TcpSession> {
public:
    TcpSession(tcp::socket socket);
    void start();

private:
    void do_read();
    void do_write(std::size_t length);

    tcp::socket socket_;
    enum { max_length = 1024 };
    std::vector<char> data_;
};

class TcpServer {
public:
    TcpServer(boost::asio::io_context& io_context, short port);

private:
    void do_accept();

    tcp::acceptor acceptor_;
};

#endif // ZEXUAN_NETWORK_TCPSERVER_HPP
