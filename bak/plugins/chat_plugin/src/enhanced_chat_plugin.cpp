#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/plugin/plugin_communication.hpp"
#include <vector>
#include <sstream>
#include <algorithm>

/**
 * 使用增强基类的Chat插件 - 大幅简化代码
 */
class ChatPlugin : public zexuan::plugin::PluginBase<ChatPlugin> {
public:
    ChatPlugin() : PluginBase("zexuan.plugin.chat") {}

    // === 基础插件信息 ===
    const char* getName() const override { return "ChatPlugin"; }
    const char* getVersion() const override { return "2.0.0"; }
    const char* getDescription() const override { return "Enhanced Chat Plugin - Inter-plugin communication demo"; }

    // === 移除了processMessage方法 ===
    // 所有消息处理现在通过事件系统进行

protected:
    // === 基类虚函数实现 ===
    
    bool onInitialize() override {
        // 初始化用户列表
        activeUsers_.clear();
        return true;
    }

    void onInitializationComplete() override {
        // 发送加入消息
        zexuan::plugin::publishMessage("Chat<PERSON><PERSON><PERSON> has joined the conversation!", getName());
    }

    void onShutdown() override {
        // 发送离开消息
        if (isInitialized()) {
            zexuan::plugin::publishMessage("ChatPlugin is leaving the conversation.", getName());
        }
    }

    void setupEventSubscriptions() override {
        // 订阅消息事件
        subscribeToMessages([this](auto event) {
            handleMessageEvent(event);
        });

        // 订阅数据事件
        subscribeToData([this](auto event) {
            handleDataEvent(event);
        });

        // 订阅控制事件（基类已处理PAUSE/RESUME/RELOAD）
        subscribeToControl([this](auto event) {
            // ChatPlugin没有特殊的控制事件处理
            getLogger()->debug("Received control event: {}", static_cast<int>(event->action));
        });
    }

private:
    // === 事件处理方法 ===
    
    void handleMessageEvent(std::shared_ptr<zexuan::core::MessageEvent> event) {
        if (isPaused()) {
            getLogger()->debug("ChatPlugin is paused, ignoring message");
            return;
        }

        getLogger()->info("Received message from {}: {}", event->source, event->message);

        // 防止自己回复自己的消息
        if (event->source == getName()) {
            return;
        }

        // 如果是点对点消息（发给ChatPlugin的），处理命令
        if (!event->isBroadcast()) {
            handleChatCommand(event->message, event->source);
            return;
        }

        // 广播消息：如果消息包含 "@ChatPlugin"，则回复
        if (event->message.find("@ChatPlugin") != std::string::npos) {
            zexuan::plugin::publishMessage("ChatPlugin received your mention: " + event->message, getName(), event->source);
        }
    }
    
    void handleDataEvent(std::shared_ptr<zexuan::core::DataEvent> event) {
        getLogger()->info("Received data event: {} from {}", event->dataType, event->source);

        if (event->dataType == "user_list") {
            safeExecute("processing user list", [&]() {
                auto userList = event->getData<std::vector<std::string>>();
                getLogger()->info("Received user list with {} users", userList.size());
                activeUsers_ = userList;
            });
        } else if (event->dataType == "chat_room_info") {
            getLogger()->info("Received chat room info from {}", event->source);
        } else {
            getLogger()->debug("Ignoring unhandled data type: {}", event->dataType);
        }
    }

    // === 命令处理方法 ===

    void handleChatCommand(const std::string& message, const std::string& requester) {
        // 处理特殊命令
        if (message.find("/send ") == 0) {
            handleSendCommand(message.substr(6), requester);
        } else if (message.find("/broadcast ") == 0) {
            handleBroadcastCommand(message.substr(11), requester);
        } else if (message.find("/users") == 0) {
            handleUsersCommand(requester);
        } else {
            // 普通聊天消息，广播给所有插件
            zexuan::plugin::publishMessage("ChatPlugin says: " + message, getName());
            zexuan::plugin::publishMessage("Message broadcasted: " + message, getName(), requester);
        }
    }

    void handleSendCommand(const std::string& command, const std::string& requester) {
        size_t spacePos = command.find(' ');
        if (spacePos == std::string::npos) {
            zexuan::plugin::publishMessage("Usage: /send <plugin> <message>", getName(), requester);
            return;
        }

        std::string targetPlugin = command.substr(0, spacePos);
        std::string message = command.substr(spacePos + 1);

        zexuan::plugin::publishMessage(message, getName(), targetPlugin);
        zexuan::plugin::publishMessage("Message sent to " + targetPlugin + ": " + message, getName(), requester);
    }

    void handleBroadcastCommand(const std::string& message, const std::string& requester) {
        zexuan::plugin::publishMessage(message, getName());
        zexuan::plugin::publishMessage("Message broadcasted: " + message, getName(), requester);
    }

    void handleUsersCommand(const std::string& requester) {
        std::string response;
        if (activeUsers_.empty()) {
            response = "No active users found";
        } else {
            std::ostringstream oss;
            oss << "Active users (" << activeUsers_.size() << "): ";
            for (size_t i = 0; i < activeUsers_.size(); ++i) {
                if (i > 0) oss << ", ";
                oss << activeUsers_[i];
            }
            response = oss.str();
        }
        zexuan::plugin::publishMessage(response, getName(), requester);
    }

    // === 成员变量 ===
    std::vector<std::string> activeUsers_;
};

// === 插件导出函数 ===
extern "C" {
    ChatPlugin* create_plugin() {
        return new ChatPlugin();
    }

    void destroy_plugin(ChatPlugin* plugin) {
        delete plugin;
    }

    const char* get_plugin_info() {
        return "Enhanced Chat Plugin v2.0.0 - Simplified with base class";
    }
}
