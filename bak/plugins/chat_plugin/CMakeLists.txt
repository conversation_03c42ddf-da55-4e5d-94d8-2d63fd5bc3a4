# 聊天插件
# Chat Plugin - Using unified PluginBase
add_library(chat_plugin SHARED
    src/enhanced_chat_plugin.cpp
)

# 设置包含目录
target_include_directories(chat_plugin
    PRIVATE
        ${CMAKE_SOURCE_DIR}/interface/include
        ${CMAKE_SOURCE_DIR}/core/include
)

# 链接依赖库
target_link_libraries(chat_plugin
    PRIVATE
        plugin_interface
)

# 设置输出目录
set_target_properties(chat_plugin PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/libs/plugins
)
