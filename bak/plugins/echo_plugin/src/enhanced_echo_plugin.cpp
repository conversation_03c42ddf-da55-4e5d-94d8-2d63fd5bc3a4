#include "zexuan/plugin/plugin_base.hpp"
#include "zexuan/plugin/plugin_communication.hpp"
#include <iostream>
#include <sstream>
#include <vector>

/**
 * 使用增强基类的Echo插件 - 大幅简化代码
 */
class EchoPlugin : public zexuan::plugin::PluginBase<EchoPlugin> {
public:
    EchoPlugin() : PluginBase("zexuan.plugin.echo") {}

    // === 基础插件信息 ===
    const char* getName() const override { return "EchoPlugin"; }
    const char* getVersion() const override { return "2.0.0"; }
    const char* getDescription() const override { return "Enhanced Echo Plugin with simplified code"; }

    // === 移除了processMessage方法 ===
    // 所有消息处理现在通过事件系统进行

protected:
    // === 基类虚函数实现 ===
    
    bool onInitialize() override {
        // 重置计数器
        messageCount_ = 0;
        dataEventCount_ = 0;
        return true;
    }

    void onInitializationComplete() override {
        // 发送初始化消息
        zexuan::plugin::publishMessage("EchoPlugin is now online and ready!", getName());
    }

    void onShutdown() override {
        // 发送关闭消息
        if (isInitialized()) {
            zexuan::plugin::publishMessage("EchoPlugin is going offline.", getName());
        }
    }

    void setupEventSubscriptions() override {
        // 订阅消息事件
        subscribeToMessages([this](auto event) {
            handleMessageEvent(event);
        });

        // 订阅数据事件
        subscribeToData([this](auto event) {
            handleDataEvent(event);
        });

        // 订阅控制事件（基类已处理通用的PAUSE/RESUME/RELOAD）
        subscribeToControl([this](auto event) {
            handleCustomControlEvent(event);
        });
    }

private:
    // === 事件处理方法 ===
    
    void handleMessageEvent(std::shared_ptr<zexuan::core::MessageEvent> event) {
        if (isPaused()) {
            getLogger()->debug("EchoPlugin is paused, ignoring message");
            return;
        }

        getLogger()->info("Received message from {}: {}", event->source, event->message);

        // 防止自己回复自己的消息，避免无限循环
        if (event->source == getName()) {
            return;
        }

        messageCount_++;

        // 处理特殊命令
        if (event->message.find("/reply ") == 0) {
            handleReplyCommand(event->message.substr(7), event->source);
            return;
        }

        // 如果是点对点消息（发给EchoPlugin的），直接回显
        if (!event->isBroadcast()) {
            std::string echoMsg = "Echo: " + event->message;
            zexuan::plugin::publishMessage(echoMsg, getName(), event->source);
            return;
        }

        // 广播消息：如果消息包含 "echo" 或 "@EchoPlugin"，则回复
        if (event->message.find("echo") != std::string::npos ||
            event->message.find("@EchoPlugin") != std::string::npos) {
            zexuan::plugin::publishMessage("EchoPlugin auto-reply: " + event->message, getName(), event->source);
        }
    }
    
    void handleDataEvent(std::shared_ptr<zexuan::core::DataEvent> event) {
        getLogger()->info("Received data event: {} from {}", event->dataType, event->source);
        dataEventCount_++;
        
        if (event->dataType == "numbers") {
            safeExecute("processing numbers", [&]() {
                auto numbers = event->getData<std::vector<int>>();
                
                std::ostringstream oss;
                oss << "EchoPlugin processed " << numbers.size() << " numbers: ";
                for (size_t i = 0; i < std::min(numbers.size(), size_t(5)); ++i) {
                    if (i > 0) oss << ", ";
                    oss << numbers[i];
                }
                if (numbers.size() > 5) oss << "...";
                
                zexuan::plugin::publishMessage(oss.str(), getName());
            });
        }
    }
    
    void handleCustomControlEvent(std::shared_ptr<zexuan::core::ControlEvent> event) {
        // 处理EchoPlugin特有的控制事件
        // 通用的PAUSE/RESUME/RELOAD已由基类处理
        getLogger()->debug("Received custom control event: {}", static_cast<int>(event->action));
    }

    // === 辅助方法 ===

    void handleReplyCommand(const std::string& command, const std::string& requester) {
        size_t spacePos = command.find(' ');
        if (spacePos == std::string::npos) {
            zexuan::plugin::publishMessage("Usage: /reply <plugin> <message>", getName(), requester);
            return;
        }

        std::string targetPlugin = command.substr(0, spacePos);
        std::string message = command.substr(spacePos + 1);

        zexuan::plugin::publishMessage("EchoPlugin replies: " + message, getName(), targetPlugin);
        zexuan::plugin::publishMessage("Reply sent to " + targetPlugin, getName(), requester);
    }

    // === 成员变量 ===
    int messageCount_ = 0;
    int dataEventCount_ = 0;
};

// === 插件导出函数 ===
extern "C" {
    EchoPlugin* create_plugin() {
        return new EchoPlugin();
    }

    void destroy_plugin(EchoPlugin* plugin) {
        delete plugin;
    }

    const char* get_plugin_info() {
        return "Enhanced Echo Plugin v2.0.0 - Simplified with base class";
    }
}
