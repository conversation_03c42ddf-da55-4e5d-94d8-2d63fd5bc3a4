# Echo Plugin - Using unified PluginBase
add_library(echo_plugin SHARED
    src/enhanced_echo_plugin.cpp
)

# 设置包含目录
target_include_directories(echo_plugin
    PRIVATE
        ${CMAKE_SOURCE_DIR}/interface/include
        ${CMAKE_SOURCE_DIR}/core/include
)

# 链接依赖库 - plugin 依赖 interface
target_link_libraries(echo_plugin
    PRIVATE
        plugin_interface
)

# 设置C++标准和编译选项
set_target_properties(echo_plugin PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED ON
    PREFIX "lib"
    SUFFIX ".so"
    # 防止链接器优化掉静态初始化代码
    LINK_FLAGS "-Wl,--no-as-needed"
)

# 设置输出目录
set_target_properties(echo_plugin PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY "${CMAKE_SOURCE_DIR}/libs/plugins"
) 