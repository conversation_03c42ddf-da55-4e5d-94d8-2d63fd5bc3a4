#include "tcpserver.hpp"
#include "tcpclient.hpp"
#include "asio.hpp"
#include <iostream>
#include <thread>
#include <boost/asio.hpp>

void run_server() {
    try {
        // 从全局单例中获取 io_context。
        // AsioThreadPool 会自动创建一个线程池来运行它。
        auto& io_context = zexuan::getAsioPool().getContext();

        TcpServer server(io_context, 12345);
        std::cout << "Server started on port 12345" << std::endl;
        std::cout << "Running on " << std::thread::hardware_concurrency() << " threads." << std::endl;
        std::cout << "Press Enter to exit." << std::endl;

        // io_context 在后台线程池中运行，我们只需阻塞主线程即可。
        std::cin.get();

        // 用户按回车后，程序将退出，
        // AsioThreadPool 单例的析构函数会自动处理线程池的优雅关闭。
    } catch (std::exception& e) {
        std::cerr << "Exception in server: " << e.what() << "\\n";
    }
}

void run_client() {
    try {
        // 客户端也使用同一个全局 io_context
        auto& io_context = zexuan::getAsioPool().getContext();
        TcpClient client(io_context);
        client.connect("127.0.0.1", "12345");

        // io_context 已在后台运行，无需再为此创建线程
        std::cout << "Enter messages to send to the server. Press Ctrl+D (or Ctrl+Z on Windows) to exit." << std::endl;

        char line[1024];
        while (std::cin.getline(line, 1024))
        {
            client.write(line);
        }

        client.close();
        
        // 显式停止线程池，确保所有操作完成
        zexuan::getAsioPool().stop();

    } catch (std::exception& e) {
        std::cerr << "Exception in client: " << e.what() << "\\n";
    }
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " [server|client]" << std::endl;
        return 1;
    }

    std::string mode = argv[1];
    if (mode == "server") {
        run_server();
    } else if (mode == "client") {
        run_client();
    } else {
        std::cerr << "Unknown mode: " << mode << std::endl;
        return 1;
    }

    return 0;
}
