#include "tcpserver-coroutines.hpp"
#include "tcpclient.hpp"
#include "asio.hpp"
#include <iostream>
#include <thread>
#include <boost/asio.hpp>

// 引入必要的命名空间
using boost::asio::awaitable;
using boost::asio::use_awaitable;
using boost::asio::co_spawn;
using boost::asio::detached;

void run_server_coro() {
    try {
        // 创建协程版本的服务器
        zexuan::TcpServerCoro server(12345, 
            // 自定义消息处理函数
            [](const std::string& msg) -> awaitable<void> {
                std::cout << "Processing message: " << msg << std::endl;
                // 这里可以添加任何异步处理逻辑
                co_return;
            }
        );

        std::cout << "Starting coroutine server..." << std::endl;
        std::cout << "Running on " << std::thread::hardware_concurrency() << " threads." << std::endl;
        
        // 启动服务器并获取 future
        auto server_future = server.start();
        
        std::cout << "Press Enter to exit." << std::endl;
        std::cin.get();

        // 停止线程池，这会导致服务器停止
        zexuan::getAsioPool().stop();
        
        // 等待服务器完全停止
        try {
            server_future.wait();
        } catch (const std::exception& e) {
            std::cerr << "Server stopped: " << e.what() << std::endl;
        }
    } catch (std::exception& e) {
        std::cerr << "Exception in server: " << e.what() << std::endl;
    }
}

void run_client() {
    try {
        // 客户端使用全局 io_context
        auto& io_context = zexuan::getAsioPool().getContext();
        TcpClient client(io_context);

        // 设置自定义的消息处理函数
        client.set_message_handler([](const std::string& msg) {
            std::cout << "Server> " << msg << std::endl;
        });

        // 连接到服务器
        client.connect("127.0.0.1", "12345");

        std::cout << "Enter messages to send to the server." << std::endl;
        std::cout << "Type 'quit' to exit." << std::endl;

        std::string line;
        while (std::getline(std::cin, line)) {
            if (line == "quit") {
                break;
            }
            std::cout << "Client> " << line << std::endl;
            client.write(line);
        }

        client.close();
        zexuan::getAsioPool().stop();

    } catch (std::exception& e) {
        std::cerr << "Exception in client: " << e.what() << std::endl;
    }
}

int main(int argc, char* argv[]) {
    if (argc < 2) {
        std::cerr << "Usage: " << argv[0] << " [server|client]" << std::endl;
        return 1;
    }

    std::string mode = argv[1];
    if (mode == "server") {
        run_server_coro();  // 使用协程版本的服务器
    } else if (mode == "client") {
        run_client();
    } else {
        std::cerr << "Unknown mode: " << mode << std::endl;
        return 1;
    }

    return 0;
}
