#include "tcpclient.hpp"
#include <iostream>

TcpClient::TcpClient(boost::asio::io_context& io_context)
    : io_context_(io_context),
      socket_(io_context),
      resolver_(io_context),
      read_msg_(1024),
      message_handler_([](const std::string& msg) {
          std::cout << "Reply: " << msg << std::endl;
      }) {}

void TcpClient::connect(const std::string& host, const std::string& port) {
    auto endpoints = resolver_.resolve(host, port);
    do_connect(endpoints);
}

void TcpClient::write(const std::string& msg) {
    boost::asio::post(io_context_,
        [this, msg]() {
            bool write_in_progress = !write_msgs_.empty();
            write_msgs_.push_back(msg);
            if (!write_in_progress) {
                do_write();
            }
        });
}

void TcpClient::close() {
    boost::asio::post(io_context_, [this]() { 
        if (socket_.is_open()) {
            boost::system::error_code ec;
            socket_.shutdown(tcp::socket::shutdown_both, ec);
            socket_.close();
        }
    });
}

void TcpClient::do_connect(const tcp::resolver::results_type& endpoints) {
    boost::asio::async_connect(socket_, endpoints,
        [this](boost::system::error_code ec, tcp::endpoint endpoint) {
            if (!ec) {
                std::cout << "Connected to " << endpoint.address().to_string() 
                         << ":" << endpoint.port() << std::endl;
                do_read();
            } else {
                std::cerr << "Connect error: " << ec.message() << std::endl;
            }
        });
}

void TcpClient::do_read() {
    socket_.async_read_some(boost::asio::buffer(read_msg_),
        [this](boost::system::error_code ec, std::size_t length) {
            if (!ec) {
                // 立即处理收到的消息
                if (message_handler_) {
                    message_handler_(std::string(read_msg_.data(), length));
                }
                
                // 继续读取下一条消息
                do_read();
            } else {
                if (ec == boost::asio::error::eof) {
                    std::cout << "Server closed connection" << std::endl;
                } else if (ec == boost::asio::error::connection_reset) {
                    std::cout << "Connection reset by server" << std::endl;
                } else {
                    std::cerr << "Read error: " << ec.message() << std::endl;
                }
                socket_.close();
            }
        });
}

void TcpClient::do_write() {
    boost::asio::async_write(socket_,
        boost::asio::buffer(write_msgs_.front()),
        [this](boost::system::error_code ec, std::size_t /*length*/) {
            if (!ec) {
                write_msgs_.pop_front();
                if (!write_msgs_.empty()) {
                    do_write();
                }
            } else {
                std::cerr << "Write error: " << ec.message() << std::endl;
                socket_.close();
            }
        });
}
